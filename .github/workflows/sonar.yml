name: Quality Analysis

on:
  workflow_call:
    secrets:
      SONAR_TOKEN:
        required: true
      SONAR_HOST_URL:
        required: true

jobs:
  sonar:
    name: Sonar Analysis
    runs-on: ubuntu-latest

    steps:
      - name: Downloading Source Code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Desabilita clones rasos para melhor relevância da análise

      - name: Set up JDK 11 for SonarQube
        uses: actions/setup-java@v3
        with:
          java-version: '11'  # SonarQube recomenda o uso do JDK 11
          distribution: 'temurin'  # AdoptOpenJDK é agora Eclipse Temurin

      - name: Cache SonarQube packages
        uses: actions/cache@v3
        with:
          path: |
            ~/.sonar/cache
          key: ${{ runner.os }}-sonar
          restore-keys: ${{ runner.os }}-sonar

      - name: Downloading Reports (Optional)
        if: always()  # Garante que esta etapa sempre será executada, mesmo se etapas anteriores falharem
        uses: actions/download-artifact@v4
        with:
          name: reports
          path: .reports

      - name: Running Sonar Scan
        uses: sonarsource/sonarqube-scan-action@master
        with:
          args: >
            -Dsonar.projectKey=my_project_key
            -Dsonar.qualitygate.wait=true
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}