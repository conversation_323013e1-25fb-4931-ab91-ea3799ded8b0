name: Python CI Pipeline

on:
  workflow_call:

jobs:
  tests:
    name: Python Code Quality and Unit Tests
    runs-on: ubuntu-latest
    steps:
      - name: Setup Workspace for Reports
        run: mkdir -p .reports

      - name: Checkout Code
        uses: actions/checkout@v4
        with:
          fetch-depth: 1 # Reduzindo para apenas o último commit para economizar tempo

      - name: Setup Python Environment
        uses: actions/setup-python@v4
        with:
          python-version: "3.11.13"
          cache: "pip"
          cache-dependency-path: "**/requirements.txt" # Especificando o path para melhor caching

      - name: Upgrade pip and build tools
        run: |
          python -m pip install --upgrade pip setuptools wheel

      - name: Running Code Conventions with Ruff
        run: |
          make ruff-code-convention

      - name: Cache Python Packages
        uses: actions/cache@v3
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
          restore-keys: |
            ${{ runner.os }}-pip-

      - name: Install System Dependencies
        run: |
          sudo apt-get update -y
          sudo apt-get install libxml2-dev libxslt1-dev libjpeg-dev zlib1g-dev -y # Adicionando -y para evitar interações

      - name: Setup Dev Environment
        run: |
          make setup-dev

      - name: Run Unit Tests and Coverage
        env:
          SUSPEND_SIGNALS: True
          ENVIRONMENT_TEST: True
        run: |
          make test-cov

      - name: Upload Test Reports
        uses: actions/upload-artifact@v4
        if: always() # Garante que os relatórios sejam carregados mesmo se os passos anteriores falharem
        with:
          name: reports
          path: .reports/
          include-hidden-files: true