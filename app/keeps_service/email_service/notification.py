from typing import Sequence

from config.celery import celery
from config.default import Config as settings
from keeps_service.email_service.builder import Email
from keeps_service.email_service.di import Container


def get_container():
    return Container()


@celery.task()
def notify_users(
    email_data,
    message_key: str,
    users_receivers: Sequence[dict],
    application_id: str = settings.APPLICATION_ID,
    **kwargs,
):
    """
    Send emails for users:

    :param email_data: dict,
    :param message_key: str,
    :param users_receivers: [{"email": "user_email", "language": "pt-BR", "email_verified": bool}],
    :param application_id: str
    """
    data = email_data.copy()
    message_mappers = {str(settings.APPLICATION_ID): analytics_msg_send}
    messages = []
    print(f"start task to notify users by e-mail: {users_receivers}")
    users_receivers = list(filter(lambda _user: _user.get("email_verified"), users_receivers))

    for user in users_receivers:
        language = _get_user_language(user)
        message = message_mappers[application_id](
            data=data, language=language, message_key=message_key, receivers=[user.get("email")], **kwargs
        )
        messages.append(message)

    return {"email_data": email_data, "messages": messages}


def _get_user_language(user: dict) -> str:
    language_account = user.get("language") if user.get("language") else "en"
    # TODO: Delete this when account language code has been normalized
    language_country = language_account.split("-")[1].upper() if len(language_account.split("-")) > 1 else None
    language = (
        f'{language_account.split("-")[0]}-{language_country}' if language_country is not None else language_account
    )
    return language


def analytics_msg_send(data: dict, language: str, message_key: str, receivers: list, **kwargs):
    subject = f"analytics_{message_key}_subject"
    template = f"analytics_{message_key}.html"
    email = Email(
        data=data,
        subject=subject,
        language=language,
        receivers=receivers,
        template_name=template,
        footer_name="email_footer_v1.html",
    )
    container = get_container()
    message, subject = container.builder().build_message(email)
    container.aws_email_client().sender(message=message, email_receivers=receivers, subject=subject)

    return message
