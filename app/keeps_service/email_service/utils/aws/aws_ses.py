import boto3


# pylint: disable=R0903
class AmazonEmailService:
    def __init__(self, aws_ses_key: str, aws_ses_access_key: str, aws_ses_region: str, mail_sender: str):
        self.client = boto3.client(
            "ses", region_name=aws_ses_region, aws_access_key_id=aws_ses_key, aws_secret_access_key=aws_ses_access_key
        )

        self.mail_sender = mail_sender

    def sender(self, email_receivers, subject, message):
        charset = "UTF-8"
        self.client.send_email(
            Destination={"ToAddresses": email_receivers},
            Message={
                "Body": {
                    "Html": {"Charset": charset, "Data": message},
                    "Text": {"Charset": charset, "Data": message},
                },
                "Subject": {
                    "Charset": charset,
                    "Data": subject,
                },
            },
            Source=self.mail_sender,
        )
