import os

import injector
from config.default import Config as settings
from keeps_service.email_service import Builder
from keeps_service.email_service.custom import SlackClient
from keeps_service.email_service.utils.aws import AmazonEmailService

TEMPLATES_EMAIL_DIR = os.path.join(os.path.abspath(os.path.dirname(__file__)), 'assets/tpl_emails')


class Container:
    def __init__(self):
        self.__slack_client = SlackClient(settings.SLACK_LOG_CHANNEL_WEBHOOK)

        self.__builder = Builder(template_dir=TEMPLATES_EMAIL_DIR)

        self.__aws_email_client = AmazonEmailService(
            aws_ses_key=settings.AWS_S3_ACCESS_KEY_ID,
            aws_ses_access_key=settings.AWS_S3_SECRET_ACCESS_KEY,
            aws_ses_region=settings.AWS_S3_REGION_NAME,
            mail_sender=settings.AWS_MAIL_SENDER
        )

    def builder(self):
        return self.__builder

    def aws_email_client(self):
        return self.__aws_email_client

    def slack_client(self):
        return self.__slack_client

    def configure(self, binder):
        binder.bind(AmazonEmailService, to=injector.CallableProvider(self.aws_email_client))
        binder.bind(SlackClient, to=injector.CallableProvider(self.slack_client))
        binder.bind(Builder, to=injector.CallableProvider(self.builder))
