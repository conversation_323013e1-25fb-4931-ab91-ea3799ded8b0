import requests


class SlackClient:
    def __init__(self, slack_log_channel_webhook):
        self.webhook_url = slack_log_channel_webhook

    def send_msg(self, title, content_title, content_text, content_type='warning'):
        attachments = [
            {
                'color': content_type,
                'title': content_title,
                'text': content_text,
            }
        ]
        data = {
            'attachments': attachments,
            'text': title
        }

        return requests.post(self.webhook_url, json=data)

    def send_msg_custom_attachment(self, title, attachments):
        data = {
            'attachments': attachments,
            'text': title
        }

        if not self.webhook_url:
            print(f'Slack: {data}')
            return {}

        return requests.post(self.webhook_url, json=data)
