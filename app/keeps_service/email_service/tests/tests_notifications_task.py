from datetime import date

from keeps_service.email_service import notification


def test_email_analytics_new_report(mocker):
    mocker.patch("keeps_service.email_service.utils.aws.aws_ses.AmazonEmailService.sender", return_value={})

    email_data = {
        "report_name": "Missão Teste 1",
        "report_url": "missao.com",
    }

    receivers = [{"email": "<EMAIL>", "language": "en", "email_verified": True}]

    # Nos testes nao usa '.delay()' do Ce<PERSON>y, para poder pegar imediatamente a resposta do método mockado
    response = notification.notify_users(email_data, "new_report", receivers)

    # check if variables are inside generated message
    message = response.get("messages")[0]
    check_email_data(email_data, message)


def check_email_data(email_data, message, date_format="%d-%m-%Y"):
    # check if the values are inside generated message
    for data_key in email_data:
        data = email_data.get(data_key)
        if not data:
            continue
        if type(data) == date:
            data = data.strftime(date_format)
            assert data in message
        elif type(data) == dict:
            check_email_data(data, message, date_format)
        elif type(data) == list:
            for sub_data in data:
                check_email_data(sub_data, message, date_format)
        else:
            assert data in message
    assert "%(" not in message
