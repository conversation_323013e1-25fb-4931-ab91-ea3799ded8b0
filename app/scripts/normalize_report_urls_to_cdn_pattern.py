from config.default import Config
from database import session
from domain.report.models import Report

OLD_URL = 'https://s3.amazonaws.com/keeps.reports/'
NEW_URL = f'{Config.AWS_BASE_CDN_URL}/{Config.AWS_BUCKET_NAME}/{Config.AWS_BUCKET_PATH}/'


def run():
    query = session.query(Report).filter(Report.url.isnot(None))
    for report in query:
        report.url = report.url.replace(OLD_URL, NEW_URL)
    session.commit()


if __name__ == '__main__':
    run()
