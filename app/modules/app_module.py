from controller.api.course import CourseEndpoints
from controller.api.course_v2 import CourseEndpointsV2
from controller.api.elasticsearch import ElasticSearchEndpoints
from controller.api.healthcheck import HealthCheckEndpoints
from controller.api.notification import NotificationEndpoints
from controller.api.report import ReportEndpoints
from controller.api.root import RootEndpoints
from controller.api.user import UserEndpoints
from controller.api.user_v2 import UserEndpointsV2
from injector import Module, provider, singleton
from keeps_flask.app import ApplicationRegister, CorsResources
from keeps_flask.swagger.routes import SwaggerEndpoints


class AppModule(Module):
    @singleton
    @provider
    def register(self) -> ApplicationRegister:
        """
        Provides a list of factory functions (callables) that are meant to register things on the application.
        Here they are used to register the endpoints of the REST API.

        The injector will be at disposal of the functions provided here (they can have their arguments injected too).

        This dependency is resolved at the end of the application configuration when it is created. So, expect this
        registration to be the last thing to happen.

        Returns:
            a list of callables that will be called at the end of the application configuration.
        """
        return [
            CourseEndpoints,
            CourseEndpointsV2,
            NotificationEndpoints,
            UserEndpoints,
            UserEndpointsV2,
            ReportEndpoints,
            ElasticSearchEndpoints,
            SwaggerEndpoints,
            HealthCheckEndpoints,
            RootEndpoints,
        ]

    @singleton
    @provider
    def cors_resources(self) -> CorsResources:
        """
        Provides the CORS configurations of the Application as a dict with paths and origins.

        Returns:
            the dict containing the CORS configurations.
        """
        return {
            r"/api/*": {
                "origins": "*"
            },
            r"/swagger/": {
                "origins": "*"
            }
        }
