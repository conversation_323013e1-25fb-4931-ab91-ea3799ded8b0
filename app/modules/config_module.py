import logging
import os

from config.default import Config
from custom.discord_webhook_logger import DiscordWebhookLogger
from domain.common import dependencies
from domain.course.services import CourseService
from domain.elasticsearch.services import ElasticSearchService
from domain.notification.services import NotificationService
from domain.report.services import ReportService, ReportTypeService
from injector import Module, provider
from keeps_flask.app import ApplicationConfig
from reports.di import ReportContainer
from werkzeug.utils import import_string


class ConfigModule(Module):
    """
    Injector Module that provides the ApplicationConfig dependency.
    """

    @provider
    def configuration(self) -> ApplicationConfig:
        """
        Provides the ApplicationConfig dependency.

        Returns:
            str: the configuration module fully qualified name.
        """
        return import_string('config.default.' + os.getenv("APP_ENV", 'Testing'))

    def configure(self, binder):
        binder.bind(dependencies.Logger, to=logging)
        binder.bind(dependencies.DatabaseUrl, to=Config.DATABASE_URL)
        binder.bind(dependencies.DatabaseEngine, to=None)
        binder.bind(dependencies.DatabaseSession, to=None)
        binder.bind(dependencies.DatabaseMyAccountUrl, to=Config.DATABASE_MYACCOUNT_URL)
        binder.bind(dependencies.CourseService, to=CourseService)
        binder.bind(dependencies.NotificationService, to=NotificationService)
        binder.bind(dependencies.ElasticSearchService, to=ElasticSearchService)
        binder.bind(dependencies.ElasticsearchURL, to=Config.ELASTICSEARCH_URL)
        binder.bind(dependencies.ElasticsearchAUTH, to=Config.ELASTICSEARCH_AUTH)
        binder.bind(dependencies.ElasticsearchAUTH, to=Config.ELASTICSEARCH_AUTH)
        binder.bind(dependencies.ElasticsearchConnection, to=None)
        binder.bind(dependencies.Report, to=ReportContainer)
        binder.bind(dependencies.ReportService, to=ReportService)
        binder.bind(dependencies.ReportTypeService, to=ReportTypeService)
        binder.bind(dependencies.WebhookLogger, to=DiscordWebhookLogger)
