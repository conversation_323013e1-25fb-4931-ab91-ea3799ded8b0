from domain.common.dependencies import ElasticsearchAUTH, ElasticsearchConnection, ElasticsearchURL
from elasticsearch import Elasticsearch
from elasticsearch_dsl import connections
from injector import Module, provider, singleton


class ElasticsearchModule(Module):
    @singleton
    @provider
    def es_connection(self, url: ElasticsearchURL, auth: ElasticsearchAUTH) -> ElasticsearchConnection:
        """
        Args:
            url: the elasticsearch's address.

        Returns:
            A Elasticsearch connection instance.
            :param url:
            :param auth:
        """
        conn = Elasticsearch(url, http_auth=auth)
        connections.create_connection(
            hosts=[url],
            timeout=20,
            http_auth=auth
        )

        return conn
