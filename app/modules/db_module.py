import database
from domain.common.dependencies import DatabaseEngine, DatabaseSession, DatabaseUrl
from flask_sqlalchemy_session import flask_scoped_session
from injector import Module, provider, singleton
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker


class DatabaseModule(Module):
    @singleton
    @provider
    def engine(self, url: DatabaseUrl) -> DatabaseEngine:
        """
        Args:
            url: the database's address.

        Returns:
            A engine connection instance.
        """
        return create_engine(url, connect_args={'connect_timeout': 60})

    @singleton
    @provider
    def session(self, engine: DatabaseEngine) -> DatabaseSession:
        """
        Args:
            engine: a reference for DatabaseEngine.

        Returns:
            The scoped_session to use.
        """
        database.app_session = flask_scoped_session(sessionmaker(autocommit=False, autoflush=True, bind=engine))
        return database.app_session
