"""create_tables

Revision ID: 001
Revises: 
Create Date: 2021-12-17 10:20:54.714983

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('report_type',
    sa.Column('created', sa.DateTime(), nullable=True),
    sa.Column('updated', sa.DateTime(), nullable=True),
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('file_format', sa.String(), nullable=True),
    sa.Column('application', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_table('report',
    sa.Column('created', sa.DateTime(), nullable=True),
    sa.Column('updated', sa.DateTime(), nullable=True),
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('type_id', sa.String(length=36), nullable=False),
    sa.Column('processing_time', sa.Integer(), nullable=True),
    sa.Column('url', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('filters', sa.JSON(), nullable=True),
    sa.Column('language', sa.String(), nullable=True),
    sa.Column('user_creator', sa.JSON(), nullable=True),
    sa.Column('company_id', sa.String(length=36), nullable=False),
    sa.ForeignKeyConstraint(['type_id'], ['report_type.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('report')
    op.drop_table('report_type')
    # ### end Alembic commands ###
