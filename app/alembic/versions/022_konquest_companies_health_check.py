"""0myaccount_user_permissions_report_type

Revision ID: 020
Revises: 019
Create Date: 2024-10-23 15:21:43.122913

"""
from alembic import op

# revision identifiers, used by Alembic.
from sqlalchemy import orm

from domain.report.models import ReportType

revision = '022'
down_revision = '020'
branch_labels = None
depends_on = None

REPORT_TYPES = [
    {
        'name': 'konquest-companies-health-check',
        'description': 'konquest companies health check',
        'application': 'KONQUEST',
        'model': 'EXPORT'
    }
]


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    bind = op.get_bind()
    session = orm.Session(bind=bind)
    for report_type in REPORT_TYPES:
        instance = ReportType(**report_type)
        instance.application = report_type["application"]
        session.add(instance)
    session.commit()
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    bind = op.get_bind()
    session = orm.Session(bind=bind)
    for report_type in REPORT_TYPES:
        instance = session.query(ReportType).filter(ReportType.name == report_type["name"]).first()
        if not instance:
            return
        session.delete(instance)
    session.commit()
    # ### end Alembic commands ###
