"""new_smartzap_report_types

Revision ID: 015
Revises: 014
Create Date: 2022-03-02 15:21:43.122913

"""
from alembic import op

# revision identifiers, used by Alembic.
from sqlalchemy import orm

from domain.report.models import ReportType

revision = '019'
down_revision = '018'
branch_labels = None
depends_on = None

REPORT_TYPES = [
    {
        'name': 'konquest-trails-enrollments-export',
        'description': 'konquest trails enrollments',
        'application': 'KONQUEST',
        'model': 'EXPORT'
    }
]

def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    bind = op.get_bind()
    session = orm.Session(bind=bind)
    for report_type in REPORT_TYPES:
        instance = ReportType(**report_type)
        instance.application = report_type["application"]
        session.add(instance)
    session.commit()
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    bind = op.get_bind()
    session = orm.Session(bind=bind)
    for report_type in REPORT_TYPES:
        instance = session.query(ReportType).filter(ReportType.name == report_type["name"]).first()
        if not instance:
            return
        session.delete(instance)
    session.commit()
    # ### end Alembic commands ###
