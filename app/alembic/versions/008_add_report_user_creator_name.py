"""add_report_user_creator_name

Revision ID: 008
Revises: 006
Create Date: 2022-07-27 09:52:51.765574

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
from sqlalchemy import orm

from domain.report.models import Report

revision = '008'
down_revision = '006'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('report', sa.Column('user_creator_name', sa.String()))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('report', 'user_creator_name')
    # ### end Alembic commands ###
