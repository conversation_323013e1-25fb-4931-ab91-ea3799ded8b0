"""refactor_reports

Revision ID: 002
Revises: 001
Create Date: 2021-12-17 15:53:56.717890

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '002'
down_revision = '001'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('report', sa.Column('report_type_id', sa.String(length=36), nullable=False))
    op.drop_constraint('report_type_id_fkey', 'report', type_='foreignkey')
    op.create_foreign_key(None, 'report', 'report_type', ['report_type_id'], ['id'])
    op.drop_column('report', 'type_id')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('report', sa.Column('type_id', sa.VARCHAR(length=36), autoincrement=False, nullable=False))
    op.drop_constraint(None, 'report', type_='foreignkey')
    op.create_foreign_key('report_type_id_fkey', 'report', 'report_type', ['type_id'], ['id'])
    op.drop_column('report', 'report_type_id')
    # ### end Alembic commands ###
