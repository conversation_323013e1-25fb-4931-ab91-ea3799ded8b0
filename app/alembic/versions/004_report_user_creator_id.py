"""report_user_creator_id

Revision ID: 004
Revises: 8ebda845261e
Create Date: 2021-12-27 13:13:14.088613

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '004'
down_revision = '8ebda845261e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('report', sa.Column('user_creator_id', sa.String(), nullable=False))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('report', 'user_creator_id')
    # ### end Alembic commands ###
