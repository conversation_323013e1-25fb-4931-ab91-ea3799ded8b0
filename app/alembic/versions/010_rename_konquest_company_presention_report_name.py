"""rename_company_id_to_workspace_id

Revision ID: 009
Revises: 008
Create Date: 2022-08-10 12:34:20.765318

"""
from alembic import op
from sqlalchemy import orm
import sqlalchemy as sa


# revision identifiers, used by Alembic.
from domain.report.models import ReportType

revision = '010'
down_revision = '009'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    bind = op.get_bind()
    session = orm.Session(bind=bind)
    report_type = session.query(ReportType).filter(ReportType.name=="konquest-company-presentation").first()
    report_type.name = "konquest-workspace-presentation"
    session.commit()
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    bind = op.get_bind()
    session = orm.Session(bind=bind)
    report_type = session.query(ReportType).filter(ReportType.name == "konquest-workspace-presentation").first()
    report_type.name = "konquest-company-presentation"
    session.commit()
    # ### end Alembic commands ###
