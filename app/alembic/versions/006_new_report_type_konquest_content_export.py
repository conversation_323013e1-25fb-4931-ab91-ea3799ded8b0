"""new_report_type_konquest_content_export

Revision ID: 006
Revises: 005
Create Date: 2022-03-02 15:21:43.122913

"""
from alembic import op

# revision identifiers, used by Alembic.
from sqlalchemy import orm

from domain.report.models import ReportType

revision = '006'
down_revision = '005'
branch_labels = None
depends_on = None

REPORT_TYPE = {
        'name': 'konquest-contents-export',
        'description': 'konquest contents export',
        'application': 'KONQUEST',
        'model': 'EXPORT'
}

def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    bind = op.get_bind()
    session = orm.Session(bind=bind)
    instance = ReportType(**REPORT_TYPE)
    instance.application = REPORT_TYPE["application"]
    session.add(instance)
    session.commit()
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    bind = op.get_bind()
    session = orm.Session(bind=bind)
    instance = session.query(ReportType).filter(ReportType.name == REPORT_TYPE["name"]).first()
    if not instance:
        return
    session.delete(instance)
    session.commit()
    # ### end Alembic commands ###
