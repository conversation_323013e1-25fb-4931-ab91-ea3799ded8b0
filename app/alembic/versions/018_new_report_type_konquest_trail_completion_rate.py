"""new_konquest_report_type
Revision ID: 018
Revises: 018
Create Date: 2024-02-22 15:21:43.122913
"""
from alembic import op

# revision identifiers, used by Alembic.
from sqlalchemy import orm

from domain.report.models import ReportType

revision = '018'
down_revision = '017'
branch_labels = None
depends_on = None

REPORT_TYPES = [
    {
        'name': 'konquest-trails-completion-rate-export',
        'description': 'konquest trails completion rate export',
        'application': 'KONQUEST',
        'model': 'EXPORT'
    }
]

def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    bind = op.get_bind()
    session = orm.Session(bind=bind)
    for report_type in REPORT_TYPES:
        instance = ReportType(**report_type)
        instance.application = report_type["application"]
        session.add(instance)
    session.commit()
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    bind = op.get_bind()
    session = orm.Session(bind=bind)
    for report_type in REPORT_TYPES:
        instance = session.query(ReportType).filter(ReportType.name == report_type["name"]).first()
        if not instance:
            return
        session.delete(instance)
    session.commit()
    # ### end Alembic commands ###