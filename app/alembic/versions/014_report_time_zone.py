"""report_time_zone

Revision ID: 014
Revises: 013
Create Date: 2022-10-19 11:41:02.795514

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '014'
down_revision = '013'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('report', sa.Column('time_zone', sa.String(), nullable=True, default='America/Sao_Paulo'))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('report', 'time_zone')
    # ### end Alembic commands ###
