"""rename_company_id_to_workspace_id

Revision ID: 009
Revises: 008
Create Date: 2022-08-10 12:34:20.765318

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '009'
down_revision = '008'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('report', 'company_id', nullable=False, new_column_name='workspace_id')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('report', 'workspace_id', nullable=False, new_column_name='company_id')
    # ### end Alembic commands ###
