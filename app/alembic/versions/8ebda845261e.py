"""empty message

Revision ID: 8ebda845261e
Revises: 002
Create Date: 2021-12-21 14:48:55.650480

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy import orm
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
from domain.report.models import ReportType

revision = '8ebda845261e'
down_revision = '002'
branch_labels = None
depends_on = None

report_types = [
    {
        'name': 'smartzap-course-presentation',
        'description': 'smartzap course presentation',
        'application': 'SMARTZAP',
        'model': 'PRESENTATION'
    },
    {
        'name': 'konquest-mission-presentation',
        'description': 'konquest mission presentation',
        'application': 'KONQUEST',
        'model': 'PRESENTATION'
    },
    {
        'name': 'konquest-company-presentation',
        'description': 'konquest company presentation',
        'application': 'KONQUEST',
        'model': 'PRESENTATION'
    },
    {
        'name': 'konquest-user-presentation',
        'description': 'konquest user presentation',
        'application': 'KONQUEST',
        'model': 'PRESENTATION'
    },
    {
        'name': 'konquest-missions-export',
        'description': 'konquest missions export',
        'application': 'KONQUEST',
        'model': 'EXPORT'
    },
    {
        'name': 'konquest-pulses-channels-export',
        'description': 'konquest pulses channels export',
        'application': 'KONQUEST',
        'model': 'EXPORT'
    },
    {
        'name': 'konquest-users-pulses-activities-export',
        'description': 'konquest users pulses activities export',
        'application': 'KONQUEST',
        'model': 'EXPORT'
    },
    {
        'name': 'konquest-missions-enrollments-export',
        'description': 'konquest missions enrollments export',
        'application': 'KONQUEST',
        'model': 'EXPORT'
    },
    {
        'name': 'konquest-groups-missions-users-export',
        'description': 'konquest groups missions users export',
        'application': 'KONQUEST',
        'model': 'EXPORT'
    },
    {
        'name': 'konquest-groups-channels-users-export',
        'description': 'konquest groups channels users export',
        'application': 'KONQUEST',
        'model': 'EXPORT'
    },
    {
        'name': 'konquest-missions-quizzes-answers-export',
        'description': 'konquest missions quizzes answers export',
        'application': 'KONQUEST',
        'model': 'EXPORT'
    },
    {
        'name': 'konquest-pulses-quizzes-answers-export',
        'description': 'konquest pulses quizzes answers export',
        'application': 'KONQUEST',
        'model': 'EXPORT'
    },
    {
        'name': 'konquest-users-export',
        'description': 'konquest users export',
        'application': 'KONQUEST',
        'model': 'EXPORT'
    },
    {
        'name': 'konquest-users-access-by-date-export',
        'description': 'konquest users access by date export',
        'application': 'KONQUEST',
        'model': 'EXPORT'
    },
    {
        'name': 'konquest-missions-evaluations-export',
        'description': 'konquest missions evaluations export',
        'application': 'KONQUEST',
        'model': 'EXPORT'
    },
]


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('report', sa.Column('file_format', sa.String(), nullable=True))
    op.add_column('report', sa.Column('object_id', sa.String(length=36), nullable=True))
    op.alter_column('report', 'user_creator',
                    existing_type=postgresql.JSON(astext_type=sa.Text()),
                    nullable=False)
    op.add_column('report_type', sa.Column('model', sa.String(), nullable=False))
    op.alter_column('report_type', 'description',
                    existing_type=sa.VARCHAR(),
                    nullable=False)
    op.alter_column('report_type', 'application',
                    existing_type=sa.VARCHAR(),
                    nullable=False)
    op.drop_column('report_type', 'file_format')

    bind = op.get_bind()
    session = orm.Session(bind=bind)
    for report_type in report_types:
        instance = ReportType(**report_type)
        instance.application = report_type["application"]
        session.add(instance)
        session.commit()


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('report_type', sa.Column('file_format', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.alter_column('report_type', 'application',
                    existing_type=sa.VARCHAR(),
                    nullable=True)
    op.alter_column('report_type', 'description',
                    existing_type=sa.VARCHAR(),
                    nullable=True)
    op.drop_column('report_type', 'model')
    op.alter_column('report', 'user_creator',
                    existing_type=postgresql.JSON(astext_type=sa.Text()),
                    nullable=True)
    op.drop_column('report', 'object_id')
    op.drop_column('report', 'file_format')

    bind = op.get_bind()
    session = orm.Session(bind=bind)
    for report_type in report_types:
        instance = session.query(ReportType).filter(ReportType.name == report_type["name"]).first()
        if not instance:
            continue
        session.delete(instance)
        session.commit()
