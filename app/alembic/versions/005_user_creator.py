"""user_creator

Revision ID: 005
Revises: 004
Create Date: 2022-01-11 13:44:05.135882

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '005'
down_revision = '004'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('report', 'user_creator_id',
               existing_type=sa.VARCHAR(),
               nullable=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('report', 'user_creator_id',
               existing_type=sa.VARCHAR(),
               nullable=True)
    # ### end Alembic commands ###
