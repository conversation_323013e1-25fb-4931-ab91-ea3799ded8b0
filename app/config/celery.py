from __future__ import absolute_import

from celery import Celery
from config.default import Config

celery = Celery(Config.APP_NAME, broker=Config.CELERY_BROKER_URL)
celery.conf.timezone=Config.TIMEZONE
celery.conf.task_default_queue=Config.CELERY_DEFAULT_QUEUE
celery.conf.result_backend=Config.CELERY_RESULT_BACKEND
celery.conf.task_ignore_result=Config.CELERY_IGNORE_RESULT

celery.config_from_object(Config)
celery.autodiscover_tasks(lambda: ["domain", "reports", "tasks", "tasks.report"], force=True)
