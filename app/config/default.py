"""Main configuration module"""

import os
from distutils import util

from apispec import APISpec
from apispec.ext.marshmallow import MarshmallowPlugin
from apispec_webframeworks.flask import FlaskPlugin


class Config:
    """
    Base configuration, this class contains most of the variables and default values.
    """
    APP_NAME = 'analytics-api'
    DEBUG = os.environ.get('DEBUG', False)
    TESTING = False
    BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    LOCALE_JSON = f'{BASE_DIR}/config/locale.json'
    ENVIRONMENT = os.getenv('ENVIRONMENT', 'development')

    APPLICATION_ID = 'c2928f23-a5a6-4f59-94a7-7e409cf1d4f4'

    APISPEC_SPEC = APISpec(
        title='Learning Analytics API',
        version='1.0.0',
        openapi_version="3.0.2",
        plugins=[FlaskPlugin(), MarshmallowPlugin()],
    )

    KEYCLOAK_ISS = os.environ.get('KEYCLOAK_ISS', 'https://iam.keepsdev.com/auth/realms/keeps')
    KEYCLOAK_PUBLIC_KEY = os.getenv('KEYCLOAK_PUBLIC_KEY')

    ELASTICSEARCH_URL = os.environ.get('ELASTICSEARCH_URL', 'http://localhost')
    ELASTICSEARCH_AUTH = os.environ.get('ELASTICSEARCH_AUTH', 'user:pass')
    ELASTICSEARCH_INDEX_COURSES_V1 = os.environ.get('ELASTICSEARCH_INDEX_COURSES_V1', 'learning-analytics-courses-stage')
    ELASTICSEARCH_INDEX_USERS_V1 = os.environ.get('ELASTICSEARCH_INDEX_USERS_V1', 'learning-analytics-users-stage')
    ELASTICSEARCH_INDEX_NOTIFICATIONS = os.environ.get('ELASTICSEARCH_INDEX_NOTIFICATIONS', 'learning-analytics-notifications-stage')
    ELASTICSEARCH_INDEX_KONTENT = os.environ.get('ELASTICSEARCH_INDEX_KONTENT', 'kontent-hml')

    ELASTICSEARCH_INDEX_COURSES = os.environ.get('ELASTICSEARCH_INDEX_COURSES', 'kafka-analytics-courses-stage')
    ELASTICSEARCH_INDEX_USERS = os.environ.get('ELASTICSEARCH_INDEX_USERS', 'kafka-analytics-users-stage')
    ELASTICSEARCH_INDEX_ACTIVITIES = os.environ.get('ELASTICSEARCH_INDEX_ACTIVITIES', 'kafka-analytics-activities-stage')
    ELASTICSEARCH_INDEX_ANSWERS = os.environ.get('ELASTICSEARCH_INDEX_ANSWERS', 'kafka-analytics-answers-stage')
    ELASTICSEARCH_INDEX_ENROLLMENTS = os.environ.get('ELASTICSEARCH_INDEX_ENROLLMENTS', 'kafka-analytics-enrollments-stage')
    ELASTICSEARCH_INDEX_COURSE_RATINGS = os.environ.get('ELASTICSEARCH_INDEX_COURSE_RATINGS', 'kafka-analytics-course-ratings-stage')
    ELASTICSEARCH_INDEX_COURSE_EVALUATIONS = os.environ.get('ELASTICSEARCH_INDEX_COURSE_EVALUATIONS', 'kafka-analytics-course-evaluations-stage')
    ELASTICSEARCH_INDEX_CHANNELS = os.environ.get('ELASTICSEARCH_INDEX_CHANNELS', 'kafka-analytics-channels-stage')
    ELASTICSEARCH_INDEX_PULSES = os.environ.get('ELASTICSEARCH_INDEX_PULSES', 'kafka-analytics-pulses-stage')

    DATABASE_URL = os.environ.get('DATABASE_URL', 'postgresql://localhost')

    DATABASE_KONQUEST_URL = os.environ.get('DATABASE_KONQUEST_URL', 'postgresql://localhost')
    DATABASE_MYACCOUNT_URL = os.environ.get('DATABASE_MYACCOUNT_URL', 'postgresql://localhost')
    DATABASE_SMARTZAP_URL = os.environ.get('DATABASE_SMARTZAP_URL', 'postgresql://localhost')
    DATABASE_KONTENT_URL = os.environ.get('DATABASE_KONTENT_URL', 'postgresql://localhost')

    EMAIL_LOCALE_PATH = os.path.join(BASE_DIR, "email_locale")

    TIMEZONE = os.environ.get('TIMEZONE', 'America/Sao_Paulo')

    AWS_BASE_CDN_URL = os.getenv('AWS_BASE_CDN_URL')
    AWS_BASE_S3_URL = os.getenv('AWS_BASE_S3_URL')
    AWS_BUCKET_NAME = os.getenv('AWS_BUCKET_NAME')
    AWS_BUCKET_NAME_REPORT = os.getenv('AWS_BUCKET_NAME_REPORT')
    AWS_BUCKET_PATH = os.getenv("AWS_BUCKET_PATH", "analytics")

    AWS_S3_ACCESS_KEY_ID = os.getenv('AWS_S3_ACCESS_KEY_ID')
    AWS_S3_SECRET_ACCESS_KEY = os.getenv('AWS_S3_SECRET_ACCESS_KEY')
    AWS_S3_REGION_NAME = os.getenv('AWS_S3_REGION_NAME', 'us-east-1')
    AWS_MAIL_SENDER = "Plataforma Aprendizagem <<EMAIL>>"

    AWS_LAMBDA_ACCESS_KEY_ID = os.getenv('AWS_LAMBDA_ACCESS_KEY_ID')
    AWS_LAMBDA_SECRET_ACCESS_KEY = os.getenv('AWS_LAMBDA_SECRET_ACCESS_KEY')
    AWS_LAMBDA_REGION_NAME = os.getenv('AWS_LAMBDA_REGION_NAME', 'us-east-1')

    # Celery
    CELERY_BROKER_URL = os.getenv('CELERY_BROKER_URL', 'amqp://localhost:5672')
    CELERY_DEFAULT_QUEUE = os.getenv('CELERY_DEFAULT_QUEUE', 'analytics-tasks-stage')
    CELERY_RESULT_BACKEND = os.getenv('CELERY_RESULT_BACKEND', 'rpc://localhost:5672')

    # Whether to store the task return values or not (tombstones).
    # If you still want to store errors, just not successful return values, you can set task_store_errors_even_if_ignored.
    CELERY_IGNORE_RESULT = bool(util.strtobool(os.getenv("CELERY_IGNORE_RESULT", "True")))

    SLACK_LOG_CHANNEL_WEBHOOK = os.getenv('SLACK_LOG_CHANNEL_WEBHOOK', '*******************************************************************************')

    # Export limit range dates
    KONQUEST_EXPORT_MISSION_ENROLLMENT_LIMIT_DAYS = os.getenv('KONQUEST_EXPORT_MISSION_ENROLLMENT_LIMIT_DAYS', 60)
    KONQUEST_EXPORT_MISSIONS_LIMIT_DAYS = os.getenv('KONQUEST_EXPORT_MISSIONS_LIMIT_DAYS', 365)
    KONQUEST_EXPORT_MISSIONS_QUIZZES_ANSWERS_LIMIT_DAYS = os.getenv('KONQUEST_EXPORT_MISSIONS_QUIZZES_ANSWERS_LIMIT_DAYS',
                                                                    90)
    KONQUEST_EXPORT_GROUPS_MISSIONS_USERS_LIMIT_DAYS = os.getenv('KONQUEST_EXPORT_GROUPS_MISSIONS_USERS_LIMIT_DAYS', 365)
    KONQUEST_EXPORT_PULSES_CHANNELS_LIMIT_DAYS = os.getenv('KONQUEST_EXPORT_PULSES_CHANNELS_LIMIT_DAYS', 365)
    KONQUEST_EXPORT_USERS_PULSES_ACTIVITIES_LIMIT_DAYS = os.getenv('KONQUEST_EXPORT_USERS_PULSES_ACTIVITIES_LIMIT_DAYS',
                                                                   60)
    KONQUEST_EXPORT_PULSES_QUIZZES_ANSWERS_LIMIT_DAYS = os.getenv('KONQUEST_EXPORT_PULSES_QUIZZES_ANSWERS_LIMIT_DAYS',
                                                                  90)
    KONQUEST_EXPORT_GROUPS_CHANNELS_USERS_LIMIT_DAYS = os.getenv('KONQUEST_EXPORT_GROUPS_CHANNELS_USERS_LIMIT_DAYS',
                                                                 1800)
    KONQUEST_EXPORT_MISSION_EVALUATIONS_LIMIT_DAYS = os.getenv('KONQUEST_EXPORT_MISSION_EVALUATIONS_LIMIT_DAYS',
                                                               365)
    KONQUEST_EXPORT_USER_CONSUMPTION_LIMIT_DAYS = os.getenv('KONQUEST_EXPORT_USER_CONSUMPTION_LIMIT_DAYS', 120)
    KONQUEST_EXPORT_USER_GENERAL_STATISTICS_LIMIT_DAYS = os.getenv('KONQUEST_EXPORT_USER_GENERAL_STATISTICS_LIMIT_DAYS', 365)
    KONQUEST_EXPORT_USER_ACCESS_BY_DATE_LIMIT_DAYS = os.getenv('KONQUEST_EXPORT_USER_ACCESS_BY_DATE_LIMIT_DAYS', 365)
    KONQUEST_EXPORT_USERS_LIMIT_DAYS = os.getenv('KONQUEST_EXPORT_USERS_LIMIT_DAYS', 720)

    NOTIFICATION_REPORT_IMAGE = os.getenv('NOTIFICATION_REPORT_IMAGE', 'https://s3.amazonaws.com/keeps.konquest.media.prd/assets/notification/report.png')

    CONTENT_TYPE_PDF_ID = os.getenv("CONTENT_TYPE_PDF_ID", "0faac34b-2393-4352-8a94-a9ee0659f824")
    CONTENT_TYPE_VIDEO_ID = os.getenv("CONTENT_TYPE_VIDEO_ID", "569cc389-ac1d-4fa0-9692-f715b475b59b")
    CONTENT_TYPE_SPREADSHEET_ID = os.getenv("CONTENT_TYPE_SPREADSHEET_ID", "673e4c02-ae1c-4e61-830b-706d35bd0b11")
    CONTENT_TYPE_PODCAST_ID = os.getenv("CONTENT_TYPE_PODCAST_ID", "799f766c-a956-4c03-b5aa-bde9ba357de8")
    CONTENT_TYPE_QUESTION_ID = os.getenv("CONTENT_TYPE_QUESTION_ID", "7a41a8e0-ee37-4d0b-ad4f-35bada67134d")
    CONTENT_TYPE_PRESENTATION_ID = os.getenv("CONTENT_TYPE_PRESENTATION_ID", "7ee375e4-b781-46e6-b0de-0323ebb94b96")
    CONTENT_TYPE_TEXT_ID = os.getenv("CONTENT_TYPE_TEXT_ID", "b7094e27-b263-4fed-a928-6f0a78439cbe")
    CONTENT_TYPE_IMAGE_ID = os.getenv("CONTENT_TYPE_IMAGE_ID", "2284bfce-fdfc-4477-9143-39c380cc653c")

    KONQUEST_APPLICATION_ID = os.getenv("KONQUEST_APPLICATION_ID", "0abf08ea-d252-4d7c-ab45-ab3f9135c288")

    ICON_URL_ARROW_UP = os.getenv("ICON_URL_ARROW_UP", "https://s3.amazonaws.com/keeps.reports/assets/icon_arrow_up.png")
    ICON_URL_ARROW_DOWN = os.getenv("ICON_URL_ARROW_DOWN", "https://s3.amazonaws.com/keeps.reports/assets/icon_arrow_down.png")
    IMAGE_URL_DEFAULT_AVATAR = os.getenv(
        "IMAGE_URL_DEFAULT_AVATAR",
        "https://s3.amazonaws.com/keeps.reports/assets/mascote_icon.png"
    )

    # User Engagement
    MINIMUM_WORKSPACE_MISSIONS = int(os.getenv("MINIMUM_WORKSPACE_MISSIONS", 10))
    MINIMUM_WORKSPACE_PULSES = int(os.getenv("MINIMUM_WORKSPACE_PULSES", 10))
    MAXIMUM_MISSIONS_COMPLETED = int(os.getenv("MAXIMUM_MISSIONS_COMPLETED", 4))
    MAXIMUM_PULSES_CONSUMED = int(os.getenv("MAXIMUM_PULSES_CONSUMED", 4))
    MAXIMUM_DAYS_CONSUMED = int(os.getenv("MAXIMUM_DAYS_CONSUMED", 30))
    MULTIPLIER_MISSIONS_CONSUMED = float(os.getenv("MULTIPLIER_MISSIONS_CONSUMED", 0.25))
    MULTIPLIER_PULSES_CONSUMED = float(os.getenv("MULTIPLIER_PULSES_CONSUMED", 0.25))
    MULTIPLIER_DAYS_CONSUME = float(os.getenv("MULTIPLIER_DAYS_CONSUME", 0.1))
    WEIGHT_PERFORMANCE = int(os.getenv("WEIGHT_PERFORMANCE", 3))
    WEIGHT_MISSIONS_CONSUMED = int(os.getenv("WEIGHT_MISSIONS_CONSUMED", 2))
    WEIGHT_PULSES_CONSUMED = int(os.getenv("WEIGHT_PULSES_CONSUMED", 1))
    WEIGHT_DAYS_CONSUMED = int(os.getenv("WEIGHT_DAYS_CONSUMED", 2))

    MAX_DAYS_OF_USER_INACTIVITY = int(os.getenv('MAX_DAYS_OF_USER_INACTIVITY', 30))

    REPORT_LIFE_TIME_DAY = int(os.getenv("REPORT_LIFE_TIME_DAY", 90))
    PROCESS_PRESENTATIONS_LIMIT = int(os.getenv("PROCESS_PRESENTATIONS_LIMIT", 10))
    SCHEDULER_INTERVAL_MIN_PROCESS_PRESENTATION = int(os.getenv("SCHEDULER_INTERVAL_MIN_PROCESS_PRESENTATION", 5))
    SCHEDULER_INTERVAL_MIN_PROCESS_EXPORTS = int(os.getenv("SCHEDULER_INTERVAL_MIN_PROCESS_EXPORTS", 5))
    DISCORD_WEBHOOK = os.getenv(
        "DISCORD_WEBHOOK",
        "https://discord.com/api/webhooks/1078690022997364787/gVOSRJuUBkrJ9BKD9IZhhdDT-0tAxWWs4RITFgFo7_DWAfSVzjDGN7czNGb_6oiXqQA_"
    )
    JASPER_REPORT_SERVER_URL = os.getenv("JASPER_REPORT_SERVER_URL", "https://learning-platform-api-stage.keepsdev.com/report-generator")

    KEEPS_WORKSPACE_ID = os.getenv("KEEPS_WORKSPACE_ID", "e76b5082-f4fe-4f41-be79-1977840e16a8")

    # EXPORTS FORMATTERS
    MAX_COLUMN_WIDTH = int(os.getenv("MAX_COLUMN_WIDTH", 100))

    # APM
    ELASTIC_APM = {
        "SERVICE_NAME": os.getenv("ELASTIC_APM_SERVICE_NAME", "analytics"),
        "SERVER_URL": os.getenv("ELASTIC_APM_SERVER_URL", "https://keeps.apm.us-east-1.aws.cloud.es.io"),
        "SECRET_TOKEN": os.getenv("ELASTIC_APM_SECRET_TOKEN"),
        "ENVIRONMENT": os.getenv("ELASTIC_APM_ENVIRONMENT", "development"),
        "DEBUG": DEBUG if ENVIRONMENT in ['stage', 'staging'] else None,  # True to activate in debug mode for stage

        # Fine-tuning - Ref.: https://www.elastic.co/guide/en/apm/agent/python/current/configuration.html
        "TRANSACTION_SAMPLE_RATE": 0.1,  # default: 1.0
        "SPAN_STACK_TRACE_MIN_DURATION": -1,  # default: 5ms
        "SPAN_COMPRESSION_SAME_KIND_MAX_DURATION": "5ms",  # default: 0ms,
    }


class Production(Config):
    """Configuration to be used on Production."""
    pass


class Development(Config):
    """Configuration to be used during development."""
    DEBUG = True


class Testing(Development):
    """Testing configuration."""
    TESTING = True
