import celery
import database
from custom import DISCORD_WEBHOOK


class ProcessReportBaseTask(celery.Task):
    autoretry_for = (Exception,)
    max_retries = 5
    default_retry_delay = 30
    reject_on_worker_lost = True
    acks_on_failure_or_timeout = False
    acks_late = True

    def run(self, *args, **kwargs):
        super().run(*args, **kwargs)

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        title = f'Report Process Task Error. Task id: {task_id}'
        DISCORD_WEBHOOK.emit_short_message(title, exc)
        try:
            database.session.rollback()
            database.engine.dispose()
        except Exception as error:
            DISCORD_WEBHOOK.emit_short_message("Error to rollback session", error)

    def after_return(self, status, retval, task_id, args, kwargs, einfo):
        database.session.remove()
