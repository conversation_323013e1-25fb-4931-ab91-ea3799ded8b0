#!/usr/bin/env python3

import pandas as pd
import pytz
from datetime import datetime
import warnings

# Simulate the issue by creating a DataFrame with timezone-aware datetime columns
def test_datetime_formatting_fix():
    print("Testing datetime formatting fix...")
    
    # Create test data similar to what causes the warning
    dates_with_tz = pd.to_datetime([
        '2025-05-16 12:30:50.556853+00:00',
        '2024-11-07 11:24:59.379051+00:00',
        '2024-10-12 16:43:18.507879+00:00',
        None,  # This will be NaT
        '2024-08-26 19:30:51.676823+00:00'
    ], utc=True)
    
    dates_without_tz = pd.to_datetime([
        '2024-08-07 10:55:39.933063',
        '2025-03-14 08:23:48.335664',
        '2025-05-16 12:15:31.511059',
        '2024-11-05 09:22:06.112550',
        '2024-11-07 11:22:42.277157'
    ])
    
    # Create DataFrame
    df = pd.DataFrame({
        'datetime_with_tz': dates_with_tz,
        'datetime_without_tz': dates_without_tz,
        'other_column': ['A', 'B', 'C', 'D', 'E']
    })
    
    print("Original DataFrame dtypes:")
    print(df.dtypes)
    print("\nOriginal DataFrame:")
    print(df)
    
    # Import and test the FormatterService
    from reports.formatters.formatter_service import FormatterService
    
    # Create formatter service and set timezone
    formatter_service = FormatterService()
    formatter_service.set_time_zone("America/Sao_Paulo")
    
    # Capture warnings
    with warnings.catch_warnings(record=True) as w:
        warnings.simplefilter("always")
        
        # Format the dataframe
        result_df = formatter_service.format_dataframe(df.copy())
        
        # Check if any FutureWarnings were raised
        future_warnings = [warning for warning in w if issubclass(warning.category, FutureWarning)]
        
        if future_warnings:
            print(f"\n❌ FAILED: {len(future_warnings)} FutureWarning(s) still present:")
            for warning in future_warnings:
                print(f"  - {warning.message}")
        else:
            print("\n✅ SUCCESS: No FutureWarnings detected!")
    
    print("\nFormatted DataFrame dtypes:")
    print(result_df.dtypes)
    print("\nFormatted DataFrame:")
    print(result_df)
    
    return len(future_warnings) == 0

if __name__ == "__main__":
    success = test_datetime_formatting_fix()
    exit(0 if success else 1)
