import inspect

import flask
import marshmallow


class WebFilterValidationException(Exception):
    def __init__(self, message):
        self.message = message


def _parse_key(key):
    operations = {
        'eq': '==',
        'ne': '!=',
        'gt': '>',
        'lt': '<',
        'gte': '>=',
        'lte': '<=',
        'like': 'like',
        'ilike': 'ilike',
        'in': 'in',
        'not_in': 'not_in'
    }
    v = key.split('__')
    op = v[-1]
    if not operations.get(op):
        return key, '=='
    key = '__'.join(v[:-1])
    return key, operations.get(op, '')


def _parse_value(op, value):
    value = value.strip()
    if op in ['like', 'ilike'] and value:
        return f'%{value}%'
    if op in ['in', 'not_in'] and value:
        return [s.strip() for s in value.split(',') if s.strip()]
    return value


def _parse_sort(order):
    fields = order.split(',')
    query_sort = []
    for f in fields:
        f = f.strip()
        if not f:
            continue
        if f.startswith('-'):
            query_sort.append(dict(field=f[1:], direction='desc', nullslast=True))
        else:
            query_sort.append(dict(field=f, direction='asc', nullsfirst=True))
    return query_sort


def _parse_pagination(page, per_page):
    page = int(page) if page else 1
    per_page = int(per_page) if per_page else None
    return dict(page=page, per_page=per_page)


def _validate_sort(schema, sort):
    for f in sort:
        field = f['field']
        if field not in schema.load_fields:
            raise WebFilterValidationException(f'WebFilter: Sort field [{field}] not found in schema')


def _validate(schema, filters, sort):
    if not schema:
        return
    if inspect.isclass(schema):
        schema = schema()
    try:
        data = {}
        for f in filters:
            data[f['field']] = f['value']
        filter_parsed = schema.load(data)
        for f in filters:
            f['value'] = filter_parsed.get(f['field'])

        _validate_sort(schema, sort)
    except marshmallow.ValidationError as err:
        raise WebFilterValidationException(f'WebFilter schema validation error: [{err.messages}]')


def load_filters(schema=None, req=None, sort_field='sort', page_field='page', per_page_field='per_page'):
    """
    :param per_page_field: The name of the field for page size
    :param page_field: The name of the field for page number
    :param sort_field: The name of the field for sorting
    :param req: Flask request or default if empty
    :param schema: Marshmallow schema for fields validation
    :return: dict: filter, sort and pagination data.
    """
    query_filters = []
    req = req or flask.request
    for key, value in req.args.items():
        key = key.strip()
        if key in [sort_field, page_field, per_page_field]:
            continue
        field, op = _parse_key(key)
        if op:
            value = _parse_value(op, value)
            query_filters.append(dict(field=field, op=op, value=value))
    query_sort = _parse_sort(req.args.get(sort_field, ''))
    query_pagination = _parse_pagination(req.args.get('page'), req.args.get('per_page', None))
    _validate(schema, query_filters, query_sort)
    return dict(filter_spec=query_filters, sort_spec=query_sort, pag_spec=query_pagination)
