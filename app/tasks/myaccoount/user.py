import datetime
import os
from string import Template

import database_konquest
import database_kontent
import database_myaccount
import pytz
from config.default import Config
from domain.common.utils import split_chunks
from domain.user.models import User
from elasticsearch.helpers import bulk
from elasticsearch_dsl.connections import connections

BULK_SIZE = 300


def get_users_ids():
    _log('-- Retrieving users ids...')
    myaccount_db_engine = database_myaccount.engine
    with myaccount_db_engine.connect() as my_conn:
        rs = load_query(my_conn, None, 'users_ids.sql')
        return [row.id for row in rs]


def load_users_data(index_name, users_ids=None):
    brazil_now = datetime.datetime.now(pytz.timezone(Config.TIMEZONE))

    myaccount_db_engine = database_myaccount.engine
    konquest_db_engine = database_konquest.engine
    kontent_db_engine = database_kontent.engine

    try:
        _log(f'[{brazil_now}] -- Running...')

        with myaccount_db_engine.connect() as my_conn,\
                konquest_db_engine.connect() as kq_conn,\
                kontent_db_engine.connect() as kt_conn:

            rs = None
            total_users = 0

            # chunk
            if users_ids:
                rs = load_query_for_users_ids(my_conn, users_ids, 'users_chunk.sql')
                total_users = len(users_ids)
                _log(f'-- Indexing {len(users_ids)} users for this chunk...')

            # all users
            else:
                rs = load_query(my_conn, None, 'users.sql')
                total_users = query_total_users(my_conn)
                _log(f'-- Indexing {total_users} users...')

            bulk_data = []
            skipped_users = []
            size = 0  # accounting for subdocs
            pre_size = 0

            for i, row in enumerate(rs):
                pre_size = size
                size += 1

                user_id = str_id(row['id'])
                # _log(user_id)

                _log(f'[User: {user_id}] -- Processing {i+1} of {total_users} ' + ('[chunk]' if users_ids else '[complete]'))

                user_role_companies = query_user_role_companies(my_conn, user_id)

                stats_courses = query_stats_courses(kq_conn, user_id)
                # _log(stats_courses)

                stats_course_categories = query_stats_course_categories(kq_conn, user_id)
                # _log(stats_course_categories)

                stats_content_types = query_stats_content_types(kq_conn, kt_conn, user_id)
                # _log(stats_content_types)

                stats_activities = query_stats_activities(kq_conn, user_id)
                # _log(stats_activities)

                stats_answers = query_stats_answers(kq_conn, user_id)

                user_stats = {
                    'updated_date': datetime_to_isoformat(brazil_now),
                    'courses': stats_courses,
                    'course_categories': stats_course_categories,
                    'content_types': stats_content_types,
                    'activities': stats_activities,
                    'answers': stats_answers,
                }

                enrollments = query_user_enrollments(kq_conn, user_id)
                size += len(enrollments)
                # _log(enrollments)

                activities = query_user_activities(kq_conn, user_id)
                size += len(activities)
                # _log(activities)

                populate_activities_content_types(kt_conn, activities)
                # _log(activities)

                owned_channels = query_user_channels(kq_conn, user_id)
                size += len(owned_channels)
                # _log(owned_channels)

                owned_courses = query_user_courses(kq_conn, user_id)
                size += len(owned_courses)
                # _log(owned_courses)

                user = User(
                    _id=user_id,
                    user_id=user_id,
                    name=row['name'],
                    nickname=row['nickname'],
                    email=row['email'],
                    secondary_email=row['secondary_email'],
                    phone=row['phone'],
                    gender=row['gender'],
                    job=row['job'],
                    birthday=row['birthday'],
                    address=row['address'],
                    avatar=row['avatar'],
                    status=row['status'],
                    created_date=datetime_to_isoformat(row['created_date']),
                    updated_date=datetime_to_isoformat(row['updated_date']),
                    language_id=row['language_id'],
                    model_type='user',
                    origin='myaccount',
                    user_role_company=user_role_companies,
                    enrollments=enrollments,
                    activities=activities,
                    owned_channels=owned_channels,
                    owned_courses=owned_courses,
                    user_stats=user_stats
                )
                bulk_data.append(user)

                # Bulk save every BULK_SIZE docs
                if size >= BULK_SIZE:
                    try:
                        _bulk_save(index_name, bulk_data, size)
                    except Exception as e:
                        # Retry skipping last user
                        _log(f'!!! Error: {e}, skipping user {user_id} !!!')
                        skipped_users.append(user_id)
                        del bulk_data[-1]
                        _bulk_save(index_name, bulk_data, pre_size)

                    bulk_data = []
                    size = 0

            # Bulk save remaning docs
            if len(bulk_data):
                try:
                    _bulk_save(index_name, bulk_data, size)
                except Exception as e:
                    # Retry skipping last user
                    _log(f'!!! Error: {e}, skipping user {user_id} !!!')
                    skipped_users.append(user_id)
                    del bulk_data[-1]
                    _bulk_save(index_name, bulk_data, pre_size)

        _log(f'-- Ending... (started at {brazil_now})')
        _log(f'-- Users not imported: {skipped_users}')

    except Exception as e:
        _log(e)
        raise e


def datetime_to_isoformat(_datetime):
    return _datetime.isoformat() if _datetime is not None else None


def load_query(conn, user_id, template_file_name):
    """
    Load SQL Statement for users.

    :param conn: Database connection
    :param user_id: user_id UUID (could be None)
    :param template_file_name: File name where SQL query is located (txt file)

    :return: SQL statement
    """
    file_message = f'{os.path.dirname(__file__)}/query_template/{template_file_name}'
    sql = Template(open(file_message, mode="r", encoding='utf-8').read()).substitute(user_id=user_id)
    rs = conn.execute(sql)
    return rs


def load_query_for_users_ids(conn, users_ids, template_file_name):
    """
    Load SQL Statement for specific users (in clause).

    :param conn: Database connection
    :param users_ids: List of users ids.
    :param template_file_name: File name where SQL query is located (txt file)

    :return: SQL statement
    """
    file_message = f'{os.path.dirname(__file__)}/query_template/{template_file_name}'
    in_ids = ','.join([f"'{id}'" for id in users_ids])
    sql = Template(open(file_message, mode="r", encoding='utf-8').read()).substitute(users_ids=in_ids)
    rs = conn.execute(sql)
    return rs


def query_total_users(conn):
    """
    Return the total users.
    """
    rs = load_query(conn, None, 'users_total.sql')
    row = rs.fetchone()
    return row['total'] if row else 0


def query_user_role_companies(conn, user_id):
    """
    Return companies and roles linked with the specified user.
    """
    rs = load_query(conn, user_id, 'users_companies_roles.sql')

    user_role_companies = []
    for row in rs:
        user_role_companies.append({
            'id': str_id(row['id']),
            'company_id': str_id(row['company_id']),
            'role': {
                'id': str_id(row['role_id']),
                'name': row['role_name']
            }
        })
    return user_role_companies


def query_user_enrollments(conn, user_id):
    """
    Return enrollments of the specified user.
    """
    rs = load_query(conn, user_id, 'users_enrollments.sql')

    enrollments = []
    for row in rs:
        enrollments.append({
            'id': str_id(row['id']),
            'company_id': str_id(row['company_id']),
            'mission_id': str_id(row['mission_id']),
            'mission_name': row['mission_name'],
            'category_id': str_id(row['category_id']),
            'category_name': row['category_name'],
            'status': row['status'],
            'points': row['points'],
            'performance': row['performance'],
            'rating': row['rating'],
            'activities_total_time': row['activities_total_time'].total_seconds() if row['activities_total_time'] else 0,
            'created_date': datetime_to_isoformat(row['created_date']),
            'updated_date': datetime_to_isoformat(row['updated_date']),
            'start_date': datetime_to_isoformat(row['start_date']),
            'end_date': datetime_to_isoformat(row['end_date'])
        })
    return enrollments


def query_user_activities(conn, user_id):
    """
    Return enrollments of the specified user.
    """
    rs = load_query(conn, user_id, 'users_activities.sql')

    activities = []
    for row in rs:
        activities.append({
            'id': str_id(row['id']),
            'pulse_id': str_id(row['pulse_id']),
            'mission_stage_content_id': str_id(row['mission_stage_content_id']),
            'stage_id': str_id(row['stage_id']),
            'mission_id': str_id(row['mission_id']),
            'kontent_id': str_id(row['kontent_id']),
            'company_ids': row['company_ids'],  # array
            'created_date': datetime_to_isoformat(row['created_date']),
            'start_date': datetime_to_isoformat(row['start_date']),
            'stop_date': datetime_to_isoformat(row['stop_date']),
            'time_in': row['time_in'].total_seconds() if row['time_in'] else None,
        })
    return activities


def query_user_channels(conn, user_id):
    """
    Return channels which the specified user is owner.
    """
    rs = load_query(conn, user_id, 'users_channels.sql')

    channels = []
    for row in rs:
        channels.append({
            'id': str_id(row['id']),
            'company_id': str_id(row['company_id']),
            'name': row['name'],
            'created_date': datetime_to_isoformat(row['created_date']),
            'updated_date': datetime_to_isoformat(row['updated_date']),
            'is_active': row['is_active'],
        })
    return channels


def query_user_courses(conn, user_id):
    """
    Return courses which the specified user is owner.
    """
    rs = load_query(conn, user_id, 'users_missions.sql')

    courses = []
    for row in rs:
        courses.append({
            'id': str_id(row['id']),
            'name': row['name'],
            'created_date': datetime_to_isoformat(row['created_date']),
            'updated_date': datetime_to_isoformat(row['updated_date']),
            'is_active': row['is_active'],
        })
    return courses


def query_stats_courses(conn, user_id):
    """
    Returns the statistics for courses of the speficied user.
    """
    rs = load_query(conn, user_id, 'users_stats_courses.sql')
    row = rs.fetchone()
    stats = {
        'total': getattr(row, 'enrollment_total', 0),
        'completed': getattr(row, 'enrollment_completed', 0),
        'completed_ratio': 0,
        'created': getattr(row, 'courses_created', 0),
        'contributed': getattr(row, 'courses_contributed', 0),
        'channels_created': getattr(row, 'channels_created', 0),
        'pulses_created': getattr(row, 'pulses_created', 0),
    }
    if stats['total']:
        stats['completed_ratio'] = stats['completed'] / stats['total']
    return stats


def query_stats_answers(conn, user_id):
    """
    Returns the statistics for answers of the speficied user.
    """
    rs = load_query(conn, user_id, 'users_stats_answers.sql')
    row = rs.fetchone()
    stats = {
        'total_exams': getattr(row, 'total_exams', 0),
        'total_questions': getattr(row, 'total_questions', 0),
        'total_answers': getattr(row, 'total_answers', 0),
        'correct_answers': getattr(row, 'correct_answers', 0),
        'correct_answers_ratio': 0,
    }

    if stats['total_answers']:
        stats['correct_answers_ratio'] = stats['correct_answers'] / stats['total_answers']

    return stats


def query_stats_activities(conn, user_id):
    """
    Returns the statistics for activities of the speficied user.
    """
    rs = load_query(conn, user_id, 'users_stats_activities.sql')
    row = rs.fetchone()
    stats = {
        'reference_date': datetime_to_isoformat(getattr(row, 'reference_date', None)),
        'total_missions_activities': getattr(row, 'total_missions_activities', 0),
        'total_pulses_activities': getattr(row, 'total_pulses_activities', 0),
        'missions_last_7_days': getattr(row, 'missions_last_7_days', 0),
        'missions_last_30_days': getattr(row, 'missions_last_30_days', 0),
        'missions_previous_last_7_days': getattr(row, 'missions_previous_last_7_days', 0),
        'missions_previous_last_30_days': getattr(row, 'missions_previous_last_30_days', 0),
        'pulses_last_7_days': getattr(row, 'pulses_last_7_days', 0),
        'pulses_last_30_days': getattr(row, 'pulses_last_30_days', 0),
        'pulses_previous_last_7_days': getattr(row, 'pulses_previous_last_7_days', 0),
        'pulses_previous_last_30_days': getattr(row, 'pulses_previous_last_30_days', 0),
    }
    return stats


def query_stats_course_categories(conn, user_id):
    """
    Returns the statistics for courses categories which the speficied user has enrollment.
    """
    rs = load_query(conn, user_id, 'users_stats_course_categories.sql')

    categories = []
    for row in rs:
        categories.append({
            'id': str_id(row['category_id']),
            'name': row['category_name'],
            'total': row['enrollments_total']
        })
    return categories


def query_stats_content_types(kq_conn, kt_conn, user_id):
    """
    Returns the statistics for content types which the speficied user has activity.
    """
    rs = load_query(kq_conn, user_id, 'users_stats_content_ids.sql')
    kontent_ids = [row['kontent_content_id'] for row in rs]
    if not len(kontent_ids):
        return []

    rs = load_query_kontent(kt_conn, kontent_ids, 'users_stats_kontent_types.sql')
    content_types = []
    for row in rs:
        content_types.append({
            'id': str_id(row['id']),
            'name': row['name'],
            'total': row['total']
        })
    return content_types


def populate_activities_content_types(conn, activities):
    """
    Fill-in content types for the specified activities.
    """
    kontent_ids = list()
    activities_map = dict()
    for activity in activities:
        if not activity['kontent_id']:  # pulse
            continue
        kontent_id = str_id(activity['kontent_id'])
        kontent_ids.append(kontent_id)
        activities_map[kontent_id] = activity

    if not kontent_ids:
        return

    rs = load_query_kontent(conn, kontent_ids, 'users_activities_kontent_types.sql')

    for row in rs:
        kontent_id = str_id(row['content_id'])
        activity = activities_map[kontent_id]
        activity.update({
            'content_name': row['content_name'],
            'content_type_id': str_id(row['content_type_id']),
            'content_type_name': row['content_type_name']
        })

    return activities


def load_query_kontent(kt_conn, kontent_ids, template_file_name):
    """
    Load SQL Statement for Kontent Ids.

    :param kt_conn: Connection to the Kontent database.
    :param kontent_ids: List of content ids from Kontent database.
    :param template_file_name: File name where SQL query is located (txt file)

    :return: SQL statement
    """
    file_message = f'{os.path.dirname(__file__)}/query_template/{template_file_name}'
    in_ids = ','.join([f"'{id}'" for id in kontent_ids])
    sql = Template(open(file_message, mode="r", encoding='utf-8').read()).substitute(kontent_ids=in_ids)
    rs = kt_conn.execute(sql)
    return rs


def str_id(id) -> str:
    if id is not None:
        return str(id)
    return None


def _log(message):
    if message:
        print(f'[{datetime.datetime.now()}] [Importer::User] {message}')


def _bulk_save(index_name, docs, size):
    _log(f'-- bulk saving {size} users and related docs...')
    docs = [doc.to_dict(True) for doc in docs]
    for doc in docs:
        doc.update({
            '_index': index_name,
            '_type': '_doc',  # version 6.x
        })
    if len(docs) <= 1.5 * BULK_SIZE:
        bulk(connections.get_connection(), docs)
    else:
        _log('-- splitting into smaller chunks...')
        for chunk in split_chunks(docs, BULK_SIZE):
            bulk(connections.get_connection(), chunk)
