SELECT
	me.id,
	me.user_id,
	me.company_id,
	m.id AS mission_id,
	m.name AS mission_name,
	mc.id AS category_id, 
	mc.name AS category_name,
	me.status,
	me.points,
	me.performance,
	me.created_date,
	me.start_date,
	me.end_date,
	me.updated_date,
	mr.rating,
	(
		SELECT SUM(lca.time_in)
		FROM learn_content_activity lca 
		JOIN mission_stage_content msc 
			ON msc.id = lca.mission_stage_content_id
		JOIN mission_stage ms 
			ON ms.id = msc.stage_id
		WHERE lca.user_id = me.user_id 
			AND ms.mission_id = me.mission_id
	) AS activities_total_time
FROM 
	mission_enrollment me
JOIN 
	mission m on m.id = me.mission_id
JOIN 
	mission_category mc on mc.id = m.mission_category_id
LEFT JOIN 
	mission_rating mr ON mr.mission_id = m.id 
	AND mr.user_id = me.user_id 
WHERE
	me.user_id = '$user_id'