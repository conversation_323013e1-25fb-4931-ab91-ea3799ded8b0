SELECT 
    lca.id, 
    lca.user_id, 
    lca.pulse_id,
    lca.mission_stage_content_id,
    msc.stage_id,
    ms.mission_id,
    COALESCE(msc.learn_content_uuid, p.learn_content_uuid) AS kontent_id,
    lca.action, 
    lca.time_start AS start_date, 
    lca.time_stop AS stop_date, 
    lca.time_in, 
    lca.created_date,
    ARRAY(
        SELECT DISTINCT c.company_id::TEXT
        FROM channel c  
        JOIN pulse_channel pc ON pc.channel_id = c.id 
        WHERE pc.pulse_id = lca.pulse_id
        
        UNION 
        
        SELECT DISTINCT mc.company_id::TEXT
        FROM mission_company mc 
        WHERE mc.mission_id = ms.mission_id 
    ) AS company_ids
FROM 
    learn_content_activity lca
LEFT JOIN 
    pulse p ON p.id = lca.pulse_id 
LEFT JOIN 
    mission_stage_content msc ON msc.id = lca.mission_stage_content_id
LEFT JOIN 
    mission_stage ms ON ms.id = msc.stage_id
WHERE 
    lca.user_id = '$user_id'