SELECT
	me.user_id,
	COUNT(*) AS enrollment_total,
	COUNT(CASE WHEN status = 'COMPLETED' THEN me.user_id END) AS enrollment_completed,
	( 
		SELECT COUNT(*)
		FROM mission m
		WHERE m.user_creator_id = me.user_id 
	) AS courses_created,
	( 
		SELECT COUNT(*)
		FROM mission_contributor mc 
		WHERE mc.user_id = me.user_id 
	) AS courses_contributed,
	( 
		SELECT COUNT(*)
		FROM channel c
		WHERE c.user_creator_id = me.user_id 
	) AS channels_created,
	( 
		SELECT COUNT(*)
		FROM pulse p
		WHERE p.user_creator_id = me.user_id 
	) AS pulses_created	
FROM mission_enrollment me
WHERE me.user_id = '$user_id'
GROUP BY me.user_id