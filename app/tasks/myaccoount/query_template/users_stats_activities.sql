SELECT 
	lca.user_id, 
	CURRENT_DATE AS reference_date,
	COUNT(lca.mission_stage_content_id) AS total_missions_activities, 
	COUNT(DISTINCT lca.mission_stage_content_id) AS total_unique_missions, 
	COUNT(lca.pulse_id) AS total_pulses_activities,
	COUNT(DISTINCT lca.pulse_id) AS total_unique_pulses,
	COUNT(
		CASE WHEN lca.created_date > current_date - 7 
		THEN lca.mission_stage_content_id END
	) AS missions_last_7_days,
	COUNT(
		CASE WHEN lca.created_date > current_date - 7 
		THEN lca.pulse_id END
	) AS pulses_last_7_days,
	COUNT(
		CASE WHEN lca.created_date > current_date - 30 
		THEN lca.mission_stage_content_id END
	) AS missions_last_30_days,
	COUNT(
		CASE WHEN lca.created_date > current_date - 30 
		THEN lca.pulse_id END
	) AS pulses_last_30_days,
	COUNT(
		CASE WHEN lca.created_date > current_date - 14 
		AND lca.created_date <= current_date - 7 
		THEN lca.mission_stage_content_id END
	) AS missions_previous_last_7_days,
	COUNT(
		CASE WHEN lca.created_date > current_date - 14
		AND lca.created_date <= current_date - 7 
		THEN lca.pulse_id END
	) AS pulses_previous_last_7_days,
	COUNT(
		CASE WHEN lca.created_date > current_date - 60 
		AND lca.created_date <= current_date - 30 
		THEN lca.mission_stage_content_id END
	) AS missions_previous_last_30_days,
	COUNT(
		CASE WHEN lca.created_date > current_date - 60 
		AND lca.created_date <= current_date - 30 
		THEN lca.pulse_id END
	) AS pulses_previous_last_30_days
FROM 
	learn_content_activity lca 
WHERE 
	lca.user_id = '$user_id'
GROUP BY 
	lca.user_id