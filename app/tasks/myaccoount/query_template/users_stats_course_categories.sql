SELECT 
	mission_enrollment.user_id, 
	mission_category.id AS category_id, 
	mission_category.name AS category_name,
	COUNT(mission_enrollment.id) AS enrollments_total
FROM mission_enrollment
JOIN mission ON mission.id = mission_enrollment.mission_id
JOIN mission_category ON mission_category.id = mission.mission_category_id
WHERE mission_enrollment.user_id = '$user_id'
GROUP BY 
	mission_enrollment.user_id,
	mission_category.id,
	mission_category.name