SELECT 
   mi.id as mission_id, 
   mi.name, 
   mi.description, 
   mi.holder_image, 
   mi.duration_time, 
   mi.points, 
   mi.created_date, 
   mi.updated_date, 
   mi.mission_category_id, 
   mi.mission_type_id, 
   mi.user_creator_id, 
   mi.is_active, 
   mi.development_status, 
   mi.thumb_image, 
   u.name as user_name, 
   u.status, 
   u.email, 
   u.avatar, 
   u.last_access_date, 
   mc.name as mission_category_name, 
   mt.name as mission_type_name 
FROM mission mi 
JOIN "user" u ON (u.id = mi.user_creator_id) 
LEFT JOIN mission_category mc ON (mc.id = mi.mission_category_id)
LEFT JOIN mission_type mt ON (mt.id = mi.mission_type_id) 
WHERE
    mi.id IN ($courses_ids)
