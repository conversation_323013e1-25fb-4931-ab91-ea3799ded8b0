SELECT 
	m.id as mission_id,
	(
		SELECT SUM(lca.time_in)
		FROM learn_content_activity lca
		JOIN mission_stage_content msc ON msc.id = lca.mission_stage_content_id
		JOIN mission_stage ms ON ms.id = msc.stage_id
		WHERE ms.mission_id = m.id
	) AS total_activity_seconds,
	(
		SELECT COUNT(*)
		FROM mission_comment mc 
		WHERE mc.mission_id = m.id
	) AS total_comments,
	(
		SELECT COUNT(*)
		FROM mission_rating mr 
		WHERE mr.mission_id = m.id
	) AS total_ratings,
	(
		SELECT COUNT(*)
		FROM mission_evaluation me 
		WHERE me.mission_id = m.id
	) AS total_evaluations
FROM 
	mission m 
WHERE 
	m.id = '$mission_id'
GROUP 
	BY m.id