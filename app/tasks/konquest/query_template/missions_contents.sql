SELECT 
   m.id AS mission_id,
   m.name AS mission_name,
   ms.id AS stage_id,
   ms.name AS stage_name,
   ms.order AS stage_order,
   msc.id AS content_id,
   msc.name AS content_name,
   msc.order AS content_order,
   msc.learn_content_uuid AS kontent_content_id,
   SUM(lca.time_in) AS activities_total_seconds,
   COUNT(DISTINCT lca.user_id) AS activities_total_users,
   COUNT(lca.id) AS activities_total_views
FROM mission_stage_content msc
JOIN mission_stage ms 
   ON ms.id = msc.stage_id
JOIN mission m 
   ON m.id = ms.mission_id
LEFT JOIN learn_content_activity lca 
   ON lca.mission_stage_content_id = msc.id
WHERE 
   m.id = '$mission_id'
GROUP BY 
   m.id, m.name, 
   ms.id, ms.name, ms.order, 
   msc.id, msc.name, msc.order, msc.learn_content_uuid 
ORDER BY 
   m.id, ms.order, msc.order