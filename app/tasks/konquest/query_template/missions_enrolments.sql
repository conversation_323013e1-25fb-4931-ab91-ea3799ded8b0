SELECT
	me.id,
	me.status,
	me.performance,
	me.points,
	me.created_date,
	me.updated_date,
	me.start_date,
	me.end_date,
	me.goal_date,
	me.give_up,
	me.give_up_comment,
	u.id AS user_id,
	u.name AS user_name,
	u.avatar AS user_avatar,
	mr.id AS rating_id,
	mr.rating 
FROM
	mission_enrollment me
JOIN 
	"user" u ON u.id = me.user_id
LEFT JOIN mission_rating mr 
	ON mr.mission_id = me.mission_id 
	AND mr.user_id = u.id
WHERE
	me.mission_id = '$mission_id'