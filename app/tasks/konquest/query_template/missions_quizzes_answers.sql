SELECT 
   mission.id as "mission_id", 
   answer.id as "answer.id", 
   answer.user_id as "answer.user", 
   answer.is_ok as "answer.hit", 
   answer.created_date as "answer.created_date" 
FROM answer 
JOIN question on answer.exam_has_question_id=question.id 
JOIN exam on question.exam_id=exam.id 
JOIN mission_stage on exam.stage_id=mission_stage.id 
JOIN mission on mission_stage.mission_id=mission.id 
WHERE mission.id = '$mission_id'