import datetime
import os
from string import Template

import database_konquest
import database_kontent
import pytz
from config.default import Config
from domain.common.utils import split_chunks
from domain.course.models import Course
from elasticsearch.helpers import bulk
from elasticsearch_dsl.connections import connections

BULK_SIZE = 300


def get_courses_ids():
    _log('-- Retrieving courses ids...')
    konquest_db_engine = database_konquest.engine
    with konquest_db_engine.connect() as kq_conn:
        rs = load_query(kq_conn, None, 'missions_ids.sql')
        return [row.id for row in rs]


def load_courses_data(index_name, courses_ids=None):
    """
    Load courses data from Konquest and save into ElasticSearch.
    This method could be called by API or periodic task routine.
    """
    brazil_now = datetime.datetime.now(pytz.timezone(Config.TIMEZONE))

    konquest_db_engine = database_konquest.engine
    kontent_db_engine = database_kontent.engine

    try:
        _log(f'[{brazil_now}] -- Running...')

        with konquest_db_engine.connect() as kq_conn,\
                kontent_db_engine.connect() as kt_conn:

            rs = None
            total_courses = 0

            # chunk
            if courses_ids:
                rs = load_query_for_courses_ids(kq_conn, courses_ids, 'missions_chunk.sql')
                total_courses = len(courses_ids)
                _log(f'-- Indexing {total_courses} courses for this chunk...')

            # all courses
            else:
                rs = load_query(kq_conn, None, 'missions.sql')
                total_courses = query_total_courses(kq_conn)
                _log(f'-- Indexing {total_courses} courses...')

            bulk_data = []

            for i, row in enumerate(rs):
                mission_id = str_id(row['mission_id'])
                _log(f'[{mission_id}] -- Processing {i+1} of {total_courses} ' + ('[chunk]' if courses_ids else '[all]'))
                owner_companies = query_mission_company(kq_conn, mission_id)

                _category = {
                    'id': str_id(row['mission_category_id']),
                    'name': row['mission_category_name']
                }

                _type = {
                    'id': str_id(row['mission_type_id']),
                    'name': row['mission_type_name']
                }

                _user = {
                    'id': str_id(row['user_creator_id']),
                    'user_name': row['user_name'],
                    'status': row['status'],
                    'email': row['email'],
                    'avatar': row['avatar'],
                    'last_access_date': datetime_to_isoformat(row['last_access_date'])
                }

                _contents = query_mission_contents(kq_conn, mission_id)
                populate_content_types(kt_conn, _contents)

                stats_enrollment = query_stats_enrollments(kq_conn, mission_id)
                stats_rating = query_stats_rating(kq_conn, mission_id)
                stats_content_types = query_stats_content_types(kq_conn, kt_conn, mission_id)
                stats_answers = query_stats_answers(kq_conn, mission_id)
                stats_general = query_stats_general(kq_conn, mission_id)
                stats_nps = query_stats_nps(kq_conn, mission_id)
                _stats = {
                    'updated_date': datetime_to_isoformat(brazil_now),
                    'enrollment': stats_enrollment,
                    'rating': stats_rating,
                    'content_types': stats_content_types,
                    'answers': stats_answers,
                    'general': stats_general,
                    'nps': stats_nps,
                }

                course = Course(
                    _id=mission_id,
                    course_id=mission_id,
                    origin='konquest',
                    model_type='course',
                    name=row['name'],
                    description=row['description'],
                    duration_time=row['duration_time'],
                    points=row['points'],
                    created_date=datetime_to_isoformat(row['created_date']),
                    updated_date=datetime_to_isoformat(row['updated_date']),
                    is_active=row['is_active'],
                    development_status=row['development_status'],
                    owner_companies=owner_companies,
                    user=_user,
                    course_type=_type,
                    course_category=_category,
                    course_stats=_stats,
                    course_contents=_contents
                )
                bulk_data.append(course)

                enrollments = query_mission_enrollment(kq_conn, mission_id, owner_companies)
                ratings = query_mission_rating(kq_conn, mission_id, owner_companies)
                bookmarks = query_mission_bookmark(kq_conn, mission_id, owner_companies)
                activities = query_mission_content_activity(kq_conn, mission_id, owner_companies)
                anwers = query_mission_exam_answer(kq_conn, mission_id, owner_companies)

                bulk_data.extend(enrollments)
                bulk_data.extend(ratings)
                bulk_data.extend(bookmarks)
                bulk_data.extend(activities)
                bulk_data.extend(anwers)

                # Bulk save every BULK_SIZE docs
                if len(bulk_data) >= BULK_SIZE:
                    _bulk_save(index_name, bulk_data)
                    bulk_data = []

            # Bulk save remaning docs
            if len(bulk_data):
                _bulk_save(index_name, bulk_data)

        _log(f'[{brazil_now}] -- Ending...')

    except Exception as e:
        _log(e)
        raise e


def query_mission_contents(kq_conn, mission_id):
    """
    Return the contents of the specified mission.
    """
    brazil_now = datetime.datetime.now(pytz.timezone(Config.TIMEZONE))

    rs = load_query(kq_conn, mission_id, 'missions_contents.sql')

    contents = []
    for row in rs:
        contents.append({
            'stage_id': str_id(row['stage_id']),
            'stage_name': row['stage_name'],
            'stage_order': row['stage_order'],
            'content_id': str_id(row['content_id']),
            'content_name': row['content_name'],
            'content_order': row['content_order'],
            'kontent_id': row['kontent_content_id'],
            'activity_stats': {
                'updated_date': datetime_to_isoformat(brazil_now),
                'total_seconds': row['activities_total_seconds'].total_seconds() if row['activities_total_seconds'] else None,
                'total_users': row['activities_total_users'],
                'total_views': row['activities_total_views'],
            }
        })
    return contents


def populate_content_types(kt_conn, contents):
    """
    Fill-in content types for the specified contents.
    """
    kontent_ids = list()
    content_map = dict()
    for content in contents:
        if not content['kontent_id']:
            continue
        kontent_id = str_id(content['kontent_id'])
        kontent_ids.append(kontent_id)
        content_map[kontent_id] = content

    if not kontent_ids:
        return

    rs = load_query_kontent(kt_conn, kontent_ids, 'missions_contents_kontent_type.sql')

    for row in rs:
        kontent_id = str_id(row['learn_content_id'])
        content = content_map[kontent_id]
        content.update({
            'content_type_id': str_id(row['content_type_id']),
            'content_type_name': row['content_type_name']
        })

    return contents


def query_mission_enrollment(con, mission_id, owner_companies):
    """
    List all mission's enrolments.

    :param con: Database connections
    :param mission_id: Mission UUID
    :param owner_companies: Dict with all companies linked

    :return: Dict with mission enrolments
    """
    rs = load_query(con, mission_id, 'missions_enrolments.sql')

    bulk_data = []
    for row in rs:
        row_id = str_id(row['id'])
        _enrollment = {
            'id': row_id,
            'points': row['points'],
            'start_date': datetime_to_isoformat(row['start_date']),
            'end_date': datetime_to_isoformat(row['end_date']),
            'goal_date': datetime_to_isoformat(row['goal_date']),
            'give_up': row['give_up'],
            'give_up_comment': row['give_up_comment'],
            'status': row['status'],
            'performance': row['performance']
        }
        _user = {
            'id': str_id(row['user_id']),
            'user_name': row['user_name'],
            'avatar': row['user_avatar'],
        }
        _rating = {
            'id': str_id(row['rating_id']),
            'rating': row['rating'],
        }
        enrollment = Course(
            _id=row_id,
            origin='konquest',
            model_type='enrollment',
            course_id=mission_id,
            owner_companies=owner_companies,
            created_date=datetime_to_isoformat(row['created_date']),
            updated_date=datetime_to_isoformat(row['updated_date']),
            enrollment=_enrollment,
            user=_user,
            rating=_rating,
        )

        # enrollment.save(index=index_name)
        bulk_data.append(enrollment)

    return bulk_data


def query_mission_rating(con, mission_id, owner_companies):
    """
    List all mission's ratings.

    :param con: Database connections
    :param mission_id: Mission UUID
    :param owner_companies: Dict with all companies linked

    :return: Dict with mission ratings
    """
    rs = load_query(con, mission_id, 'missions_ratings.sql')

    bulk_data = []
    for row in rs:
        row_id = str_id(row['id'])
        _rating = {
            'id': row_id,
            'rating': row['rating'],
        }

        rating = Course(
            _id=row_id,
            origin='konquest',
            model_type='rating',
            course_id=mission_id,
            owner_companies=owner_companies,
            created_date=datetime_to_isoformat(row['created_date']),
            updated_date=datetime_to_isoformat(row['updated_date']),
            user=dict(id=str_id(row['user_id'])),
            rating=_rating
        )

        # rating.save(index=index_name)
        bulk_data.append(rating)

    return bulk_data


def query_mission_bookmark(con, mission_id, owner_companies):
    """
    List all mission's bookmarks.

    :param con: Database connections
    :param mission_id: Mission UUID
    :param owner_companies: Dict with all companies linked

    :return: Dict with mission bookmarks
    """
    rs = load_query(con, mission_id, 'missions_bookmarks.sql')

    bulk_data = []
    for row in rs:
        row_id = str_id(row['id'])
        _bookmark = {
            'id': row_id,
        }

        bookmark = Course(
            _id=row_id,
            origin='konquest',
            model_type='rating',
            course_id=mission_id,
            owner_companies=owner_companies,
            created_date=datetime_to_isoformat(row['created_date']),
            updated_date=datetime_to_isoformat(row['updated_date']),
            user=dict(id=str_id(row['user_id'])),
            bookmark=_bookmark
        )

        # bookmark.save(index=index_name)
        bulk_data.append(bookmark)

    return bulk_data


def query_mission_content_activity(con, mission_id, owner_companies):
    """
    List all mission's users.sql activities.

    :param con: Database connections
    :param mission_id: Mission UUID
    :param owner_companies: Dict with all companies linked

    :return: Dict with missions users.sql activities
    """
    rs = load_query(con, mission_id, 'missions_activities.sql')

    bulk_data = []
    for row in rs:
        row_id = str_id(row['activity.id'])
        _activity = {
            'id': row_id,
            'action': str(row['activity.action']),
            'start_date': datetime_to_isoformat(row['activity.start_date']),
            'stop_date': datetime_to_isoformat(row['activity.stop_date']),
            'time_in': row['activity.time_in'].total_seconds(),
            'mission_stage_content_id': str_id(row['mission_stage_content_id']),
        }

        activity = Course(
            _id=row_id,
            origin='konquest',
            model_type='course_activity',
            course_id=mission_id,
            owner_companies=owner_companies,
            created_date=datetime_to_isoformat(row['created_date']),
            user=dict(id=str(row['activity.user'])),
            activity=_activity
        )

        # activity.save(index=index_name)
        bulk_data.append(activity)

    return bulk_data


def query_mission_exam_answer(con, mission_id, owner_companies):
    """
    List all mission's quizzes answers.

    :param con: Database connections
    :param mission_id: Mission UUID
    :param owner_companies: Dict with all companies linked

    :return: Dict with mission's quizzes answers.
    """
    rs = load_query(con, mission_id, 'missions_quizzes_answers.sql')

    bulk_data = []
    for row in rs:
        row_id = str_id(row['answer.id'])
        _answer = {
            'id': row_id,
            'hit': str(row['answer.hit']),
        }

        answer = Course(
            _id=row_id,
            origin='konquest',
            model_type='answer',
            course_id=mission_id,
            owner_companies=owner_companies,
            created_date=datetime_to_isoformat(row['answer.created_date']),
            user=dict(id=str(row['answer.user'])),
            answer=_answer
        )

        # answer.save(index=index_name)
        bulk_data.append(answer)

    return bulk_data


def datetime_to_isoformat(_datetime):
    return _datetime.isoformat() if _datetime is not None else None


def load_query(con, mission_id, template_file_name):
    """
    Load SQL Statement for missions.

    :param con: Database connections
    :param mission_id: Mission UUID (could be None)
    :param template_file_name: File name where SQL query is located (txt file)

    :return: SQL statement
    """
    file_message = f'{os.path.dirname(__file__)}/query_template/{template_file_name}'
    sql = Template(open(file_message, mode="r", encoding='utf-8').read()).substitute(mission_id=mission_id)
    rs = con.execute(sql)
    return rs


def load_query_for_courses_ids(conn, courses_ids, template_file_name):
    """
    Load SQL Statement for specific courses (in clause).

    :param conn: Database connection
    :param courses_ids: List of courses ids.
    :param template_file_name: File name where SQL query is located (txt file)

    :return: SQL statement
    """
    file_message = f'{os.path.dirname(__file__)}/query_template/{template_file_name}'
    in_ids = ','.join([f"'{id}'" for id in courses_ids])
    sql = Template(open(file_message, mode="r", encoding='utf-8').read()).substitute(courses_ids=in_ids)
    rs = conn.execute(sql)
    return rs


def query_total_courses(conn):
    """
    Return the total courses.
    """
    rs = load_query(conn, None, 'missions_total.sql')
    row = rs.fetchone()
    return row['total'] if row else 0


def query_mission_company(conn, mission_id):
    """
    List companies linked with these mission.
    The mission must have one companies as owner and should have many companies (shared)

    :param conn: Database connections
    :param mission_id: Mission UUID

    :return: Dict with companies missions linked
    """
    rs = load_query(conn, mission_id, 'missions_companies.sql')
    companies = []

    for i, row in enumerate(rs):
        company = {
            'id': str_id(row['id']),
            'created_date': datetime_to_isoformat(row['created_date']),
            'updated_date': datetime_to_isoformat(row['updated_date']),
            'company_id': str_id(row['company_id']),
            'relationship_type': row['relationship_type']
        }

        companies.append(company)

    return companies


def query_stats_enrollments(conn, mission_id):
    """
    Returns the statistics for enrollments of the speficied mission.
    """
    rs = load_query(conn, mission_id, 'missions_stats_enrollments.sql')
    row = rs.fetchone()
    stats = {
        'total': getattr(row, 'enrollment_total', 0),
        'started': getattr(row, 'enrollment_started', 0),
        'completed': getattr(row, 'enrollment_completed', 0),
        'give_up': getattr(row, 'enrollment_give_up', 0),
        'completed_ratio': 0,
    }
    if stats['total']:
        ratio = stats['completed'] / stats['total']
        stats['completed_ratio'] = ratio
    return stats


def query_stats_rating(conn, mission_id):
    """
    Returns the statistics for ratings of the speficied mission.
    """
    rs = load_query(conn, mission_id, 'missions_stats_ratings.sql')
    row = rs.fetchone()
    stats = {
        'average': getattr(row, 'rating_average', 0),
        'total': getattr(row, 'rating_total', 0)
    }
    return stats


def query_stats_content_types(kq_conn, kt_conn, mission_id):
    """
    Returns the statistics for content of the specified mission.
    """
    rs = load_query(kq_conn, mission_id, 'missions_stats_content_ids.sql')
    kontent_ids = [row['kontent_content_id'] for row in rs]
    if not len(kontent_ids):
        return []

    rs = load_query_kontent(kt_conn, kontent_ids, 'missions_stats_kontent_types.sql')
    content_types = []
    for row in rs:
        content_types.append({
            'id': str_id(row['id']),
            'name': row['name'],
            'total': row['total']
        })
    return content_types


def query_stats_answers(conn, mission_id):
    """
    Returns the statistics for answers of the speficied mission.
    """
    rs = load_query(conn, mission_id, 'missions_stats_answers.sql')
    row = rs.fetchone()

    stats = {
        'total_exams': getattr(row, 'total_exams', 0),
        'total_questions': getattr(row, 'total_questions', 0),
        'total_answers': getattr(row, 'total_answers', 0),
        'correct_answers': getattr(row, 'correct_answers', 0),
        'correct_ratio': 0,
    }
    if stats['total_answers']:
        ratio = stats['correct_answers'] / stats['total_answers']
        stats['correct_ratio'] = ratio
    return stats


def query_stats_general(conn, mission_id):
    """
    Returns general statistics of the speficied mission.
    """
    rs = load_query(conn, mission_id, 'missions_stats_general.sql')
    row = rs.fetchone()

    total_activity_seconds = 0.0
    if getattr(row, 'total_activity_seconds', None):
        total_activity_seconds = row['total_activity_seconds'].total_seconds()

    stats = {
        'total_activity_seconds': total_activity_seconds,
        'total_comments': getattr(row, 'total_comments', 0),
        'total_ratings': getattr(row, 'total_ratings', 0),
        'total_evaluations': getattr(row, 'total_evaluations', 0),
        'total_feedbacks': 0,
    }
    stats['total_feedbacks'] = stats['total_ratings'] + stats['total_evaluations']
    return stats


def query_stats_nps(conn, mission_id):
    """
    Returns nps statistics of the speficied mission.
    """
    rs = load_query(conn, mission_id, 'missions_stats_nps.sql')
    row = rs.fetchone()

    stats = {
        'total': getattr(row, 'total', 0),
        'cons': getattr(row, 'cons', 0),
        'neutrals': getattr(row, 'neutrals', 0),
        'pros': getattr(row, 'pros', 0),
    }
    return stats


def load_query_kontent(kt_conn, kontent_ids, template_file_name):
    """
    Load SQL Statement for Kontent Ids.

    :param kt_conn: Connection to the Kontent database.
    :param kontent_ids: List of content ids from Kontent database.
    :param template_file_name: File name where SQL query is located (txt file)

    :return: SQL statement
    """
    file_message = f'{os.path.dirname(__file__)}/query_template/{template_file_name}'
    in_ids = ','.join([f"'{id}'" for id in kontent_ids])
    sql = Template(open(file_message, mode="r", encoding='utf-8').read()).substitute(kontent_ids=in_ids)
    rs = kt_conn.execute(sql)
    return rs


def _log(message):
    print(f'[{datetime.datetime.now()}] [Importer::Course] {message}')


def _bulk_save(index_name, docs):
    _log(f'-- bulk saving {len(docs)} courses and related docs...')
    docs = [doc.to_dict(True) for doc in docs]
    for doc in docs:
        doc.update({
            '_index': index_name,
            '_type': '_doc',  # version 6.x
        })
    if len(docs) <= 1.5 * BULK_SIZE:
        bulk(connections.get_connection(), docs)
    else:
        _log('-- splitting into smaller chunks...')
        for chunk in split_chunks(docs, BULK_SIZE):
            bulk(connections.get_connection(), chunk)


def str_id(id) -> str:
    if id is not None:
        return str(id)
    return None
