from contextlib import contextmanager
from datetime import datetime, timedelta

import database
from config.base_task import ProcessReportBaseTask
from config.celery import celery
from config.constants import PROCESS_REPORT_TASK
from config.default import Config
from custom.discord_webhook_logger import DiscordWebhook<PERSON>ogger
from domain.notification.services import NotificationService
from domain.report.models import Report
from elasticsearch import Elasticsearch
from keeps_service.email_service.notification import notify_users
from reports.di import ReportContainer
from reports.export import ExportBuilder
from reports.presentation import PresentationService
from sqlalchemy.orm import Session


def notification_service() -> NotificationService:
    es = Elasticsearch(Config.ELASTICSEARCH_URL, http_auth=Config.ELASTICSEARCH_AUTH)
    return NotificationService(logger=None, es=es)


def notify_user(report: Report) -> None:
    email_data = {
        "user_name": report.user_creator.get("name", ""),
        "report_name": report.report_type.name,
        "report_url": report.url,
    }
    receiver = {
        "email": report.user_creator.get("email"),
        "language": report.language,
        "email_verified": True
    }
    notify_users.delay(email_data, "new_report", [receiver])


def logger(method_name: str, error: BaseException) -> None:
    title = f'Report Process Task Error. {method_name}'
    DiscordWebhookLogger().emit_short_message(title, error)


@contextmanager
def task_transaction(method_name, identifier=None):
    container = ReportContainer()
    try:
        yield container
    except Exception as error:
        database.session.rollback()
        raise error


def _process_report(presentation_generators: dict, report: Report, session: Session) -> None:
    generator = presentation_generators[report.report_type.name]()
    if not isinstance(generator, PresentationService) and not isinstance(generator, ExportBuilder):
        raise ValueError(f"{type(generator)} invalid generator service, configure the get_report_services_map in the ReportContainer")
    generator.generate(report)
    session.commit()
    try:
        notify_user(report)
    except Exception as error:
        logger(f"error to notify user: report_id: {report.id}", error)


@celery.task(name=PROCESS_REPORT_TASK, base=ProcessReportBaseTask)
def process_report(report_id: str) -> None:
    with task_transaction("process_export") as container:
        report_services = container.get_report_services_map()
        session = database.session
        report = session.query(Report).get(report_id)
        report.status = "PROCESSING"
        session.commit()
        try:
            _process_report(report_services, report, session)
        except Exception as error:
            session.rollback()
            report.status = "ERROR"
            session.commit()
            logger(f"process_exports: report_id: {report.id}", error)


def get_report_raw_data(report: Report) -> None:
    with task_transaction("process_export") as container:
        report_services = container.get_report_services_map()
        generator = report_services[report.report_type.name]()
        return generator.get_raw_data(report)


def delete_older_reports() -> None:
    with task_transaction("delete_older_reports"):
        session = database.session
        now = datetime.now()
        some_days_ago = now - timedelta(days=Config.REPORT_LIFE_TIME_DAY)
        session.query(
            Report
        ).filter(
            Report.created < some_days_ago
        ).delete()
        session.commit()

def cancel_older_processing_reports() -> None:
    with task_transaction("error_when_processing"):
        session = database.session
        time = datetime.now()
        one_hour_ago = time - timedelta(minutes=60)
        two_hours_ago = time - timedelta(hours=2)

        reports = session.query(Report).filter(
            Report.created >= two_hours_ago,
            Report.created < one_hour_ago,
            Report.status == 'PROCESSING'
        ).all()
        try:
            for report in reports:
                report.status = 'ERROR'
                session.commit()
        except Exception as error:
            logger(f"error to cancel processing report: report_id: {report.id}", error)
        finally:
            session.close()
