from __future__ import absolute_import

import datetime
import glob
import os

from celery import Task
from celery.utils.log import get_task_logger
from config.celery import celery
from config.default import Config
from custom.discord_webhook_logger import DiscordWebhookLogger
from domain.common.utils import split_chunks
from domain.course.mapping import course_mapping
from domain.user.mapping import user_mapping
from elasticsearch import Elasticsearch
from elasticsearch_dsl.connections import connections
from reports.di import ReportContainer
from tasks.konquest.course import get_courses_ids, load_courses_data
from tasks.myaccoount.user import get_users_ids, load_users_data

INDEX_SUFFIX_1 = '_a'
INDEX_SUFFIX_2 = '_b'
TASK_CHUNK_SIZE = 100

logger = get_task_logger(__name__)

connections.create_connection(hosts=[Config.ELASTICSEARCH_URL], http_auth=Config.ELASTICSEARCH_AUTH)
es = Elasticsearch(Config.ELASTICSEARCH_URL, http_auth=Config.ELASTICSEARCH_AUTH)


def build_container():
    container = ReportContainer()
    return container


class RetryTask(Task):
    autoretry_for = (Exception,)
    max_retries = 3
    default_retry_delay = 180


@celery.task(base=RetryTask)
def task_indexer_courses():
    logger.info('*** Courses indexer started ***')
    start = datetime.datetime.now().isoformat()
    index = Config.ELASTICSEARCH_INDEX_COURSES_V1

    courses_ids = get_courses_ids()

    if not courses_ids:
        logger.info('Nothing to index, terminating.')
        return

    info = {
        'task': 'index-courses',
        'start': start,
        'index': index,
        'index_new': None,
        'index_old': None,
        'total': len(courses_ids),
    }

    logger.info(f'Indexing {len(courses_ids)} courses...')
    _task_indexer(info, courses_ids, course_mapping, import_courses_chunk)


@celery.task(base=RetryTask)
def task_indexer_users():
    logger.info('*** Users indexer started ***')
    start = datetime.datetime.now().isoformat()
    index = Config.ELASTICSEARCH_INDEX_USERS_V1

    users_ids = get_users_ids()

    if not users_ids:
        logger.info('Nothing to index, terminating.')
        return

    info = {
        'task': 'index-users',
        'start': start,
        'index': index,
        'index_new': None,
        'index_old': None,
        'total': len(users_ids),
    }

    logger.info(f'Indexing {len(users_ids)} users...')
    _task_indexer(info, users_ids, user_mapping, import_users_chunk)


def _task_indexer(info, docs_ids, mapping, partial_task):
    index_alias = info['index']
    index_new = index_alias + INDEX_SUFFIX_1
    index_old = index_alias + INDEX_SUFFIX_2
    info.update({
        'index_new': index_new,
        'index_old': index_old,
    })

    try:
        # Retrieve old index from alias
        if es.indices.exists_alias(name=index_alias):
            logger.info('alias exists, retrieving old index')
            existing_alias = es.indices.get_alias(name=index_alias)
            index_old = next(iter(existing_alias))

            # change new index name if same as old
            if index_new == index_old:
                index_new = index_alias + INDEX_SUFFIX_2

            info.update({
                'index_new': index_new,
                'index_old': index_old,
            })

            logger.info('alias info: %s', existing_alias)
            logger.info('old index: %s', index_old)
            logger.info('new index: %s', index_new)

        # First run with index instead of alias
        elif es.indices.exists(index=index_alias):
            logger.info('index exists in place of alias, cloning old index')
            es_version = es.info()['version']['number']

            if int(es_version.split('.')[0]) >= 7:
                es.indices.put_settings(index=index_alias, body={'index.blocks.write': True})
                es.indices.delete(index=index_old, ignore=[404])
                es.indices.clone(index=index_alias, target=index_old)
                es.indices.delete(index=index_alias)
                es.indices.put_alias(index=index_old, name=index_alias)
            else:
                logger.warning('cloning not supported in current version: %s', es_version)
                es.indices.delete(index=index_alias)

        logger.info('creating new index: %s', index_new)
        es.indices.delete(index=index_new, ignore=[404])
        es.indices.create(index=index_new, body=mapping)

        # build task chain
        chain = None
        for chunk in split_chunks(docs_ids, TASK_CHUNK_SIZE):
            if not chain:
                chain = partial_task.s(0, info, chunk)
            else:
                chain = chain | partial_task.s(info, chunk)
        chain = chain | commit_index.s(info)
        chain = chain.on_error(import_error.s(info))
        chain.delay()

    except Exception as e:
        logger.error('Error preparing new index, discarding...', exc_info=e)
        es.indices.delete(index=info['index_new'], ignore=[404])
        raise e


@celery.task
def commit_index(prev_result, info):
    logger.info('updating alias: ' + info['index'] + ' -> ' + info['index_new'])
    es.indices.put_alias(index=info['index_new'], name=info['index'])
    es.indices.delete_alias(index=info['index_old'], name=info['index'], ignore=[404])

    logger.info('deleting old index: ' + info['index_old'])
    es.indices.delete(index=info['index_old'], ignore=[404])

    now = datetime.datetime.now()
    duration = now - datetime.datetime.fromisoformat(info['start'])
    duration_m = duration.total_seconds() // 60

    logger.info(
        '\n*** [%s] import finished successfully! *** \n -- started at  %s  \n -- finished at %s \n -- took %d minutes',
        info['task'],
        info['start'],
        now.isoformat(),
        duration_m
    )


@celery.task
def import_error(request, exc, traceback, info):
    logger.error('Error importing data, discarding new index...', exc_info=exc)
    DiscordWebhookLogger().emit_short_message('Celery import error - ' + info['task'], exc)
    es.indices.delete(index=info['index_new'], ignore=[404])


@celery.task(base=RetryTask)
def import_courses_chunk(prev_result, info, courses_ids):
    load_courses_data(info['index_new'], courses_ids)
    chunk_count = len(courses_ids)
    progress_count = chunk_count + prev_result
    total_count = info['total']
    logger.info(f'>>> imported {chunk_count} on this chunk ({progress_count} of {total_count})')
    return progress_count


@celery.task(base=RetryTask)
def import_users_chunk(prev_result, info, users_ids):
    load_users_data(info['index_new'], users_ids)
    chunk_count = len(users_ids)
    progress_count = chunk_count + prev_result
    total_count = info['total']
    logger.info(f'>>> imported {chunk_count} on this chunk ({progress_count} of {total_count})')
    return progress_count


@celery.task()
def delete_report_temp_files():
    files = glob.glob(f'{Config.BASE_DIR}/reports/temp/*')
    for f in files:
        created_date = datetime.datetime.strptime(datetime.datetime.fromtimestamp(os.path.getctime(f)).strftime('%Y-%m-%d'),
                                                  '%Y-%m-%d').date()
        today = datetime.date.today()
        if created_date < today:
            os.remove(f)


# ### TEST SANDBOX ###
"""
def _random_error():
    if random.random() < 0.3:
        logger.warning('Error surprise!!!')
        raise Exception('surprise!')

@celery.task
def test(base=RetryTask):
    logger.info('*** import started ***')
    start = datetime.now()
    users_ids = get_users_ids()[0:21]
    info = {"start": start, "total": len(users_ids)}
    logger.info('Importing %s users', info["total"])
    # test_chunk(info, users_ids)
    # test_split(info, users_ids)
    test_chain(info, users_ids)


def test_chunk(info, users_ids):
    chunk = test_indexer_single.chunks(((info, user_id) for user_id in users_ids), 20)
    logger.info('Chunk: %s', chunk)
    cr = chunk()
    logger.info('Chunk Result: %s', cr)
    # group = chunk.group()
    # logger.info('Group: %s', group)
    # gr = group()
    # logger.info('Group Result: %s', gr)
    # chord = chunk | test_indexer_finally.s(shared_data)
    # logger.info('Chord: %s', chord)
    # cr = chord()
    # logger.info('Chord Result: %s', cr.state)

def test_split(info, users_ids):
    g = group(test_indexer_chunk.s(info, chunk) for chunk in split_chunks(users_ids, 5))
    logger.info('g: %s', g)
    # gr = g()
    # logger.info(gr)
    c = g | test_indexer_finally.s(info)
    logger.info('c: %s', c)
    c = c.on_error(task_error.s(info))
    cr = c()
    logger.info('cr: %s', cr)


def test_chain(info, users_ids):
    chain = None
    for chunk in split_chunks(users_ids, 5):
        if not chain:
            chain = test_indexer_chain.s(0, info, chunk)
        else:
            chain = chain | test_indexer_chain.s(info, chunk)
    chain = chain | test_indexer_finally.s(info)
    logger.info('c: %s', chain)
    chain = chain.on_error(task_error.s(info))
    cr = chain()
    logger.info('cr: %s', cr)


@celery.task(base=RetryTask)
def test_indexer_single(info, user_id):
    logger.info('Task Single (%s) :: %s ', type(user_id), user_id)
    _random_error()
    time.sleep(3)
    return 1

@celery.task(base=RetryTask)
def test_indexer_chunk(info, users_ids):
    logger.info('Task Chunk (%s)[%d] :: %s ', type(users_ids), len(users_ids), users_ids)
    logger.info('-- info: %s ', info)
    _random_error()
    time.sleep(3)
    return len(users_ids)


@celery.task(base=RetryTask)
def test_indexer_chain(prev_result, info, users_ids):
    logger.info('Task Chain (%s)[%d] :: %s ', type(users_ids), len(users_ids), users_ids)
    logger.info('-- prev_result: %s', prev_result)
    logger.info('-- info: %s ', info)
    _random_error()
    time.sleep(3)
    return len(users_ids) + prev_result


@celery.task(base=RetryTask, max_retries=0)
def test_indexer_finally(results, info):
    logger.info('Finally: %s', results)
    logger.info('-- info: %s ', info)


@celery.task
def task_error(request, exc, traceback, info):
    logger.error('error: [\n--- info=%s,\n--- exc=%s,\n--- request=%s,\n--- traceback=%s] ', info, request, exc, traceback)



@celery.task
def simple_task(x, y):
    logger.info('x=%s & y=%s', x, y)
    print(f'[Celery] [simple_task] x={x} & y={y}')
    return x + y


@celery.task
def long_task(x, y):
    logger.info('x=%s & y=%s', x, y)
    print(f'[Celery] [long_task] x={x} & y={y} ... ')
    time.sleep(3)
    result = x + y
    print(f'[Celery] [long_task] ... result={result}')
    return result

@celery.task
def very_long_task(x, y):
    logger.info('x=%s & y=%s', x, y)
    print(f'[Celery] [very_long_task] x={x} & y={y} ... ')
    time.sleep(10)
    result = x * y
    print(f'[Celery] [very_long_task] ... result={result}')
    return result

@celery.task()
def task_with_error(x, y):
    logger.warning('x=%s & y=%s', x, y)
    print(f'[Celery] [task_with_error] x={x} & y={y} ... ')
    time.sleep(3)
    raise Exception('Something wrong')


@celery.task()
def check_conf():
    logger.info('APP_NAME=%s', Config.APP_NAME)
    logger.info('ELASTICSEARCH_URL=%s', Config.ELASTICSEARCH_URL)
    logger.info('ELASTICSEARCH_INDEX_COURSES_V1=%s', Config.ELASTICSEARCH_INDEX_COURSES_V1)
    logger.info('ELASTICSEARCH_INDEX_USERS_V1=%s', Config.ELASTICSEARCH_INDEX_USERS_V1)

    logger.info('CELERY_BROKER_URL=%s', Config.CELERY_BROKER_URL)
    logger.info('CELERY_DEFAULT_QUEUE=%s', Config.CELERY_DEFAULT_QUEUE)

    logger.info('DATABASE_KONQUEST_URL=%s', Config.DATABASE_KONQUEST_URL)
    logger.info('DATABASE_MYACCOUNT_URL=%s', Config.DATABASE_MYACCOUNT_URL)
"""

if __name__ == '__main__':
    celery.worker_main()
