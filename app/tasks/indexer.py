from datetime import datetime

from config.default import Config
from domain.course.mapping import course_mapping
from domain.user.mapping import user_mapping
from elasticsearch import Elasticsearch
from tasks.konquest.course import load_courses_data
from tasks.myaccoount.user import load_users_data

INDEX_SUFFIX_1 = '_a'
INDEX_SUFFIX_2 = '_b'


def task_indexer_courses():
    task_indexer(index=Config.ELASTICSEARCH_INDEX_COURSES_V1, mapping=course_mapping, load_callback=load_courses_data)


def task_user_indexer():
    task_indexer(index=Config.ELASTICSEARCH_INDEX_USERS_V1, mapping=user_mapping, load_callback=load_users_data)


def task_indexer(index, mapping, load_callback):
    _log(index, '*** import started ***')
    start = datetime.now()
    error = None
    index_alias = index
    index_new = index_alias + INDEX_SUFFIX_1
    index_old = index_alias + INDEX_SUFFIX_2

    try:
        es = Elasticsearch(Config.ELASTIC<PERSON><PERSON><PERSON>_URL, http_auth=Config.ELASTICSEARCH_AUTH)

        # Retrieve old index from alias
        if es.indices.exists_alias(name=index_alias):
            _log(index, 'alias exists, retrieving old index')
            existing_alias = es.indices.get_alias(name=index_alias)
            index_old = next(iter(existing_alias))

            # change new index name if same as old
            if index_new == index_old:
                index_new = index_alias + INDEX_SUFFIX_2

            _log(index, 'alias info: ' + str(existing_alias))
            _log(index, 'old index: ' + str(index_old))
            _log(index, 'new index: ' + str(index_new))

        # First run with index instead of alias
        elif es.indices.exists(index=index_alias):
            _log(index, 'index exists in place of alias, cloning old index')
            es_version = es.info()['version']['number']

            if int(es_version.split('.')[0]) >= 7:
                es.indices.put_settings(index=index_alias, body={'index.blocks.write': True})
                es.indices.delete(index=index_old, ignore=[404])
                es.indices.clone(index=index_alias, target=index_old)
                es.indices.delete(index=index_alias)
                es.indices.put_alias(index=index_old, name=index_alias)
            else:
                _log(index, 'WARNING - cloning not supported in current version: ' + str(es_version))
                es.indices.delete(index=index_alias)

        _log(index, 'queued new index: ' + index_new)
        es.indices.delete(index=index_new, ignore=[404])
        es.indices.create(index=index_new, body=mapping)

        _log(index, 'loading data')
        load_callback(index_new)

    except Exception as e:
        _log(index, 'error: ' + str(e))
        es.indices.delete(index=index_new, ignore=[404])
        error = e
        raise e

    else:
        _log(index, 'updating alias: ' + index_alias + ' -> ' + index_new)
        es.indices.put_alias(index=index_new, name=index_alias)
        es.indices.delete_alias(index=index_old, name=index_alias, ignore=[404])

        _log(index, 'deleting old index: ' + index_old)
        es.indices.delete(index=index_old, ignore=[404])

    finally:
        msg = 'successfully' if not error else f'with error [{error.__class__.__name__}]'
        _log(index, f'*** import finished {msg} *** / started at {start}')


def _log(index, message):
    print(f'[{datetime.now()}] [Task] [{index}] {message}')
