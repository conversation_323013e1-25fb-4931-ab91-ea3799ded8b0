from datetime import datetime, timedelta
from unittest.mock import MagicMock

import pytest
from pandas import DataFrame

from config.default import Config
from domain.report.models import Report
from reports.di import ReportContainer
from reports.export import ExportBuilder, ExportPart
from reports.presentation import PresentationService, PresentationPage
from tasks.report import delete_older_reports, process_report, task_transaction
import database as database_file


GET_PRESENTATION_SERVICES_MAP = 'reports.di.ReportContainer.get_report_services_map'
PRESENTATION_CREATE = 'reports.presentation.PresentationCreator.create'
EXPORT_CREATE = 'reports.export.ExportCreator.create'
NOTIFY_USER = 'keeps_service.email_service.notification.notify_users.delay'


def mock_presentation_service():
    container = ReportContainer()
    return MockPresentationService(container.presentation_creator(), container.konquest_query())


def mock_export_service():
    container = ReportContainer()
    return MockExportService(
        container.export_xlsx_creator(), container.export_csv_creator(), container.konquest_export_query(), container.formatter_service()
    )


class MockPresentationService(PresentationService):
    def __init__(self, creator, query_selector):
        super().__init__(creator, query_selector)

    def _load_pages(self) -> None:
        self._pages = [PresentationPage(name='page-test', dataset_lambda=self._load_data(), template_key='none')]

    @staticmethod
    def _load_data() -> dict:
        return {'data_load': 'test'}

    def generate(self, report: Report, number_of_reports: int = 1) -> Report:
        return super().generate(report)


class MockExportService(ExportBuilder):
    def __init__(self, xlsx_creator, csv_creator, query_selector, formatter_service):
        super().__init__(xlsx_creator, csv_creator, query_selector, formatter_service)

    def load_main_data(self) -> DataFrame:
        return DataFrame([])

    def _load_parts(self) -> None:
        self._parts = [ExportPart(name='page-test', data_lambda=self._get_main_data)]

    def generate(self, report: Report) -> Report:
        return super().generate(report)


def test_process_presentations(mocker, database, report):
    expected_ulr = 'https://url.com.br'
    mocker.patch(GET_PRESENTATION_SERVICES_MAP, return_value={report.report_type.name: mock_presentation_service})
    mocker.patch(PRESENTATION_CREATE, return_value=expected_ulr)
    mocker.patch(NOTIFY_USER, return_value=None)
    database_file.session = database

    process_report(report.id)

    instance = database.query(Report).filter().first()
    assert instance.status == 'DONE'
    assert instance.url == expected_ulr
    assert instance.processing_time


def test_process_exports(mocker, database, report_export_model):
    expected_ulr = 'https://url.com.br'
    mocker.patch(GET_PRESENTATION_SERVICES_MAP, return_value={report_export_model.report_type.name: mock_export_service})
    mocker.patch(EXPORT_CREATE, return_value=expected_ulr)
    mocker.patch(NOTIFY_USER, return_value=None)
    database_file.session = database

    process_report(report_export_model.id)

    instance = database.query(Report).filter(Report.id == report_export_model.id).first()
    assert instance.status == 'DONE'
    assert instance.url == expected_ulr
    assert instance.processing_time


def test_delete_older_reports(database, report):
    now = datetime.now()
    some_days_ago = now - timedelta(days=Config.REPORT_LIFE_TIME_DAY + 1)
    report_id = report.id  # Store the ID before modifying the report
    report.created = some_days_ago
    database.commit()
    database_file.session = database

    delete_older_reports()

    # Query by ID directly instead of using the report object which has been deleted
    instance = database.query(Report).filter(Report.id == report_id).first()
    assert not instance


def test_not_delete_new_report(database, report):
    database_file.session = database

    delete_older_reports()

    instance = database.query(Report).filter(Report.id == report.id).first()
    assert instance


def test_should_rollback_session_when_exception_raised(database):
    database_file.session = MagicMock()

    with pytest.raises(ValueError) as error:
        error_to_raise = ValueError("ERROR raised")
        with task_transaction("test"):
            raise error_to_raise

        assert error == error_to_raise
        database_file.session.return_value.rollback.assert_called()
