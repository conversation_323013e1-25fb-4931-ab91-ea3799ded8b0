from keeps_flask.marshmallow.base_schema import BaseSchema
from marshmallow import INCLUDE, fields, post_dump


def paginate_schema(schema, name_prefix=''):
    class PaginationSchema(BaseSchema):
        class Meta:
            unknown = INCLUDE
        page = fields.Integer()
        per_page = fields.Integer()
        total_pages = fields.Integer()
        count = fields.Integer()
        result = fields.Nested(schema, many=True, dump_only=False)

        @post_dump
        def remove_none_values(self, data, many, **kwargs):
            return {
                key: value for key, value in data.items() if value or key == 'result'
            }

    PaginationSchema.__name__ = f'Pagination{name_prefix}{schema.__name__}'
    return PaginationSchema


def build_pagination(data, pagination):
    return dict(page=pagination.page, per_page=pagination.per_page, total_pages=pagination.total_pages,
                count=pagination.count, result=data)
