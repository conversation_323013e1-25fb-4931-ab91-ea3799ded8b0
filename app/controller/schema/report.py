from typing import Any, List, Union

from keeps_flask.marshmallow.base_schema import BaseSchema
from marshmallow import exceptions, fields
from marshmallow import fields as ma_fields
from marshmallow.validate import OneOf, Range, Regexp

SQL_BOOLEAN = ['true', 'false']
LIST_OF_STRINGS_FIELD = fields.List(fields.Str(), required=False)
DATE_FIELD_REGEX = '[0-9]{4}-[0-9]{2}-[0-9]{2}$'
DATE_FIELD = fields.Str(validate=Regexp(DATE_FIELD_REGEX))


class DelimitedListField(fields.List):
    def __init__(self, cls_or_instance: Union[fields.Field, type], **kwargs):
        super().__init__(cls_or_instance, **kwargs)

    def _deserialize(self, value, attr, data, **kwargs) -> List[Any]:
        try:
            list = value.split(",")
            return super()._deserialize(list, attr, data, **kwargs)
        except AttributeError:
            raise exceptions.ValidationError(
                f"{attr} is not a delimited list it has a non string value {value}."
            )


class KonquestWorkspacePresentation(BaseSchema):
    workspace_id = fields.Str(required=True)

class ListOrStringField(ma_fields.Field):
    def _serialize(self, value):
        if isinstance(value, list):
            return value
        elif isinstance(value, str):
            return [value]
        else:
            raise ValueError("CustomField only accepts string or list types, got {}".format(type(value).__name__))

class CoursePresentation(BaseSchema):
    course_id = ListOrStringField(required=True)

class UserPresentation(BaseSchema):
    user_id = ListOrStringField(required=True)

class ExportSchema(BaseSchema):
    report_format = fields.Str(validate=OneOf(['XLSX', 'CSV']), required=True)


class KonquestContentsExportFiltersSchema(BaseSchema):
    mission_stage__mission_id__in = LIST_OF_STRINGS_FIELD


class KonquestContentsExportSchema(ExportSchema):
    filters = fields.Nested(KonquestContentsExportFiltersSchema())


class KonquestMissionExportFiltersSchema(BaseSchema):
    created_date__gte = DATE_FIELD
    created_date__lte = DATE_FIELD
    updated_date__gte = DATE_FIELD
    updated_date__lte = DATE_FIELD
    mission_model__in = LIST_OF_STRINGS_FIELD
    id__in = LIST_OF_STRINGS_FIELD
    mission_provider__id__in = LIST_OF_STRINGS_FIELD
    user_creator_id__in = LIST_OF_STRINGS_FIELD
    mission_category_id__in = LIST_OF_STRINGS_FIELD
    duration_time__gte = fields.Integer()
    duration_time__lte = fields.Integer()
    language__in = LIST_OF_STRINGS_FIELD
    development_status__in = LIST_OF_STRINGS_FIELD
    mission_type_id__in = LIST_OF_STRINGS_FIELD
    group__id__in = LIST_OF_STRINGS_FIELD
    mission__learning_trail_id = LIST_OF_STRINGS_FIELD
    deleted = fields.Boolean()
    deleted_date__gte = DATE_FIELD
    deleted_date__lte = DATE_FIELD
    expiration_date__gte = DATE_FIELD
    expiration_date__lte = DATE_FIELD
    updated_date__gte = DATE_FIELD
    updated_date__lte = DATE_FIELD
    assessment_type__in = LIST_OF_STRINGS_FIELD


class KonquestTrailExportFiltersSchema(BaseSchema):
    created_date__gte = DATE_FIELD
    created_date__lte = DATE_FIELD
    id__in = LIST_OF_STRINGS_FIELD
    user_creator_id__in = LIST_OF_STRINGS_FIELD
    language__in = LIST_OF_STRINGS_FIELD
    duration_time__gte = fields.Integer()
    duration_time__lte = fields.Integer()

class KonquestMissionExportSchema(ExportSchema):
    filters = fields.Nested(KonquestMissionExportFiltersSchema())

class KonquestTrailExportSchema(ExportSchema):
    filters = fields.Nested(KonquestTrailExportFiltersSchema())


class KonquestPulsesChannelsExportFiltersSchema(BaseSchema):
    created_date__gte = DATE_FIELD
    created_date__lte = DATE_FIELD


class KonquestPulsesChannelsExportSchema(ExportSchema):
    filters = fields.Nested(KonquestPulsesChannelsExportFiltersSchema())


class KonquestUserPulsesActivitiesExportFiltersSchema(BaseSchema):
    time_start__gte = DATE_FIELD
    time_start__lte = DATE_FIELD
    user_id__in = fields.List(fields.Str())
    user__related_user_leader_id__in = fields.List(fields.Str())
    channel_id__in = fields.List(fields.Str())


class KonquestUserPulsesActivitiesExportSchema(ExportSchema):
    filters = fields.Nested(KonquestUserPulsesActivitiesExportFiltersSchema())


class UserProfileWorkspaceExportSubFilter(BaseSchema):
    director__in = LIST_OF_STRINGS_FIELD
    manager__in = LIST_OF_STRINGS_FIELD
    area_of_activity__in = LIST_OF_STRINGS_FIELD


class KonquestMissionEnrollmentsExportFiltersSchema(BaseSchema):
    start_date__gte = DATE_FIELD
    start_date__lte = DATE_FIELD
    created_date__gte = DATE_FIELD
    created_date__lte = DATE_FIELD
    goal_date__gte = DATE_FIELD
    goal_date__lte = DATE_FIELD
    end_date__gte = DATE_FIELD
    end_date__lte = DATE_FIELD
    give_up = fields.Boolean()
    user_id__in = LIST_OF_STRINGS_FIELD
    status__in = LIST_OF_STRINGS_FIELD
    performance__lte = fields.Float(validate=Range(min=0.0, max=1.0), required=False)
    performance__gte = fields.Float(validate=Range(min=0.0, max=1.0), required=False)
    progress__lte = fields.Float(validate=Range(min=0.0, max=1.0), required=False)
    progress__gte = fields.Float(validate=Range(min=0.0, max=1.0), required=False)
    mission__mission_model__in = LIST_OF_STRINGS_FIELD
    mission__assessment_type__in = LIST_OF_STRINGS_FIELD
    user__related_user_leader_id__in = LIST_OF_STRINGS_FIELD
    user__country__in = LIST_OF_STRINGS_FIELD
    required = fields.Boolean()
    user__status = fields.Boolean()
    job__name__in = LIST_OF_STRINGS_FIELD
    job_function__name__in = LIST_OF_STRINGS_FIELD
    mission__deleted = fields.Boolean()
    mission_provider__id__in = LIST_OF_STRINGS_FIELD
    mission__language__in = LIST_OF_STRINGS_FIELD
    mission__deleted_date__gte = DATE_FIELD
    mission__deleted_date__lte = DATE_FIELD
    mission__expiration_date__gte = DATE_FIELD
    mission__expiration_date__lte = DATE_FIELD
    learning_trail_step__learning_trail_id__in = LIST_OF_STRINGS_FIELD
    user_profile_workspace = fields.Nested(UserProfileWorkspaceExportSubFilter())
    mission_id__in = LIST_OF_STRINGS_FIELD
    group_mission_id = LIST_OF_STRINGS_FIELD
    mission_enrollment_active = fields.Boolean()
    user_deleted_from_workspace = fields.Boolean()


class KonquestTrailEnrollmentExportFiltersSchema(BaseSchema):
    start_date__gte = DATE_FIELD
    start_date__lte = DATE_FIELD
    created_date__gte = DATE_FIELD
    created_date__lte = DATE_FIELD
    goal_date__gte = DATE_FIELD
    goal_date__lte = DATE_FIELD
    end_date__gte = DATE_FIELD
    end_date__lte = DATE_FIELD
    give_up = fields.Boolean()
    user_id__in = LIST_OF_STRINGS_FIELD
    status__in = LIST_OF_STRINGS_FIELD
    performance__lte = fields.Float(validate=Range(min=0.0, max=1.0), required=False)
    performance__gte = fields.Float(validate=Range(min=0.0, max=1.0), required=False)
    progress__lte = fields.Float(validate=Range(min=0.0, max=1.0), required=False)
    progress__gte = fields.Float(validate=Range(min=0.0, max=1.0), required=False)
    user__related_user_leader_id__in = LIST_OF_STRINGS_FIELD
    user__country__in = LIST_OF_STRINGS_FIELD
    learning_trail__language__in = LIST_OF_STRINGS_FIELD
    user_profile_workspace = fields.Nested(UserProfileWorkspaceExportSubFilter())
    duration_trail__gte = fields.Integer()
    duration_trail__lte = fields.Integer()
    user__status = fields.Boolean()
    trail_enrollment_active = fields.Boolean()
    learning_trail__deleted = fields.Boolean()
    learning_trail__deleted_date__gte = DATE_FIELD
    learning_trail__deleted_date__lte = DATE_FIELD
    required = fields.Boolean()
    user_deleted_from_workspace = fields.Boolean()


class KonquestMissionEnrollmentExportSchema(ExportSchema):
    filters = fields.Nested(KonquestMissionEnrollmentsExportFiltersSchema())


class KonquestGroupMissionUserExportFiltersSchema(BaseSchema):
    mission_id__in = LIST_OF_STRINGS_FIELD
    group_id__in = LIST_OF_STRINGS_FIELD
    user__related_user_leader_id__in = LIST_OF_STRINGS_FIELD


class KonquestGroupMissionUserExportSchema(ExportSchema):
    filters = fields.Nested(KonquestGroupMissionUserExportFiltersSchema())


class KonquestGroupChannelUserExportFiltersSchema(BaseSchema):
    channel_id__in = LIST_OF_STRINGS_FIELD
    group_id__in = LIST_OF_STRINGS_FIELD
    user__related_user_leader_id__in = LIST_OF_STRINGS_FIELD


class KonquestGroupChannelUserExportSchema(ExportSchema):
    filters = fields.Nested(KonquestGroupChannelUserExportFiltersSchema())


class KonquestMissionQuizAnswersExportFiltersSchema(BaseSchema):
    created_date__gte = DATE_FIELD
    created_date__lte = DATE_FIELD
    user_id__in = LIST_OF_STRINGS_FIELD
    user__related_user_leader_id__in = LIST_OF_STRINGS_FIELD
    is_ok = fields.Str(validate=OneOf(SQL_BOOLEAN))
    mission__id__in = LIST_OF_STRINGS_FIELD


class KonquestMissionQuizAnswersExportSchema(ExportSchema):
    filters = fields.Nested(KonquestMissionQuizAnswersExportFiltersSchema())


class KonquestPulseQuizAnswersExportFiltersSchema(BaseSchema):
    created_date__gte = DATE_FIELD
    created_date__lte = DATE_FIELD
    user_id__in = LIST_OF_STRINGS_FIELD
    user__related_user_leader_id__in = LIST_OF_STRINGS_FIELD
    is_ok = fields.Str(validate=OneOf(SQL_BOOLEAN))
    channel__id__in = LIST_OF_STRINGS_FIELD


class KonquestUsersExportFilterSchema(BaseSchema):
    status = fields.Boolean()
    related_user_leader_id__in = LIST_OF_STRINGS_FIELD
    mission_provider__id__in = LIST_OF_STRINGS_FIELD
    country__in = LIST_OF_STRINGS_FIELD
    user_profile_workspace = fields.Nested(UserProfileWorkspaceExportSubFilter())
    user_role_workspace__created_date__gte = DATE_FIELD
    user_role_workspace__created_date__lte = DATE_FIELD
    activity_gte_date = DATE_FIELD
    activity_lte_date = DATE_FIELD


class KonquestUsersGeneralStatisticsExportFilterSchema(BaseSchema):
    status = fields.Boolean()
    related_user_leader_id__in = LIST_OF_STRINGS_FIELD


class KonquestUsersGeneralStatisticsExportSchema(ExportSchema):
    filters = fields.Nested(KonquestUsersGeneralStatisticsExportFilterSchema())


class KonquestUserConsumptionsExportFilterSchema(BaseSchema):
    user__related_user_leader_id__in = LIST_OF_STRINGS_FIELD


class KonquestUserConsumptionsExportSchema(ExportSchema):
    filters = fields.Nested(KonquestUserConsumptionsExportFilterSchema())


class KonquestPulseQuizAnswersExportSchema(ExportSchema):
    filters = fields.Nested(KonquestPulseQuizAnswersExportFiltersSchema())


class KonquestUsersExportSchema(ExportSchema):
    filters = fields.Nested(KonquestUsersExportFilterSchema())


class KonquestUsersAccessByDateExportFiltersSchema(BaseSchema):
    time_start__gte = DATE_FIELD
    time_start__lte = DATE_FIELD
    user_id__in = LIST_OF_STRINGS_FIELD
    user__related_user_leader_id__in = LIST_OF_STRINGS_FIELD
    user_profile_workspace = fields.Nested(UserProfileWorkspaceExportSubFilter())
    learning_object_id__in = LIST_OF_STRINGS_FIELD


class KonquestUsersAccessByDateExportSchema(ExportSchema):
    filters = fields.Nested(KonquestUsersAccessByDateExportFiltersSchema())


class KonquestMissionEvaluationsExportFiltersSchema(BaseSchema):
    created_date__gte = DATE_FIELD
    created_date__lte = DATE_FIELD
    user_id__in = LIST_OF_STRINGS_FIELD
    mission_id__in = LIST_OF_STRINGS_FIELD
    nps__lte = fields.Integer(validate=Range(min=0, max=10), required=False)
    nps__gte = fields.Integer(validate=Range(min=0, max=10), required=False)
    questions_rating_avg__lte = fields.Float(validate=Range(min=0.0, max=5.0), required=False)
    questions_rating_avg__gte = fields.Float(validate=Range(min=0.0, max=5.0), required=False)


class KonquestMissionEvaluationsExportSchema(ExportSchema):
    filters = fields.Nested(KonquestMissionEvaluationsExportFiltersSchema())


class SmartzapEnrollmentsExportFiltersSchema(BaseSchema):
    course_id = fields.Str(required=False)
    status__in = LIST_OF_STRINGS_FIELD


class SmartzapEnrollmentsExportSchema(ExportSchema):
    filters = fields.Nested(SmartzapEnrollmentsExportFiltersSchema())


class SmartzapUserActivitiesExportFiltersSchema(BaseSchema):
    enrollment__course_id = fields.Str(required=False)


class SmartzapUserActivitiesExportSchema(ExportSchema):
    filters = fields.Nested(SmartzapUserActivitiesExportFiltersSchema())


class KonquestMissionEnrollmentQuizzesExportFilters(BaseSchema):
    user_id__in = LIST_OF_STRINGS_FIELD
    mission_id__in = LIST_OF_STRINGS_FIELD


class KonquestMissionEnrollmentQuizzesExportSchema(BaseSchema):
    filters = fields.Nested(KonquestMissionEnrollmentQuizzesExportFilters())


class KonquestTrailsCompletionRateExportFiltersSchema(BaseSchema):
    learning_trail_step__learning_trail_id__in = LIST_OF_STRINGS_FIELD


class KonquestTrailsCompletionRateExportSchema(ExportSchema):
    filters = fields.Nested(KonquestTrailsCompletionRateExportFiltersSchema())


class KonquestTrailEnrollmentExportSchema(ExportSchema):
    filters = fields.Nested(KonquestTrailEnrollmentExportFiltersSchema())


class KonquestCompaniesHealthCheckFiltersSchema(BaseSchema):
    start_date = fields.Str(validate=Regexp(DATE_FIELD_REGEX), required=True)
    end_date = fields.Str(validate=Regexp(DATE_FIELD_REGEX), required=True)


class KonquestCompaniesHealthCheckExportSchema(ExportSchema):
    filters = fields.Nested(KonquestCompaniesHealthCheckFiltersSchema(), required=True)
