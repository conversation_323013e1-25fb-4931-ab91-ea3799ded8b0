from domain.user.models import User<PERSON>ilter, UserListingFilter, UserTotalFilter
from keeps_flask.marshmallow.base_schema import BaseSchema
from marshmallow import fields
from marshmallow.validate import OneOf, Regexp


class GeneralUserTotalsSchema(BaseSchema):
    pass


class UserFilterSchema(BaseSchema):
    __model__ = UserFilter
    company_id = fields.Str(required=True)


class UserTotalFilterSchema(BaseSchema):
    __model__ = UserTotalFilter

    company_id = fields.Str(required=True)
    start_date = fields.Str(validate=Regexp('[0-9]{4}-[0-9]{2}-[0-9]{2}$'))
    end_date = fields.Str(validate=Regexp('[0-9]{4}-[0-9]{2}-[0-9]{2}$'))
    interval = fields.Str(validate=OneOf(['year', 'quarter', 'month', 'week', 'day', 'hour', 'minute', 'second']))
    data_size = fields.Int(description='Data size. Default 0')


class UserListingFilterSchema(BaseSchema):
    __model__ = UserListingFilter
    company_id = fields.Str(required=True)
    search_term = fields.Str()

    page = fields.Int()
    page_size = fields.Int()
    sort = fields.Str(validate=OneOf([
        'name.sortable', '-name.sortable',

        # v1 only
        'user_stats.courses.total', '-user_stats.courses.total',
        'user_stats.courses.completed', '-user_stats.courses.completed',
        'user_stats.courses.created', '-user_stats.courses.created',
        'user_stats.courses.contributed', '-user_stats.courses.contributed',
        'user_stats.courses.channels_created', '-user_stats.courses.channels_created',
        'user_stats.courses.completed_ratio', '-user_stats.courses.completed_ratio',
    ]))


class UserEnrollmentsListingFilterSchema(BaseSchema):
    __model__ = UserListingFilter
    company_id = fields.Str(required=True)
    search_term = fields.Str()

    page = fields.Int()
    page_size = fields.Int()
    sort = fields.Str(validate=OneOf([
        # only v1
        'mission_name.sortable', '-mission_name.sortable',
        'completed_rate', '-completed_rate',
        'rating', '-rating'

        # only v2
        'course_name.sortable', '-course_name.sortable',

        # both
        'category_name.sortable', '-category_name.sortable',
        'start_date', '-start_date',
        'end_date', '-end_date',
    ]))
