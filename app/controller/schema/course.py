from domain.course.models import (
    CourseContentsListingFilter,
    CourseEnrollmentsListingFilter,
    CourseFilter,
    CourseListingFilter,
    CourseTotalFilter,
)
from keeps_flask.marshmallow.base_schema import BaseSchema
from marshmallow import fields
from marshmallow.validate import OneOf, Regexp


class GeneralCourseTotalsSchema(BaseSchema):
    pass


class CourseFilterSchema(BaseSchema):
    __model__ = CourseFilter
    company_id = fields.Str(required=True)


class CourseTotalFilterSchema(BaseSchema):
    __model__ = CourseTotalFilter

    company_id = fields.Str(required=True)
    start_date = fields.Str(validate=Regexp('[0-9]{4}-[0-9]{2}-[0-9]{2}$'))
    end_date = fields.Str(validate=Regexp('[0-9]{4}-[0-9]{2}-[0-9]{2}$'))
    interval = fields.Str(validate=OneOf(['year', 'quarter', 'month', 'week', 'day', 'hour', 'minute', 'second']))

    data_size = fields.Int(description='Data size. Default 0')


class CourseEnrollmentsListingFilterSchema(BaseSchema):
    __model__ = CourseEnrollmentsListingFilter

    company_id = fields.Str(required=True)
    search_term = fields.Str()

    start_date = fields.Str(validate=Regexp('[0-9]{4}-[0-9]{2}-[0-9]{2}$'))
    end_date = fields.Str(validate=Regexp('[0-9]{4}-[0-9]{2}-[0-9]{2}$'))

    page = fields.Int()
    page_size = fields.Int()
    sort = fields.Str(validate=OneOf([
        # v1
        'user.user_name.sortable', '-user.user_name.sortable',
        'enrollment.start_date', '-enrollment.start_date',
        'enrollment.end_date', '-enrollment.end_date',
        'enrollment.performance', '-enrollment.performance',

        # v2
        'user_name.sortable', '-user_name.sortable',
        'start_date', '-start_date',
        'end_date', '-end_date',
        'performance', '-performance',
    ]))


class CourseListingFilterSchema(BaseSchema):
    __model__ = CourseListingFilter

    company_id = fields.Str(required=True)
    search_term = fields.Str()

    page = fields.Int()
    page_size = fields.Int()
    sort = fields.Str(validate=OneOf([
        'name.sortable', '-name.sortable',
        'course_category.name.sortable', '-course_category.name.sortable',

        # v1 only
        'course_stats.enrollment.total', '-course_stats.enrollment.total',
        'course_stats.enrollment.completed', '-course_stats.enrollment.completed',
        'course_stats.enrollment.completed_ratio', '-course_stats.enrollment.completed_ratio',
        'course_stats.rating.average', '-course_stats.rating.average',
    ]))


class CourseContentsListingFilterSchema(BaseSchema):
    __model__ = CourseContentsListingFilter

    company_id = fields.Str(required=True)
    search_term = fields.Str()
    type = fields.Str()

    page = fields.Int()
    page_size = fields.Int()
    sort = fields.Str(validate=OneOf([
        'content_name.sortable', '-content_name.sortable',

        # v1 only
        'activity_stats.total_views', '-activity_stats.total_views',
        'activity_stats.total_seconds', '-activity_stats.total_seconds',
        # 'activity_stats.average_engagement', '-activity_stats.average_engagement'
    ]))
