from keeps_flask.marshmallow.base_schema import BaseSchema


class ElasticsearchBodySchema(BaseSchema):
    """
    Base schema for all layouts.

    This schema should reflect the Contact database table schema.
    Because we use this schema as the entry point of our rest api before the
    layout validation occur we need to be the more general as possible here (and in database).

    All the rules about ranges that are specific to a layout should be defined
    in the derived schema for that layout.

    Ex: if one layout has the maximum value length of 255 characters for a field,
    and other layout has 300 characters for the same field we should declare
    this field here (and in database) with the 300 characters length rule because it is the
    broader rule.
    However, in the derived schemas for both layouts we should override that field with
    the range definition that makes sense for each other (255 and 300, respectively).
    """
    # __model__ = ElasticSearchBody
