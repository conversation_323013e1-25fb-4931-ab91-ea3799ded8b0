from domain.user.models_v2 import (
    UserDataFilter,
    UserEnrollments<PERSON>ilter,
    UserFilter,
    UsersListingFilter,
    UsersStatsFilter,
)
from keeps_flask.marshmallow.base_schema import BaseSchema
from marshmallow import fields
from marshmallow.validate import OneOf, Range, Regexp


class UserFilterSchema(BaseSchema):
    __model__ = UserFilter
    # workspace_id = fields.Str(required=True, data_key='x-client')  # TODO deserialize from headers

class UserDataFilterSchema(BaseSchema):
    __model__ = UserDataFilter
    start_date = fields.Str(validate=Regexp('[0-9]{4}-[0-9]{2}-[0-9]{2}$'))
    end_date = fields.Str(validate=Regexp('[0-9]{4}-[0-9]{2}-[0-9]{2}$'))

class UsersStatsFilterSchema(UserFilterSchema):
    __model__ = UsersStatsFilter

    start_date = fields.Str(validate=Regexp('[0-9]{4}-[0-9]{2}-[0-9]{2}$'))
    end_date = fields.Str(validate=Regexp('[0-9]{4}-[0-9]{2}-[0-9]{2}$'))
    interval = fields.Str(validate=OneOf(['year', 'quarter', 'month', 'week', 'day', 'hour', 'minute', 'second']))
    data_size = fields.Int(description='Data size. Default 0')


class UsersListingFilterSchema(UserFilterSchema):
    __model__ = UsersListingFilter

    search_term = fields.Str()
    leader = fields.Str()
    completion_rate_min = fields.Float(validate=Range(min=0.0, max=1.0), required=False)
    completion_rate_max = fields.Float(validate=Range(min=0.0, max=1.0), required=False)

    page = fields.Int()
    page_size = fields.Int()
    sort = fields.Str(validate=OneOf([
        'name.sortable', '-name.sortable',
        'leader_name', '-leader_name',
        'completion_rate', '-completion_rate'
    ]))


class UserEnrollmentsFilterSchema(UserFilterSchema):
    __model__ = UserEnrollmentsFilter

    search_term = fields.Str()
    course_category = fields.Str()

    performance_min = fields.Float(validate=Range(min=0.0, max=1.0))
    performance_max = fields.Float(validate=Range(min=0.0, max=1.0))
    start_date_min = fields.Str(validate=Regexp('[0-9]{4}-[0-9]{2}-[0-9]{2}$'))
    start_date_max = fields.Str(validate=Regexp('[0-9]{4}-[0-9]{2}-[0-9]{2}$'))
    end_date_min = fields.Str(validate=Regexp('[0-9]{4}-[0-9]{2}-[0-9]{2}$'))
    end_date_max = fields.Str(validate=Regexp('[0-9]{4}-[0-9]{2}-[0-9]{2}$'))

    page = fields.Int()
    page_size = fields.Int()
    sort = fields.Str(validate=OneOf([
        'course_name.sortable', '-course_name.sortable',
        'category_name.sortable', '-category_name.sortable',
        'start_date', '-start_date',
        'end_date', '-end_date',
        'questions.questions_answered_correctly_ratio', '-questions.questions_answered_correctly_ratio',
    ]))
