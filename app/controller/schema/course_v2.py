from domain.course.models_v2 import (
    CourseContentsListing<PERSON>ilter,
    CourseEnrollmentsListingFilter,
    CourseFilter,
    CoursesListingFilter,
    CoursesStatsFilter,
)
from keeps_flask.marshmallow.base_schema import BaseSchema
from marshmallow import fields
from marshmallow.validate import OneOf, Regexp


class CourseFilterSchema(BaseSchema):
    __model__ = CourseFilter
    # workspace_id = fields.Str(required=True, data_key='x-client')  # TODO deserialize from headers


class CoursesStatsFilterSchema(CourseFilterSchema):
    __model__ = CoursesStatsFilter

    start_date = fields.Str(validate=Regexp('[0-9]{4}-[0-9]{2}-[0-9]{2}$'))
    end_date = fields.Str(validate=Regexp('[0-9]{4}-[0-9]{2}-[0-9]{2}$'))
    interval = fields.Str(validate=OneOf(['year', 'quarter', 'month', 'week', 'day', 'hour', 'minute', 'second']))
    data_size = fields.Int(description='Data size. Default 0')


class CoursesListingFilterSchema(CourseFilterSchema):
    __model__ = CoursesListingFilter

    search_term = fields.Str()

    page = fields.Int()
    page_size = fields.Int()
    sort = fields.Str(validate=OneOf([
        'name.sortable', '-name.sortable',
        'course_category.name.sortable', '-course_category.name.sortable',
    ]))


class CourseEnrollmentsListingFilterSchema(CoursesListingFilterSchema):
    __model__ = CourseEnrollmentsListingFilter

    start_date = fields.Str(validate=Regexp('[0-9]{4}-[0-9]{2}-[0-9]{2}$'))
    end_date = fields.Str(validate=Regexp('[0-9]{4}-[0-9]{2}-[0-9]{2}$'))

    sort = fields.Str(validate=OneOf([
        'user_name.sortable', '-user_name.sortable',
        'start_date', '-start_date',
        'end_date', '-end_date',
        'performance', '-performance',
    ]))


class CourseContentsListingFilterSchema(CoursesListingFilterSchema):
    __model__ = CourseContentsListingFilter

    type = fields.Str()

    sort = fields.Str(validate=OneOf([
        'content_name.sortable', '-content_name.sortable',
    ]))
