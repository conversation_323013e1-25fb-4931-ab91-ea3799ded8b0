from flask import jsonify
from injector import inject
from keeps_flask.app import Application
from werkzeug.exceptions import HTTPException


class KeepsException(HTTPException):  # pragma: no cover
    code = 400
    message = 'Invalid request'

    def __init__(self, _msg=None, _code=None):
        if _msg:
            self.message = _msg
        if _code:
            self.code = _code

    def as_json(self):
        jsonify(self.message), self.code


class ErrorHandler:  # pragma: no cover
    @inject
    def __init__(self, app: Application):
        self.app = app

    def register_endpoints(self):

        @self.app.errorhandler(KeepsException)
        def handle_keeps_exception(e: KeepsException):
            print('[Error] handle_keeps_exception')
            return e.as_json()

        @self.app.errorhandler(HTTPException)
        def handle_http_exception(e: HTTPException):
            print('[Error] handle_http_exception')
            return jsonify({
                "name": e.name,
                "description": e.description,
            }), e.code

        @self.app.errorhandler(Exception)
        def handle_exception(e: Exception):
            print('[Error] handle_exception')
            return jsonify({
                'message': 'Internal Server Error',
                'error': str(e)
            }), 500
