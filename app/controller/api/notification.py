from http import HTTPStatus

from domain.common.authorize import authorize
from domain.common.dependencies import NotificationService
from domain.common.utils import make_keeps_error
from elasticsearch import NotFoundError
from flask import jsonify, request
from flask_apispec import doc
from injector import inject
from keeps_flask.app import Application


class NotificationEndpoints:
    @inject
    def __init__(
        self,
        app: Application,
        notification_service: NotificationService
    ):
        self.app = app
        self.notification_service = notification_service

    def register_endpoints(self):
        """
        Contact endpoints factory functions.
        """

        @doc(tags=['notification'], description='Return unread notifications by the user logged')
        @self.app.route('/api/v1/notifications', methods=['GET'])
        @authorize()
        def get_user_notifications():
            workspace_id = request.x_client
            user_id = request.user.id
            try:
                response = self.notification_service.get_user_notifications(user_id=user_id, workspace_id=workspace_id)
                return jsonify(response)
            except NotFoundError as e:
                return jsonify(str(e)), HTTPStatus.NOT_FOUND

        @doc(tags=['notification'], description='Read a notification')
        @self.app.route('/api/v1/notifications/<notification_id>/read', methods=['POST'])
        @authorize()
        def read_notification(notification_id):
            try:
                response = self.notification_service.read_notification(notification_id=notification_id)
                return jsonify(response)
            except NotFoundError as e:
                return make_keeps_error(detail=e.args[0], i18n=e.args[1], status_code=HTTPStatus.NOT_FOUND)
