import json
from http import HTT<PERSON>tatus

import pytest
from config.default import Config
from conftest import ElasticsearchConnection, FakeElasticsearchModule
from flask.testing import FlaskClient
from keeps_flask.app import create_app, create_injector
from modules.app_module import AppModule
from modules.config_module import ConfigModule
from modules.db_module import DatabaseModule


@pytest.fixture
def injector():
    injector = create_injector([AppModule, ConfigModule, DatabaseModule, FakeElasticsearchModule])
    return injector


@pytest.fixture
def app(injector):
    _app = create_app(injector)
    _app.init()
    return _app


@pytest.fixture
def initial_data(notification_doc, injector):
    es = injector.get(ElasticsearchConnection)
    return es.index(Config.ELASTICSEARCH_INDEX_NOTIFICATIONS, notification_doc)


@pytest.fixture
def initial_empty_data(injector):
    es = injector.get(ElasticsearchConnection)
    return es.indices.create(Config.ELASTICSEARCH_INDEX_NOTIFICATIONS)


@pytest.fixture
def client(app) -> FlaskClient:
    return app.test_client()


def test_get_notifications(client, auth_header, initial_data):
    # GIVEN
    headers = [auth_header]

    # WHEN
    response = client.get('/api/v1/notifications', headers=headers)

    # THEN
    assert response.status_code == HTTPStatus.OK
    data = json.loads(response.data.decode())

    assert data['data'][0]['_source']['user_receiving']['id'] == 'bbf47825-8dfb-49bc-8ad8-f8adc775f95f'
    assert data['total'] == 1
