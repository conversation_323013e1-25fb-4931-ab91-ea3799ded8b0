import json
from http import <PERSON><PERSON><PERSON>tatus

import pytest
from config.default import Config
from conftest import ElasticsearchConnection, FakeElasticsearchModule
from flask.testing import FlaskClient
from keeps_flask.app import create_app, create_injector
from modules.app_module import AppModule
from modules.config_module import ConfigModule
from modules.db_module import DatabaseModule


@pytest.fixture
def injector():
    injector = create_injector([AppModule, ConfigModule, DatabaseModule, FakeElasticsearchModule])
    return injector


@pytest.fixture
def app(injector):
    _app = create_app(injector)
    _app.init()
    return _app


@pytest.fixture
def initial_data(course_doc, injector):
    es = injector.get(ElasticsearchConnection)
    return es.index(Config.ELASTICSEARCH_INDEX_COURSES_V1, course_doc)


@pytest.fixture
def initial_started_course_data(course_enrollment_started_doc, injector):
    es = injector.get(ElasticsearchConnection)
    return es.index(Config.ELASTICSEARCH_INDEX_COURSES_V1, course_enrollment_started_doc)


@pytest.fixture
def initial_empty_data(injector):
    es = injector.get(ElasticsearchConnection)
    return es.indices.create(Config.ELASTICSEARCH_INDEX_COURSES_V1)


@pytest.fixture
def initial_completed_course_data(course_enrollment_completed_doc, injector):
    es = injector.get(ElasticsearchConnection)
    return es.index(Config.ELASTICSEARCH_INDEX_COURSES_V1, course_enrollment_completed_doc)


@pytest.fixture
def client(app) -> FlaskClient:
    return app.test_client()


@pytest.fixture
def data_2_enrollment_started_1_completed(enrollment_2_started_1_completed_docs, injector):
    es = injector.get(ElasticsearchConnection)
    docs = []
    for doc in enrollment_2_started_1_completed_docs:
        response = es.index(Config.ELASTICSEARCH_INDEX_COURSES_V1, doc)
        docs.append(response)
    return docs


def test_should_get_index_not_found(client, auth_header):
    # GIVEN
    headers = [auth_header]
    query = {
        'company_id': '1',
        'start_date': '2019-01-10',
        'end_date': '2019-02-10'
    }

    # WHEN
    response = client.get('/api/v1/courses', query_string=query, headers=headers)

    # THEN
    assert response.status_code == HTTPStatus.NOT_FOUND
    data = json.loads(response.data.decode())

    assert 'IndexMissingException' in data


def test_should_validate_query_params_format(client, auth_header):
    # GIVEN
    headers = [auth_header]
    query = {
        'company_id': '1',
        'start_date': '2019-02-10',
        'end_date': 'invalid date'
    }

    # WHEN
    response = client.get('/api/v1/courses', query_string=query, headers=headers)

    # THEN
    assert response.status_code == HTTPStatus.UNPROCESSABLE_ENTITY
    data = json.loads(response.data.decode())['message']

    assert 'end_date' in data
    assert 'start_date' not in data
    assert 'company_id' not in data


def test_should_validate_without_required_query_params(client, auth_header):
    # GIVEN
    headers = [auth_header]

    # WHEN
    response = client.get('/api/v1/courses', headers=headers)

    # THEN
    assert response.status_code == HTTPStatus.UNPROCESSABLE_ENTITY
    data = json.loads(response.data.decode())['message']
    assert 'company_id' in data


def test_should_not_authorize_request(client):
    # GIVEN
    headers = [('Authorization', '1234')]

    # WHEN
    response = client.get('/api/v1/courses', headers=headers)

    # THEN
    assert response.status_code == HTTPStatus.UNAUTHORIZED


def test_should_get_course_totals(client, auth_header, initial_data):
    # GIVEN
    headers = [auth_header]

    query = {
        'company_id': '1',
        'start_date': '2012-01-10',
        'end_date': '2012-02-10'
    }

    # WHEN
    response = client.get('/api/v1/courses', query_string=query, headers=headers)

    # THEN
    assert response.status_code == HTTPStatus.OK
    data = json.loads(response.data.decode())

    assert data['data'][0]['_source']['name'] == 'Course name'
    assert data['total'] == 1


def test_should_get_started_courses_totals(client, auth_header, initial_started_course_data):
    # GIVEN
    headers = [auth_header]
    query = {
        'company_id': '1',
        'start_date': '2020-01-01',
        'end_date': '2020-02-01'
    }

    # WHEN
    response = client.get('/api/v1/courses/started', query_string=query, headers=headers)

    # THEN
    assert response.status_code == HTTPStatus.OK
    data = json.loads(response.data.decode())

    assert data['data'][0]['_source']['model_type'] == 'enrollment'
    assert data['data'][0]['_source']['enrollment']['status'] == 'STARTED'
    assert data['total'] == 1


def test_should_get_started_courses_totals_index_not_found(client, auth_header):
    # GIVEN
    headers = [auth_header]
    query = {
        'company_id': '2',
        'start_date': '2011-01-01',
        'end_date': '2012-02-01'
    }

    # WHEN
    response = client.get('/api/v1/courses/started', query_string=query, headers=headers)

    # THEN
    assert response.status_code == HTTPStatus.NOT_FOUND


def test_should_get_started_courses_totals_empty_data(client, auth_header, initial_empty_data):
    # GIVEN
    headers = [auth_header]
    query = {
        'company_id': '2',
        'start_date': '2011-01-01',
        'end_date': '2012-02-01'
    }

    # WHEN
    response = client.get('/api/v1/courses/started', query_string=query, headers=headers)

    # THEN
    assert response.status_code == HTTPStatus.OK
    data = json.loads(response.data.decode())

    assert len(data['data']) == 0
    assert data['total'] == 0


def test_should_get_completed_courses_totals(client, auth_header, initial_completed_course_data):
    # GIVEN
    headers = [auth_header]
    query = {
        'company_id': '1',
        'start_date': '2020-01-01',
        'end_date': '2020-02-01'
    }

    # WHEN
    response = client.get('/api/v1/courses/completed', query_string=query, headers=headers)

    # THEN
    assert response.status_code == HTTPStatus.OK
    data = json.loads(response.data.decode())

    assert data['data'][0]['_source']['model_type'] == 'enrollment'
    assert data['data'][0]['_source']['enrollment']['status'] == 'COMPLETED'
    assert data['total'] == 1


def test_should_get_completed_courses_totals_index_not_found(client, auth_header):
    # GIVEN
    headers = [auth_header]
    query = {
        'company_id': '2',
        'start_date': '2011-01-01',
        'end_date': '2012-02-01'
    }

    # WHEN
    response = client.get('/api/v1/courses/completed', query_string=query, headers=headers)

    # THEN
    assert response.status_code == HTTPStatus.NOT_FOUND


def test_should_get_completed_courses_totals_empty_data(client, auth_header, initial_empty_data):
    # GIVEN
    headers = [auth_header]
    query = {
        'company_id': '2',
        'start_date': '2011-01-01',
        'end_date': '2012-02-01'
    }

    # WHEN
    response = client.get('/api/v1/courses/completed', query_string=query, headers=headers)

    # THEN
    assert response.status_code == HTTPStatus.OK
    data = json.loads(response.data.decode())

    assert len(data['data']) == 0
    assert data['total'] == 0


def test_should_get_new_enrollment_totals(client, auth_header, initial_started_course_data):
    # GIVEN
    headers = [auth_header]
    query = {
        'company_id': '1',
        'start_date': '2020-01-01',
        'end_date': '2020-02-01'
    }

    # WHEN
    response = client.get('/api/v1/courses/new-enrollment', query_string=query, headers=headers)

    # THEN
    assert response.status_code == HTTPStatus.OK
    data = json.loads(response.data.decode())

    assert data['data'][0]['_source']['model_type'] == 'enrollment'
    assert data['data'][0]['_source']['enrollment']['created_date'] >= query['start_date']
    assert data['data'][0]['_source']['enrollment']['created_date'] <= query['end_date']
    assert data['total'] == 1


def test_should_get_ratio_courses_completed(client, auth_header, data_2_enrollment_started_1_completed):
    # GIVEN
    headers = [auth_header]
    query = {
        'company_id': '1',
        'start_date': '2020-01-01',
        'end_date': '2020-02-01'
    }

    # WHEN
    return  # disabled for FakeElasticsearch
    response = client.get('/api/v1/courses/completed/ratio', query_string=query, headers=headers)

    # THEN
    assert response.status_code == HTTPStatus.OK
    data = json.loads(response.data.decode())

    assert data['data'][0]['_source']['model_type'] == 'enrollment'
    assert data['total'] == 1
