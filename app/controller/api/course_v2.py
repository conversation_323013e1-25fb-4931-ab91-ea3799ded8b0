import traceback
from http import HTTPStatus

from controller.schema.course_v2 import (
    CourseContentsListingFilterSchema,
    CourseEnrollmentsListingFilterSchema,
    CourseFilterSchema,
    CoursesListingFilterSchema,
    CoursesStatsFilterSchema,
)
from custom.discord_webhook_logger import DiscordWebhookLogger
from domain.common.authorize import authorize
from domain.course.service_v2 import CourseServiceV2
from elasticsearch import NotFoundError
from flask import jsonify, request
from flask_apispec import doc
from injector import inject
from keeps_flask.app import Application
from keeps_flask.marshmallow import marshal


class CourseEndpointsV2:
    @inject
    def __init__(
        self,
        app: Application,
        course_service: CourseServiceV2
    ):
        self.app = app
        self.course_service = course_service

    def register_endpoints(self):
        """
        Contact endpoints factory functions.
        """

        @doc(tags=['course'], description='Returns general data for the specified course')
        @self.app.route('/api/v2/course/<course_id>', methods=['GET'])
        @authorize()
        @marshal(CourseFilterSchema(), parameter_name='filters', locations=['query'])
        def get_course_data_v2(course_id, filters):
            print('[API::v2] get_course_data_v2', filters)
            try:
                self.extractWorkspace(filters)
                course = self.course_service.get_course_data(course_id, filters)
                if not course['data']:
                    return '', HTTPStatus.NOT_FOUND
                return jsonify(course)
            except Exception as e:
                return self.handleException(e, 'Analytics API > Course v2 > ID: ' + course_id)

        @doc(tags=['course'], description='Returns user enrollments for the specified course')
        @self.app.route('/api/v2/course/<course_id>/enrollments', methods=['GET'])
        @authorize()
        @marshal(CourseEnrollmentsListingFilterSchema(), parameter_name='filters', locations=['query'])
        def get_course_enrollments_v2(course_id, filters):
            print('[API::v2] get_course_enrollments_v2', filters)
            try:
                self.extractWorkspace(filters)
                enrollments = self.course_service.get_course_enrollments(course_id, filters)
                return jsonify(enrollments)
            except Exception as e:
                return self.handleException(e, 'Analytics API > Course v2 > Enrollments > ID: ' + course_id)

        @doc(tags=['course'], description='Returns the content list of the specified course')
        @self.app.route('/api/v2/course/<course_id>/contents', methods=['GET'])
        @authorize()
        @marshal(CourseContentsListingFilterSchema(), parameter_name='filters', locations=['query'])
        def get_course_contents_v2(course_id, filters):
            print('[API::v2] get_course_contents_v2', filters)
            try:
                self.extractWorkspace(filters)
                enrollments = self.course_service.get_course_contents(course_id, filters)
                return jsonify(enrollments)
            except Exception as e:
                return self.handleException(e, 'Analytics API > Course v2 > Contents > ID: ' + course_id)

        @doc(tags=['course'], description='Returns the total courses created in the period, and aggregation of date histogram')
        @self.app.route('/api/v2/courses', methods=['GET'])
        @authorize()
        @marshal(CoursesStatsFilterSchema(), parameter_name='filters', locations=['query'])
        def get_total_courses_v2(filters):
            print('[API::v2] get_total_courses_v2', filters)
            try:
                self.extractWorkspace(filters)
                total = self.course_service.get_total_courses(filters)
                return jsonify(total)
            except Exception as e:
                return self.handleException(e, 'Analytics API > Course v2 > Total')

        @doc(tags=['course'], description='Returns the total courses with enrollment started in the period + date histogram')
        @self.app.route('/api/v2/courses/started', methods=['GET'])
        @authorize()
        @marshal(CoursesStatsFilterSchema(), parameter_name='filters', locations=['query'])
        def get_total_courses_started_v2(filters):
            print('[API::v2] get_total_courses_started_v2', filters)
            try:
                self.extractWorkspace(filters)
                course_totals = self.course_service.get_total_courses_started(filters)
                return jsonify(course_totals)
            except Exception as e:
                return self.handleException(e, 'Analytics API > Course v2 > Started')

        @doc(tags=['course'], description='Returns the total courses with enrollment ending in the period + date histogram.')
        @self.app.route('/api/v2/courses/completed', methods=['GET'])
        @authorize()
        @marshal(CoursesStatsFilterSchema(), parameter_name='filters', locations=['query'])
        def get_total_courses_completed_v2(filters):
            print('[API::v2] get_total_courses_completed_v2', filters)
            try:
                self.extractWorkspace(filters)
                course_totals = self.course_service.get_total_courses_completed(filters)
                return jsonify(course_totals)
            except Exception as e:
                return self.handleException(e, 'Analytics API > Course v2 > Completed')

        @doc(tags=['course'], description='Get Total Active Users.')
        @self.app.route('/api/v2/courses/users/active', methods=['GET'])
        @authorize()
        @marshal(CoursesStatsFilterSchema(many=False), parameter_name='filters', locations=['query'])
        def get_total_courses_active_users_v2(filters):
            print('[API::v2] get_total_courses_active_users_v2', filters)
            try:
                self.extractWorkspace(filters)
                course_totals = self.course_service.get_total_active_users(filters)
                return jsonify(course_totals)
            except Exception as e:
                return self.handleException(e, 'Analytics API > Course v2 > Active Users')

        @doc(tags=['course'], description='Returns the total enrollments created in the period, and aggregation of date histogram')
        @self.app.route('/api/v2/courses/new-enrollment', methods=['GET'])
        @authorize()
        @marshal(CoursesStatsFilterSchema(), parameter_name='filters', locations=['query'])
        def get_total_courses_new_enrollment_v2(filters):
            print('[API::v2] get_total_courses_new_enrollment_v2', filters)
            try:
                self.extractWorkspace(filters)
                total = self.course_service.get_total_courses_new_enrollment(filters)
                return jsonify(total)
            except Exception as e:
                return self.handleException(e, 'Analytics API > Course v2 > New Enrollments')

        @doc(tags=['course'], description='Returns the ratio of completed enrollments over total, in the period')
        @self.app.route('/api/v2/courses/completed/ratio', methods=['GET'])
        @authorize()
        @marshal(CoursesStatsFilterSchema(only=['start_date', 'end_date']), parameter_name='filters', locations=['query'])
        def get_ratio_courses_completed_v2(filters):
            print('[API::v2] get_ratio_courses_completed_v2', filters)
            try:
                self.extractWorkspace(filters)
                total = self.course_service.get_ratio_courses_completed(filters)
                return jsonify(total)
            except Exception as e:
                return self.handleException(e, 'Analytics API > Course v2 > Completed Ratio')

        @doc(tags=['course'], description='Returns the sum of total hours of content of courses available in the period')
        @self.app.route('/api/v2/courses/content/available', methods=['GET'])
        @authorize()
        @marshal(CoursesStatsFilterSchema(exclude=['interval']), parameter_name='filters', locations=['query'])
        def get_total_courses_content_hours_available_v2(filters):
            print('[API::v2] get_total_courses_content_hours_available_v2', filters)
            try:
                self.extractWorkspace(filters)
                total = self.course_service.get_total_courses_content_hours_available(filters)
                return jsonify(total)
            except Exception as e:
                return self.handleException(e, 'Analytics API > Course v2 > Content Hours Available')

        @doc(tags=['course'], description='Returns the sum of total hours of content of activities in the period')
        @self.app.route('/api/v2/courses/content/consumed', methods=['GET'])
        @authorize()
        @marshal(CoursesStatsFilterSchema(exclude=['interval']), parameter_name='filters', locations=['query'])
        def get_total_courses_content_hours_consumed_v2(filters):
            print('[API::v2] get_total_courses_content_hours_consumed_v2', filters)
            try:
                self.extractWorkspace(filters)
                total = self.course_service.get_total_courses_content_hours_consumed(filters)
                return jsonify(total)
            except Exception as e:
                return self.handleException(e, 'Analytics API > Course v2 > Content Hours Consumed')

        @doc(tags=['course'], description='Returns the average ratings of the courses, per enrollment rating')
        @self.app.route('/api/v2/courses/rating', methods=['GET'])
        @authorize()
        @marshal(CoursesStatsFilterSchema(exclude=['interval']), parameter_name='filters', locations=['query'])
        def get_courses_ratings_v2(filters):
            print('[API::v2] get_courses_ratings_v2', filters)
            try:
                self.extractWorkspace(filters)
                total = self.course_service.get_courses_ratings(filters)
                return jsonify(total)
            except Exception as e:
                return self.handleException(e, 'Analytics API > Course v2 > Ratings')

        @doc(tags=['course'], description='Returns the ranking of course categories, id and name in subaggregation.')
        @self.app.route('/api/v2/courses/categories', methods=['GET'])
        @authorize()
        @marshal(CoursesStatsFilterSchema(exclude=['interval']), parameter_name='filters', locations=['query'])
        def get_courses_categories_v2(filters):
            print('[API::v2] get_courses_categories_v2', filters)
            try:
                self.extractWorkspace(filters)
                total = self.course_service.get_courses_categories(filters)
                return jsonify(total)
            except Exception as e:
                return self.handleException(e, 'Analytics API > Course v2 > Categories')

        @doc(tags=['course'], description='Returns the ranking of course types, id and name in subaggregation.')
        @self.app.route('/api/v2/courses/types', methods=['GET'])
        @authorize()
        @marshal(CoursesStatsFilterSchema(exclude=['interval']), parameter_name='filters', locations=['query'])
        def get_courses_types_v2(filters):
            print('[API::v2] get_courses_types_v2', filters)
            try:
                self.extractWorkspace(filters)
                total = self.course_service.get_courses_types(filters)
                return jsonify(total)
            except Exception as e:
                return self.handleException(e, 'Analytics API > Course v2 > Types')

        @doc(tags=['course'], description='Returns the ranking of content types, id and name in subaggregation.')
        @self.app.route('/api/v2/courses/content/types', methods=['GET'])
        @authorize()
        @marshal(CoursesStatsFilterSchema(exclude=['interval']), parameter_name='filters', locations=['query'])
        def get_courses_content_types_v2(filters):
            print('[API::v2] get_courses_content_types_v2', filters)
            try:
                self.extractWorkspace(filters)
                total = self.course_service.get_courses_content_types(filters)
                return jsonify(total)
            except Exception as e:
                return self.handleException(e, 'Analytics API > Course v2 > Content Types')

        @doc(tags=['course'], description='Course listing')
        @self.app.route('/api/v2/courses/list', methods=['GET'])
        @authorize()
        @marshal(CoursesListingFilterSchema(), parameter_name='filters', locations=['query'])
        def get_courses_list_v2(filters):
            print('[API::v2] get_courses_list_v2', filters)
            try:
                self.extractWorkspace(filters)
                total = self.course_service.get_courses_list(filters)
                return jsonify(total)
            except Exception as e:
                return self.handleException(e, 'Analytics API > Course v2 > List')

    @staticmethod
    def handleException(e: Exception, message: str):
        if isinstance(e, NotFoundError):
            return jsonify(error=str(e)), HTTPStatus.NOT_FOUND
        else:
            traceback.print_exc()
            DiscordWebhookLogger().emit_short_message(message, e)
            # raise e  # re-raise # TODO fix keeps-flask error handling
            return jsonify(error=str(e)), HTTPStatus.BAD_REQUEST

    @staticmethod
    def extractWorkspace(filter):
        workspace_id = request.x_client
        filter.workspace_id = workspace_id
        return filter
