import traceback
from http import HTTPStatus

from controller.schema.user import (
    UserEnrollmentsListingFilterSchema,
    UserFilterSchema,
    UserListingFilterSchema,
    UserTotalFilterSchema,
)
from custom.discord_webhook_logger import DiscordWebhookLogger
from domain.common.authorize import authorize
from domain.course.services import CourseService
from domain.user.services import UserService
from elasticsearch import NotFoundError
from flask import jsonify
from flask_apispec import doc
from injector import inject
from keeps_flask.app import Application
from keeps_flask.marshmallow import marshal

# from tasks.indexer import task_user_indexer
from tasks.celery import task_indexer_users


class UserEndpoints:
    @inject
    def __init__(
        self,
        app: Application,
        user_service: UserService,
        course_service: CourseService,
    ):
        self.app = app
        self.user_service = user_service
        self.course_service = course_service

    def register_endpoints(self):
        """
        Contact endpoints factory functions.
        """

        @doc(tags=['user'], description='Returns general data for the specified user')
        @self.app.route('/api/v1/user/<user_id>', methods=['GET'])
        @authorize()
        @marshal(UserFilterSchema(), parameter_name='filters', locations=['query'])
        def get_user_data(user_id, filters):
            # print('[API] get_user_data', user_id, filters)
            try:
                user = self.user_service.get_user_data(user_id, filters)
                if not user['total']:
                    return '', HTTPStatus.NOT_FOUND
                return jsonify(user)
            except NotFoundError as e:
                return jsonify(str(e)), HTTPStatus.NOT_FOUND
            except Exception as e:
                # keeps_flask is suppressing the error (no logging)
                traceback.print_exc()
                print('Aditional info:', getattr(e, 'info', '-'))
                DiscordWebhookLogger().emit_short_message('Analytics API > User > ID: ' + user_id, e)
                raise e

        @doc(tags=['user'], description='Returns courses which the the specified user is enrolled')
        @self.app.route('/api/v1/user/<user_id>/enrollments', methods=['GET'])
        @authorize()
        @marshal(UserEnrollmentsListingFilterSchema(), parameter_name='filters', locations=['query'])
        def get_user_enrollments(user_id, filters):
            # print('[API] get_user_enrollments', user_id, filters)
            try:
                user = self.user_service.get_user_enrollments(user_id, filters)
                return jsonify(user)
            except NotFoundError as e:
                return jsonify(str(e)), HTTPStatus.NOT_FOUND
            except Exception as e:
                # keeps_flask is suppressing the error (no logging)
                traceback.print_exc()
                DiscordWebhookLogger().emit_short_message('Analytics API > User > Enrollments > ID: ' + user_id, e)
                raise e

        @doc(tags=['user'], description='Returns the total users registered in the period')
        @self.app.route('/api/v1/users', methods=['GET'])
        @authorize()
        @marshal(UserTotalFilterSchema(), parameter_name='filters', locations=['query'])
        def get_total_users(filters):
            # print('[API] get_total_users', filters)
            try:
                user_totals = self.user_service.get_total_users(filters)
                return jsonify(user_totals)
            except NotFoundError as e:
                return jsonify(str(e)), HTTPStatus.NOT_FOUND
            except Exception as e:
                # keeps_flask is suppressing the error (no logging)
                traceback.print_exc()
                DiscordWebhookLogger().emit_short_message('Analytics API > Users', e)
                raise e

        @doc(tags=['user'], description='Returns the total active users with activities in the period')
        @self.app.route('/api/v1/users/active', methods=['GET'])
        @authorize()
        @marshal(UserTotalFilterSchema(), parameter_name='filters', locations=['query'])
        def get_total_active_users(filters):
            # print('[API] get_total_active_users', filters)
            try:
                user_totals = self.user_service.get_active_users(filters)
                return jsonify(user_totals)
            except NotFoundError as e:
                return jsonify(str(e)), HTTPStatus.NOT_FOUND
            except Exception as e:
                # keeps_flask is suppressing the error (no logging)
                traceback.print_exc()
                DiscordWebhookLogger().emit_short_message('Analytics API > Users > Active', e)
                raise e

        @doc(tags=['user'], description='Returns the distribution of course enrollments per users in the period')
        @self.app.route('/api/v1/users/enrollment/distribution', methods=['GET'])
        @authorize()
        @marshal(UserTotalFilterSchema(exclude=['interval']), parameter_name='filters', locations=['query'])
        def get_distribution_users_enrollments(filters):
            # print('[API] get_distribution_users_enrollments', filters)
            try:
                response = self.user_service.get_distribution_users_enrollments(filters)
                return jsonify(response)
            except NotFoundError as e:
                return jsonify(str(e)), HTTPStatus.NOT_FOUND
            except Exception as e:
                # keeps_flask is suppressing the error (no logging)
                traceback.print_exc()
                DiscordWebhookLogger().emit_short_message('Analytics API > Users > Enrollments > Distribution', e)
                raise e

        @doc(tags=['user'], description='Returns the average ratio of engagement factor for users, in the period')
        @self.app.route('/api/v1/users/engagement/rate', methods=['GET'])
        @authorize()
        @marshal(UserTotalFilterSchema(exclude=['interval', 'start_date']), parameter_name='filters', locations=['query'])
        def get_engagement_rate(filters):
            # print('[API] get_engagement_rate', filters)
            try:
                response = self.user_service.get_engagement_rate(filters)
                return jsonify(response)
            except NotFoundError as e:
                return jsonify(str(e)), HTTPStatus.NOT_FOUND
            except Exception as e:
                # keeps_flask is suppressing the error (no logging)
                traceback.print_exc()
                DiscordWebhookLogger().emit_short_message('Analytics API > Users > Engagement > Rate', e)
                raise e

        @doc(tags=['user'], description='Returns the average hours of content consumed per user, in the period')
        @self.app.route('/api/v1/users/content/consumed', methods=['GET'])
        @authorize()
        @marshal(UserTotalFilterSchema(exclude=['interval']), parameter_name='filters', locations=['query'])
        def get_content_consumed_per_user(filters):
            # print('[API] get_content_consumed_per_user', filters)
            try:
                response = self.user_service.get_content_consumed_per_user(filters)
                return jsonify(response)
            except NotFoundError as e:
                return jsonify(str(e)), HTTPStatus.NOT_FOUND
            except Exception as e:
                # keeps_flask is suppressing the error (no logging)
                traceback.print_exc()
                DiscordWebhookLogger().emit_short_message('Analytics API > Users > Content Consumed', e)
                raise e

        @doc(tags=['user'], description='Returns total users that created a course or channel in the period')
        @self.app.route('/api/v1/users/creators', methods=['GET'])
        @authorize()
        @marshal(UserTotalFilterSchema(exclude=['interval']), parameter_name='filters', locations=['query'])
        def get_users_creators(filters):
            # print('[API] get_users_creators', filters)
            try:
                response = self.user_service.get_users_creators(filters)
                return jsonify(response)
            except NotFoundError as e:
                return jsonify(str(e)), HTTPStatus.NOT_FOUND
            except Exception as e:
                # keeps_flask is suppressing the error (no logging)
                traceback.print_exc()
                DiscordWebhookLogger().emit_short_message('Analytics API > Users > Creators', e)
                raise e

        @doc(tags=['user'], description='Returns the ranking of course categories with enrollment in the period')
        @self.app.route('/api/v1/users/enrollment/categories', methods=['GET'])
        @authorize()
        @marshal(UserTotalFilterSchema(exclude=['interval']), parameter_name='filters', locations=['query'])
        def get_users_enrollment_categories(filters):
            # print('[API] get_users_enrollment_categories', filters)
            try:
                response = self.user_service.get_users_enrollment_categories(filters)
                return jsonify(response)
            except NotFoundError as e:
                return jsonify(str(e)), HTTPStatus.NOT_FOUND
            except Exception as e:
                # keeps_flask is suppressing the error (no logging)
                traceback.print_exc()
                DiscordWebhookLogger().emit_short_message('Analytics API > Users > Enrollments > Categories', e)
                raise e

        @doc(tags=['user'], description='Returns the ranking of most consumed content types')
        @self.app.route('/api/v1/users/activity/content-types', methods=['GET'])
        @authorize()
        @marshal(UserTotalFilterSchema(exclude=['interval']), parameter_name='filters', locations=['query'])
        def get_users_activity_content_types(filters):
            # print('[API] get_users_activity_content_types', filters)
            try:
                response = self.user_service.get_users_activity_content_types(filters)
                return jsonify(response)
            except NotFoundError as e:
                return jsonify(str(e)), HTTPStatus.NOT_FOUND
            except Exception as e:
                # keeps_flask is suppressing the error (no logging)
                traceback.print_exc()
                DiscordWebhookLogger().emit_short_message('Analytics API > Users > Activity > Content Types', e)
                raise e

        @doc(tags=['user'], description='Users listing')
        @self.app.route('/api/v1/users/list', methods=['GET'])
        @authorize()
        @marshal(UserListingFilterSchema(), parameter_name='filters', locations=['query'])
        def get_users_list(filters):
            # print('[API] get_users_list', filters)
            try:
                response = self.user_service.get_users_list(filters)
                return jsonify(response)
            except NotFoundError as e:
                return jsonify(str(e)), HTTPStatus.NOT_FOUND
            except Exception as e:
                # keeps_flask is suppressing the error (no logging)
                traceback.print_exc()
                DiscordWebhookLogger().emit_short_message('Analytics API > User > List ', e)
                raise e

        @doc(tags=['user'], description='Recreate User Index')
        @self.app.route('/api/v1/users/load-data', methods=['POST'])
        @authorize()
        def post_reload_users_task():
            try:
                task_indexer_users.delay()
                return jsonify({'task_submitted': True})
            except Exception as e:
                traceback.print_exc()
                DiscordWebhookLogger().emit_short_message('Analytics API > Users > Load Data (Reindex) ', e)
                return jsonify(str(e)), HTTPStatus.INTERNAL_SERVER_ERROR
