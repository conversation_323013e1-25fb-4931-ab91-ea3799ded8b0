from http import HTTPStatus

from controller.schema.elasticsearch import ElasticsearchBodySchema
from domain.common.authorize import authorize
from domain.elasticsearch.services import ElasticSearchService
from flask_apispec import doc
from injector import inject
from keeps_flask.app import Application
from keeps_flask.marshmallow import marshal, unmarshal
from marshmallow import INCLUDE


class ElasticSearchEndpoints:
    @inject
    def __init__(
        self,
        app: Application,
        es_service: ElasticSearchService
    ):
        self.app = app
        self.es_service = es_service

    def register_endpoints(self):
        """
        ElasticSearch endpoints factory functions.
        """

        @doc(tags=['elastic'], description='POST Search DSL.')
        @self.app.route('/api/v1/es/<index>/search', methods=['POST'])
        @authorize()
        @marshal(ElasticsearchBodySchema(many=False, unknown=INCLUDE), parameter_name='body', locations={})
        @unmarshal(ElasticsearchBodySchema(many=False, unknown=INCLUDE), code=HTTPStatus.OK)
        def post_search_dsl(index, body):
            """
            ES Search

            Args:
                index: Elasticsearch Index name
                body: DSL query

            Returns:
                Query Result
            """
            return self.es_service.search(index, body)

        @doc(tags=['elastic'], description='POST Count DSL.')
        @self.app.route('/api/v1/es/<index>/count', methods=['POST'])
        @authorize()
        @marshal(ElasticsearchBodySchema(many=False, unknown=INCLUDE), parameter_name='body', locations={})
        @unmarshal(ElasticsearchBodySchema(many=False, unknown=INCLUDE), code=HTTPStatus.OK)
        def post_count_dsl(index, body):
            """
            ES Count

            Args:
                index: Elasticsearch Index name
                body: DSL query

            Returns:
                Query Result
            """
            return self.es_service.count(index, body)

        @doc(tags=['elastic'], description='DELETE Remove a index.')
        @self.app.route('/api/v1/es/<index>', methods=['DELETE'])
        @authorize()
        def delete_index(index):
            """
            ES Remove Index

            Args:
                index: Elasticsearch Index name

            Returns:
                Query Result
            """
            return self.es_service.remove_index(index)

        @doc(tags=['elastic'], description='POST Create a doc.')
        @self.app.route('/api/v1/es/<index>/create/<id>', methods=['POST'])
        @authorize()
        @marshal(ElasticsearchBodySchema(many=False, unknown=INCLUDE), parameter_name='body', locations={})
        def create(index, id, body):
            """
            ES Create a doc

            Args:
                index: Elasticsearch Index name

            Returns:
                Query Result
            """
            return self.es_service.create(index, id=id, body=body)
