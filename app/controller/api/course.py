import traceback
from http import HTTPStatus

from controller.schema.course import (
    CourseContentsListingFilterSchema,
    CourseEnrollmentsListingFilterSchema,
    CourseFilterSchema,
    CourseListingFilterSchema,
    CourseTotalFilterSchema,
)
from custom.discord_webhook_logger import DiscordWebhookLogger
from domain.common.authorize import authorize
from domain.common.dependencies import CourseService
from elasticsearch import NotFoundError
from flask import jsonify
from flask_apispec import doc
from injector import inject
from keeps_flask.app import Application
from keeps_flask.marshmallow import marshal

# from tasks.indexer import task_indexer_courses
from tasks.celery import task_indexer_courses

# from ..error import KeepsException


class CourseEndpoints:
    @inject
    def __init__(
        self,
        app: Application,
        course_service: CourseService
    ):
        self.app = app
        self.course_service = course_service

    def register_endpoints(self):
        """
        Contact endpoints factory functions.
        """

        @doc(tags=['user'], description='Returns general data for the specified course')
        @self.app.route('/api/v1/course/<course_id>', methods=['GET'])
        @authorize()
        @marshal(CourseFilterSchema(), parameter_name='filters', locations=['query'])
        def get_course_data(course_id, filters):
            # print('[API] get_course_data', filters)
            try:
                course = self.course_service.get_course_data(course_id, filters)
                if not course['total']:
                    return '', HTTPStatus.NOT_FOUND
                return jsonify(course)
            except NotFoundError as e:
                return jsonify(str(e)), HTTPStatus.NOT_FOUND
            except Exception as e:
                # keeps_flask is suppressing the error (no logging)
                traceback.print_exc()
                DiscordWebhookLogger().emit_short_message('Analytics API > Course > ID: ' + course_id, e)
                raise e

        @doc(tags=['user'], description='Returns user enrollments for the specified course')
        @self.app.route('/api/v1/course/<course_id>/enrollments', methods=['GET'])
        @authorize()
        @marshal(CourseEnrollmentsListingFilterSchema(), parameter_name='filters', locations=['query'])
        def get_course_enrollments(course_id, filters):
            # print('[API] get_course_enrollments', filters)
            try:
                enrollments = self.course_service.get_course_enrollments(course_id, filters)
                return jsonify(enrollments)
            except NotFoundError as e:
                return jsonify(str(e)), HTTPStatus.NOT_FOUND
            except Exception as e:
                # keeps_flask is suppressing the error (no logging)
                traceback.print_exc()
                DiscordWebhookLogger().emit_short_message('Analytics API > Course > Enrollments > ID: ' + course_id, e)
                raise e

        @doc(tags=['user'], description='Returns the content list of the specified course')
        @self.app.route('/api/v1/course/<course_id>/contents', methods=['GET'])
        @authorize()
        @marshal(CourseContentsListingFilterSchema(), parameter_name='filters', locations=['query'])
        def get_course_contents(course_id, filters):
            print('[API] get_course_contents', filters)
            try:
                enrollments = self.course_service.get_course_contents(course_id, filters)
                return jsonify(enrollments)
            except NotFoundError as e:
                return jsonify(str(e)), HTTPStatus.NOT_FOUND
            except Exception as e:
                # keeps_flask is suppressing the error (no logging)
                traceback.print_exc()
                raise e

        @doc(tags=['course'], description='Returns the total courses created in the period, and aggregation of date histogram')
        @self.app.route('/api/v1/courses', methods=['GET'])
        @authorize()
        @marshal(CourseTotalFilterSchema(), parameter_name='filters', locations=['query'])
        def get_total_courses(filters):
            try:
                total = self.course_service.get_total_courses(filters)
                return jsonify(total)
            except NotFoundError as e:
                return jsonify(str(e)), HTTPStatus.NOT_FOUND
            except Exception as e:
                # keeps_flask is suppressing the error (no logging)
                traceback.print_exc()
                DiscordWebhookLogger().emit_short_message('Analytics API > Courses', e)
                raise e

        @doc(tags=['course'], description='Returns the total courses with enrollment started in the period + date histogram')
        @self.app.route('/api/v1/courses/started', methods=['GET'])
        @authorize()
        @marshal(CourseTotalFilterSchema(), parameter_name='filters', locations=['query'])
        def get_total_courses_started(filters):
            try:
                course_totals = self.course_service.get_total_courses_started(filters)
                return jsonify(course_totals)
            except NotFoundError as e:
                return jsonify(str(e)), HTTPStatus.NOT_FOUND
            except Exception as e:
                # keeps_flask is suppressing the error (no logging)
                traceback.print_exc()
                DiscordWebhookLogger().emit_short_message('Analytics API > Courses > Started', e)
                raise e

        @doc(tags=['course'], description='Returns the total courses with enrollment ending in the period + date histogram.')
        @self.app.route('/api/v1/courses/completed', methods=['GET'])
        @authorize()
        @marshal(CourseTotalFilterSchema(), parameter_name='filters', locations=['query'])
        def get_total_courses_completed(filters):
            try:
                course_totals = self.course_service.get_total_courses_completed(filters)
                return jsonify(course_totals)
            except NotFoundError as e:
                return jsonify(str(e)), HTTPStatus.NOT_FOUND
            except Exception as e:
                # keeps_flask is suppressing the error (no logging)
                traceback.print_exc()
                DiscordWebhookLogger().emit_short_message('Analytics API > Courses > Completed', e)
                raise e

        @doc(tags=['course'], description='Get Total Active Users.')
        @self.app.route('/api/v1/courses/users/active', methods=['GET'])
        @authorize()
        @marshal(CourseTotalFilterSchema(many=False), parameter_name='filters', locations=['query'])
        def get_total_courses_active_users(filters):
            try:
                course_totals = self.course_service.get_total_active_users(filters)
                return jsonify(course_totals)
            except NotFoundError as e:
                return jsonify(str(e)), HTTPStatus.NOT_FOUND
            except Exception as e:
                # keeps_flask is suppressing the error (no logging)
                traceback.print_exc()
                DiscordWebhookLogger().emit_short_message('Analytics API > Courses > Users > Active', e)
                raise e

        @doc(tags=['course'], description='Returns the total enrollments created in the period, and aggregation of date histogram')
        @self.app.route('/api/v1/courses/new-enrollment', methods=['GET'])
        @authorize()
        @marshal(CourseTotalFilterSchema(), parameter_name='filters', locations=['query'])
        def get_total_courses_new_enrollment(filters):
            try:
                total = self.course_service.get_total_courses_new_enrollment(filters)
                return jsonify(total)
            except NotFoundError as e:
                return jsonify(str(e)), HTTPStatus.NOT_FOUND
            except Exception as e:
                # keeps_flask is suppressing the error (no logging)
                traceback.print_exc()
                DiscordWebhookLogger().emit_short_message('Analytics API > Courses > New Enrollments', e)
                raise e

        @doc(tags=['course'], description='Returns the ratio of completed enrollments over total, in the period')
        @self.app.route('/api/v1/courses/completed/ratio', methods=['GET'])
        @authorize()
        @marshal(CourseTotalFilterSchema(only=['company_id', 'start_date', 'end_date']), parameter_name='filters', locations=['query'])
        def get_ratio_courses_completed(filters):
            try:
                total = self.course_service.get_ratio_courses_completed(filters)
                return jsonify(total)
            except NotFoundError as e:
                return jsonify(str(e)), HTTPStatus.NOT_FOUND
            except Exception as e:
                # keeps_flask is suppressing the error (no logging)
                traceback.print_exc()
                DiscordWebhookLogger().emit_short_message('Analytics API > Courses > Completed > Ratio', e)
                raise e

        @doc(tags=['course'], description='Returns the sum of total hours of content of courses available in the period')
        @self.app.route('/api/v1/courses/content/available', methods=['GET'])
        @authorize()
        @marshal(CourseTotalFilterSchema(exclude=['interval']), parameter_name='filters', locations=['query'])
        def get_total_courses_content_hours_available(filters):
            try:
                total = self.course_service.get_total_courses_content_hours_available(filters)
                return jsonify(total)
            except NotFoundError as e:
                return jsonify(str(e)), HTTPStatus.NOT_FOUND
            except Exception as e:
                # keeps_flask is suppressing the error (no logging)
                traceback.print_exc()
                raise e

        @doc(tags=['course'], description='Returns the sum of total hours of content of activities in the period')
        @self.app.route('/api/v1/courses/content/consumed', methods=['GET'])
        @authorize()
        @marshal(CourseTotalFilterSchema(exclude=['interval']), parameter_name='filters', locations=['query'])
        def get_total_courses_content_hours_consumed(filters):
            try:
                total = self.course_service.get_total_courses_content_hours_consumed(filters)
                return jsonify(total)
            except NotFoundError as e:
                return jsonify(str(e)), HTTPStatus.NOT_FOUND
            except Exception as e:
                # keeps_flask is suppressing the error (no logging)
                traceback.print_exc()
                DiscordWebhookLogger().emit_short_message('Analytics API > Courses > Content > Consumed', e)
                raise e

        @doc(tags=['course'], description='Returns the average ratings of the courses, per enrollment rating')
        @self.app.route('/api/v1/courses/rating', methods=['GET'])
        @authorize()
        @marshal(CourseTotalFilterSchema(exclude=['interval']), parameter_name='filters', locations=['query'])
        def get_courses_ratings(filters):
            try:
                total = self.course_service.get_courses_ratings(filters)
                return jsonify(total)
            except NotFoundError as e:
                return jsonify(str(e)), HTTPStatus.NOT_FOUND
            except Exception as e:
                # keeps_flask is suppressing the error (no logging)
                traceback.print_exc()
                DiscordWebhookLogger().emit_short_message('Analytics API > Courses > Rating', e)
                raise e

        @doc(tags=['course'], description='Returns the ranking of course categories, id and name in subaggregation.')
        @self.app.route('/api/v1/courses/categories', methods=['GET'])
        @authorize()
        @marshal(CourseTotalFilterSchema(exclude=['interval']), parameter_name='filters', locations=['query'])
        def get_courses_categories(filters):
            try:
                total = self.course_service.get_courses_categories(filters)
                return jsonify(total)
            except NotFoundError as e:
                return jsonify(str(e)), HTTPStatus.NOT_FOUND
            except Exception as e:
                # keeps_flask is suppressing the error (no logging)
                traceback.print_exc()
                DiscordWebhookLogger().emit_short_message('Analytics API > Courses > Categories', e)
                raise e

        @doc(tags=['course'], description='Returns the ranking of course types, id and name in subaggregation.')
        @self.app.route('/api/v1/courses/types', methods=['GET'])
        @authorize()
        @marshal(CourseTotalFilterSchema(exclude=['interval']), parameter_name='filters', locations=['query'])
        def get_courses_types(filters):
            try:
                total = self.course_service.get_courses_types(filters)
                return jsonify(total)
            except NotFoundError as e:
                return jsonify(str(e)), HTTPStatus.NOT_FOUND
            except Exception as e:
                # keeps_flask is suppressing the error (no logging)
                traceback.print_exc()
                DiscordWebhookLogger().emit_short_message('Analytics API > Courses > Types', e)
                raise e

        @doc(tags=['course'], description='Returns the ranking of content types, id and name in subaggregation.')
        @self.app.route('/api/v1/courses/content/types', methods=['GET'])
        @authorize()
        @marshal(CourseTotalFilterSchema(exclude=['interval']), parameter_name='filters', locations=['query'])
        def get_courses_content_types(filters):
            try:
                total = self.course_service.get_courses_content_types(filters)
                return jsonify(total)
            except NotFoundError as e:
                return jsonify(str(e)), HTTPStatus.NOT_FOUND
            except Exception as e:
                # keeps_flask is suppressing the error (no logging)
                traceback.print_exc()
                DiscordWebhookLogger().emit_short_message('Analytics API > Courses > Content > Types', e)
                raise e

        @doc(tags=['course'], description='Course listing')
        @self.app.route('/api/v1/courses/list', methods=['GET'])
        @authorize()
        @marshal(CourseListingFilterSchema(), parameter_name='filters', locations=['query'])
        def get_courses_list(filters):
            try:
                total = self.course_service.get_courses_list(filters)
                return jsonify(total)
            except NotFoundError as e:
                return jsonify(str(e)), HTTPStatus.NOT_FOUND
            except Exception as e:
                # keeps_flask is suppressing the error (no logging)
                traceback.print_exc()
                DiscordWebhookLogger().emit_short_message('Analytics API > Courses > List', e)
                raise e

        @doc(tags=['course'], description='Recreate Course Index')
        @self.app.route('/api/v1/courses/load-data', methods=['POST'])
        @authorize()
        def post_reload_courses_task():
            try:
                # task_indexer_courses()
                task_indexer_courses.delay()
                return jsonify({'task_submitted': True})
            except Exception as e:
                DiscordWebhookLogger().emit_short_message('Analytics API > Courses > Load Data (Reindex)', e)
                return jsonify(str(e)), HTTPStatus.INTERNAL_SERVER_ERROR
