from flask import redirect
from injector import inject
from keeps_flask.app import Application


class RootEndpoints:
    @inject
    def __init__(
        self,
        app: Application,
    ):
        self.app = app

    def register_endpoints(self):
        """
        Contact endpoints factory functions.
        """
        @self.app.route('/', methods=['GET'])
        def get_redirect_to_swagger():
            return redirect('swagger/')
