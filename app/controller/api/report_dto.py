from dataclasses import dataclass


@dataclass
class AuthenticatedUser:
    id: str
    name: str
    email: str
    locale: str = "pt-BR"
    time_zone: str = "America/Sao_Paulo"

@dataclass
class ReportDto:
    type_name: str
    request_user: AuthenticatedUser
    workspace_id: str
    language: str
    object_id: str
    file_format: str = 'PDF'
    filters: dict = None
    time_zone: str = 'America/Sao_Paulo'
