from http import HTTPStatus

from controller.api.report_dto import ReportDto
from controller.schema.pagination import build_pagination, paginate_schema
from controller.schema.report import (
    CoursePresentation,
    ExportSchema,
    KonquestCompaniesHealthCheckExportSchema,
    KonquestContentsExportSchema,
    KonquestGroupChannelUserExportSchema,
    KonquestGroupMissionUserExportSchema,
    KonquestMissionEnrollmentExportSchema,
    KonquestMissionEnrollmentQuizzesExportSchema,
    KonquestMissionEvaluationsExportSchema,
    KonquestMissionExportSchema,
    KonquestMissionQuizAnswersExportSchema,
    KonquestPulseQuizAnswersExportSchema,
    KonquestPulsesChannelsExportSchema,
    KonquestTrailEnrollmentExportSchema,
    KonquestTrailExportSchema,
    KonquestTrailsCompletionRateExportSchema,
    KonquestUserConsumptionsExportFilterSchema,
    KonquestUserConsumptionsExportSchema,
    KonquestUserPulsesActivitiesExportSchema,
    KonquestUsersAccessByDateExportSchema,
    KonquestUsersExportSchema,
    KonquestUsersGeneralStatisticsExportSchema,
    KonquestWorkspacePresentation,
    SmartzapEnrollmentsExportSchema,
    SmartzapUserActivitiesExportSchema,
    UserPresentation,
)
from domain.common.authorize import authorize
from domain.common.dependencies import Report, ReportService, ReportTypeService
from domain.report.schemas import ReportSchema, ReportTypeSchema
from domain.report.services import ReportService as ReportServiceClass
from domain.report.services import ReportServiceError
from domain.report.services import ReportTypeService as ReportTypeServiceClass
from flask import jsonify, request
from flask_apispec import doc
from flask_extensions import webfilters
from injector import inject
from keeps_flask.app import Application
from keeps_flask.marshmallow import marshal, unmarshal
from marshmallow import INCLUDE
from reports.constants import (
    KONQUEST_COMPANIES_HEALTH_CHECK,
    KONQUEST_MISSION_ENROLLMENT_QUIZZES_PRESENTATION,
    KONQUEST_MISSION_EVALUATIONS_STATISTICS_EXPORT,
    KONQUEST_USER_CONSUMPTIONS_EXPORT,
    KONQUEST_USER_GENERAL_STATISTICS_EXPORT,
    MYACCOUNT_USER_PERMISSIONS_EXPORT,
    SMARTZAP_ENROLLMENTS_EXPORT,
    SMARTZAP_USER_ACTIVITIES_EXPORT,
)
from reports.report_test import test_report

RESPONSE_MESSAGE = 'Report In Progress'
class ReportEndpoints:
    @inject
    def __init__(
        self,
        app: Application,
        service: ReportService,
        report: Report,
        report_type_service: ReportTypeService
    ):
        self.app = app
        self._service: ReportServiceClass = service
        self._type_service: ReportTypeServiceClass = report_type_service
        self.report = report

    def register_endpoints(self):
        """
        Contact endpoints factory functions.
        """

        @doc(tags=['report'], description='Read Reports')
        @self.app.route('/api/v1/reports', methods=['GET'])
        @authorize()
        @marshal(paginate_schema(ReportSchema), parameter_name='pagination', locations=['query'])
        @unmarshal(paginate_schema(ReportSchema), code=200)
        def read_reports(pagination):
            x_client = request.x_client
            filters = webfilters.load_filters()
            try:
                reports, pages = self._service.load_filters(filters, x_client)
            except ReportServiceError as e:
                return jsonify(str(e)), e.status_code
            return build_pagination(reports, pages)

        @doc(tags=['report'], description='Read Detail Report')
        @self.app.route('/api/v1/reports/<report_id>', methods=['GET'])
        @authorize()
        def detail_report(report_id):
            x_client = request.x_client
            try:
                report = self._service.load_report(report_id, x_client)
            except ReportServiceError as e:
                return jsonify(str(e)), e.status_code
            return jsonify(ReportSchema(many=False).dump(report)), HTTPStatus.OK

        @doc(tags=['report'], description='Delete Report')
        @self.app.route('/api/v1/reports/<report_id>', methods=['DELETE'])
        @authorize()
        def delete_report(report_id):
            self._service.workspace_id = request.x_client
            try:
                self._service.delete(report_id)
            except ReportServiceError as e:
                return jsonify(str(e)), e.status_code

            return '', HTTPStatus.NO_CONTENT

        @doc(tags=['report'], description='Read Reports')
        @self.app.route('/api/v1/reports-types', methods=['GET'])
        @authorize()
        @marshal(paginate_schema(ReportTypeSchema), parameter_name='pagination', locations=['query'])
        @unmarshal(paginate_schema(ReportTypeSchema), code=200)
        def read_report_types(pagination):
            filters = webfilters.load_filters()
            types, pages = self._type_service.load_filters(filters)
            return build_pagination(types, pages)

        @doc(tags=['report'], description='Smartzap Course Overview Report in PDF')
        @self.app.route('/api/v1/reports/smartzap-course-presentation', methods=['POST'])
        @authorize()
        @marshal(CoursePresentation(many=False, unknown=INCLUDE), parameter_name='body', locations={})
        def smartzap_report_course_overview_pdf(body):
            """
            Generate presentations for the given course IDs from the request body. Handles both single and multiple course IDs.
            Args:
                body (dict): Request body with "course_id" key holding string or list of course IDs.
                Returns:
                JSON response: Single presentation or list of presentations depending on the input.
            """
            type_name = 'smartzap-course-presentation'
            course_ids = body["course_id"]

            if not course_ids:
                return jsonify({'error': 'No course_id provided'}), HTTPStatus.BAD_REQUEST

            if isinstance(course_ids, str):
                course_ids = [course_ids]

            list_responses = []
            for course_id in course_ids:
                try:
                    response = create_presentation(self._service, type_name, course_id)
                    list_responses.append(response[0].json)
                except IndexError:
                    list_responses.append({'error': f'No data for course_id {course_id}'})

            return jsonify(list_responses)

        @doc(tags=['report'], description='Konquest Course Overview Report in PDF')
        @self.app.route('/api/v1/reports/konquest-course-presentation', methods=['POST'])
        @authorize()
        @marshal(CoursePresentation(many=False, unknown=INCLUDE), parameter_name='body', locations={})
        def konquest_report_course_overview_pdf(body):
            """
            """
            type_name = 'konquest-mission-presentation'
            course_ids = body["course_id"]

            if isinstance(course_ids, str):
                return create_presentation(self._service, type_name, course_ids)

            list_responses = []
            for course_id in course_ids:
                response = create_presentation(self._service, type_name, course_id)
                list_responses.append(response[0].json)

            return jsonify(list_responses), HTTPStatus.ACCEPTED


        @doc(tags=['report'], description='Konquest Workspace Overview Report in PDF')
        @self.app.route('/api/v1/reports/konquest-workspace-presentation', methods=['POST'])
        @authorize()
        @marshal(KonquestWorkspacePresentation(many=False, unknown=INCLUDE), parameter_name='body', locations={})
        def konquest_report_workspace_overview_pdf(body):
            """
            """
            type_name = 'konquest-workspace-presentation'
            workspace_id = body["workspace_id"]
            return create_presentation(self._service, type_name, workspace_id)

        @doc(tags=['report'], description='Konquest User Overview Report in PDF')
        @self.app.route('/api/v1/reports/konquest-user-presentation', methods=['POST'])
        @authorize()
        @marshal(UserPresentation(many=False, unknown=INCLUDE), parameter_name='body', locations={})
        def konquest_report_user_overview_pdf(body):
            """
            """
            type_name = 'konquest-user-presentation'
            user_ids = body["user_id"]

            if isinstance(user_ids, str):
                return create_presentation(self._service, type_name, user_ids)

            list_responses = []
            for user_id in user_ids:
                response = create_presentation(self._service, type_name, user_id)
                list_responses.append(response[0].json)
            return jsonify(list_responses)

        @doc(tags=['report'], description='Konquest Mission Enrollment Quizzes in PDF')
        @self.app.route('/api/v1/reports/konquest-mission-enrollment-quizzes', methods=['POST'])
        @authorize()
        @marshal(KonquestMissionEnrollmentQuizzesExportSchema(), parameter_name='data', locations=['query'])
        def konquest_report_mission_enrollment_quizzes_pdf(data):
            """
            """
            type_name = KONQUEST_MISSION_ENROLLMENT_QUIZZES_PRESENTATION
            return create_presentation(self._service, type_name, None, data.get("filters"))

        @doc(tags=['report'], description='Konquest Workspace Mission Report in XLSX or CSV')
        @self.app.route('/api/v1/reports/konquest-missions-export', methods=['POST'])
        @authorize()
        @marshal(KonquestMissionExportSchema(), parameter_name='data', locations=['query'])
        def konquest_report_workspace_mission_export(data):
            type_name = 'konquest-missions-export'
            return create_export(self._service, type_name, data.get("filters"), data["report_format"])

        @doc(tags=['report'], description='Konquest Workspace Trail Report in XLSX or CSV')
        @self.app.route('/api/v1/reports/konquest-trails-export', methods=['POST'])
        @authorize()
        @marshal(KonquestTrailExportSchema(), parameter_name='data', locations=['query'])
        def konquest_report_workspace_trail_export(data):
            type_name = 'konquest-trails-export'
            return create_export(self._service, type_name, data.get("filters"), data["report_format"])
        @doc(tags=['report'], description='Konquest Workspace Trails Enrollments Report in XLSX or CSV')
        @self.app.route('/api/v1/reports/konquest-trails-enrollments-export', methods=['POST'])
        @authorize()
        @marshal(KonquestTrailEnrollmentExportSchema(), parameter_name='data', locations=['query'])
        def konquest_report_trails_enrollments_export(data):
            type_name = 'konquest-trails-enrollments-export'
            return create_export(self._service, type_name, data.get("filters"), data["report_format"])


        @doc(tags=['report'], description='Konquest Workspace Pulses and Channels Report in XLSX or CSV')
        @self.app.route('/api/v1/reports/konquest-pulses-channels-export', methods=['POST'])
        @authorize()
        @marshal(KonquestPulsesChannelsExportSchema(), parameter_name='data', locations=['query'])
        def konquest_report_pulses_and_channels_export(data):
            type_name = 'konquest-pulses-channels-export'
            return create_export(self._service, type_name, data.get("filters"), data["report_format"])

        @doc(tags=['report'], description='Konquest Workspace Trail Completion Rate Report in XLSX or CSV')
        @self.app.route('/api/v1/reports/konquest-trails-completion-rate-export', methods=['POST'])
        @authorize()
        @marshal(KonquestTrailsCompletionRateExportSchema(), parameter_name='data', locations=['query'])
        def konquest_report_workspace_trail_completion_rate_export(data):
            type_name = 'konquest-trails-completion-rate-export'
            return create_export(self._service, type_name, data.get("filters"), data["report_format"])

        @doc(tags=['report'], description='Konquest Workspace Users Pulses Activities Report in XLSX or CSV')
        @self.app.route('/api/v1/reports/konquest-user-pulse-activities-export', methods=['POST'])
        @authorize()
        @marshal(KonquestUserPulsesActivitiesExportSchema(), parameter_name='data', locations=['query'])
        def konquest_report_user_pulses_activities_export(data):
            type_name = 'konquest-users-pulses-activities-export'
            return create_export(self._service, type_name, data.get("filters"), data["report_format"])

        @doc(tags=['report'], description='Konquest Workspace Missions Enrollments Report in XLSX or CSV')
        @self.app.route('/api/v1/reports/konquest-mission-enrollments-export', methods=['POST'])
        @authorize()
        @marshal(KonquestMissionEnrollmentExportSchema(many=False, unknown=INCLUDE), parameter_name='data', locations=['query'])
        def konquest_report_missions_enrollments_export(data):
            type_name = 'konquest-missions-enrollments-export'
            return create_export(self._service, type_name, data.get("filters"), data["report_format"])

        @doc(tags=['report'], description='Konquest Workspace Groups Missions Users Report in CSV or XLSX')
        @self.app.route('/api/v1/reports/konquest-groups-missions-users-export', methods=['POST'])
        @authorize()
        @marshal(KonquestGroupMissionUserExportSchema(), parameter_name='data', locations=['query'])
        def konquest_report_groups_missions_users_export(data):
            type_name = 'konquest-groups-missions-users-export'
            return create_export(self._service, type_name, data.get("filters"), data["report_format"])

        @doc(tags=['report'], description='Konquest Workspace Groups Channels Users Report in XLSX or CSV')
        @self.app.route('/api/v1/reports/konquest-groups-channels-users-export', methods=['POST'])
        @authorize()
        @marshal(KonquestGroupChannelUserExportSchema(), parameter_name='data', locations=['query'])
        def konquest_report_groups_channels_users_export(data):
            type_name = 'konquest-groups-channels-users-export'
            return create_export(self._service, type_name, data.get("filters"), data["report_format"])

        @doc(tags=['report'], description='Konquest Workspace Missions Quiz Answers Report in XLSX or CSV')
        @self.app.route('/api/v1/reports/konquest-missions-quizzes-answers-export', methods=['POST'])
        @authorize()
        @marshal(KonquestMissionQuizAnswersExportSchema(), parameter_name='data', locations=['query'])
        def konquest_report_workspace_missions_quiz_answers_export(data):
            type_name = 'konquest-missions-quizzes-answers-export'
            return create_export(self._service, type_name, data.get("filters"), data["report_format"])

        @doc(tags=['report'], description='Konquest Workspace Pulses Quiz Answers Report in XLSX or CSV')
        @self.app.route('/api/v1/reports/konquest-pulses-quizzes-answers-export', methods=['POST'])
        @authorize()
        @marshal(KonquestPulseQuizAnswersExportSchema(), parameter_name='data', locations=['query'])
        def konquest_report_workspace_pulses_quiz_answers_export(data):
            type_name = 'konquest-pulses-quizzes-answers-export'
            return create_export(self._service, type_name, data.get("filters"), data["report_format"])

        @doc(tags=['report'], description='Konquest Workspace All Users Report in XLSX or CSV')
        @self.app.route('/api/v1/reports/konquest-all-users-export', methods=['POST'])
        @authorize()
        @marshal(KonquestUsersExportSchema(), parameter_name='data', locations=['query'])
        def konquest_report_workspace_all_users_export(data):
            type_name = 'konquest-users-export'
            return create_export(self._service, type_name, data.get("filters"), data["report_format"])

        @doc(tags=['report'], description='Konquest Workspace Users Access by Date in XLSX or CSV')
        @self.app.route('/api/v1/reports/konquest-users-access-by-date-export', methods=['POST'])
        @authorize()
        @marshal(KonquestUsersAccessByDateExportSchema(), parameter_name='data', locations=['query'])
        def konquest_report_workspace_user_access_by_date_export(data):
            type_name = 'konquest-users-access-by-date-export'
            return create_export(self._service, type_name, data.get("filters"), data["report_format"])

        @doc(tags=['report'], description='Konquest Workspace Missions Evaluations Report in XLSX or CSV')
        @self.app.route('/api/v1/reports/konquest-missions-evaluations-export', methods=['POST'])
        @authorize()
        @marshal(KonquestMissionEvaluationsExportSchema(), parameter_name='data', locations=['query'])
        def konquest_report_missions_evaluations_export(data):
            type_name = 'konquest-missions-evaluations-export'
            return create_export(self._service, type_name, data.get("filters"), data["report_format"])

        @doc(tags=['report'], description='Konquest Workspace Missions Contents Report in XLSX or CSV')
        @self.app.route('/api/v1/reports/konquest-contents-export', methods=['POST'])
        @authorize()
        @marshal(KonquestContentsExportSchema(), parameter_name='data', locations=['query'])
        def konquest_report_contents_export(data):
            type_name = 'konquest-contents-export'
            return create_export(self._service, type_name, data.get("filters"), data["report_format"])

        @doc(tags=['report'], description='Konquest User Consumptions Report in XLSX or CSV')
        @self.app.route('/api/v1/reports/konquest-user-consumptions-export', methods=['POST'])
        @authorize()
        @marshal(KonquestUserConsumptionsExportSchema(), parameter_name='data', locations=['query'])
        def konquest_user_consumptions_export(data):
            type_name = KONQUEST_USER_CONSUMPTIONS_EXPORT
            return create_export(self._service, type_name, data.get("filters"), data["report_format"])

        @doc(tags=['report'], description='Konquest User Consumptions Report Data')
        @self.app.route('/api/v1/reports/raw-data/konquest-user-consumptions-export', methods=['GET'])
        @authorize()
        @marshal(KonquestUserConsumptionsExportFilterSchema(), parameter_name='filters', locations=['query'])
        def konquest_user_consumptions_export_raw_data(filters):
            type_name = KONQUEST_USER_CONSUMPTIONS_EXPORT
            x_client = request.x_client
            report = ReportDto(
                type_name=type_name,
                request_user=request.user,
                language=request.user.locale,
                workspace_id=x_client,
                object_id=x_client,
                filters=filters,
                time_zone=request.user.time_zone
            )
            try:
                raw_data = self._service.get_report_raw(report)
            except ReportServiceError as e:
                return jsonify(str(e)), e.status_code

            return jsonify({ "results": raw_data }), HTTPStatus.ACCEPTED

        @doc(tags=['report'], description='Konquest User General Statistics Report in XLSX or CSV')
        @self.app.route('/api/v1/reports/konquest-user-general-statistics-export', methods=['POST'])
        @authorize()
        @marshal(KonquestUsersGeneralStatisticsExportSchema(), parameter_name='data', locations=['query'])
        def konquest_user_general_statistics_export(data):
            type_name = KONQUEST_USER_GENERAL_STATISTICS_EXPORT
            return create_export(self._service, type_name, data.get("filters"), data["report_format"])

        @doc(tags=['report'], description='Konquest Mission Evaluations Statistics Report in XLSX or CSV')
        @self.app.route('/api/v1/reports/konquest-mission-evaluations-statistics-export', methods=['POST'])
        @authorize()
        @marshal(ExportSchema(), parameter_name='data', locations=['query'])
        def konquest_mission_evaluations_statistics_export(data):
            type_name = KONQUEST_MISSION_EVALUATIONS_STATISTICS_EXPORT
            return create_export(self._service, type_name, {}, data["report_format"])

        @doc(tags=['report'], description='Smartzap Course Enrollments Statistics Report in XLSX or CSV')
        @self.app.route('/api/v1/reports/smartzap-enrollments-exports', methods=['POST'])
        @authorize()
        @marshal(SmartzapEnrollmentsExportSchema(), parameter_name='data', locations=['query'])
        def smartzap_enrollments_statistics_export(data):
            type_name = SMARTZAP_ENROLLMENTS_EXPORT
            return create_export(self._service, type_name, data.get("filters"), data["report_format"])

        @doc(tags=['report'], description='Smartzap User Activities Statistics Report in XLSX or CSV')
        @self.app.route('/api/v1/reports/smartzap-user-activities-exports', methods=['POST'])
        @authorize()
        @marshal(SmartzapUserActivitiesExportSchema(), parameter_name='data', locations=['query'])
        def smartzap_user_activities_statistics_export(data):
            type_name = SMARTZAP_USER_ACTIVITIES_EXPORT
            return create_export(self._service, type_name, data.get("filters"), data["report_format"])

        @doc(tags=['report'], description='Myaccount User Permissions Report in XLSX or CSV')
        @self.app.route('/api/v1/reports/myaccount-user-permissions-exports', methods=['POST'])
        @authorize()
        @marshal(ExportSchema(), parameter_name='data', locations=['query'])
        def myaccount_user_permissions_export(data):
            type_name = MYACCOUNT_USER_PERMISSIONS_EXPORT
            return create_export(self._service, type_name, data.get("filters"), data["report_format"])

        @doc(tags=['internal', 'report'], description='Konquest Companies Health Check')
        @self.app.route('/api/v1/internal/reports/konquest-companies-health-check-export', methods=['POST'])
        @authorize("keeps_billing")
        @marshal(KonquestCompaniesHealthCheckExportSchema(), parameter_name='data', locations=['query'])
        def konquest_companies_health_check_export(data):
            type_name = KONQUEST_COMPANIES_HEALTH_CHECK
            return create_export(self._service, type_name, data.get("filters"), data["report_format"])

        @self.app.route('/api/v1/get-report', methods=['POST'])
        @authorize()
        def get_report():
            params = request.get_json()["report"]
            x_client = request.x_client
            return test_report(params, x_client)


def create_presentation(service: ReportService, type_name: str, object_id: str, filters: dict = None):
    if filters is None:
        filters = {}
    x_client = request.x_client

    report = ReportDto(
        type_name=type_name,
        request_user=request.user,
        language=request.user.locale,
        workspace_id=x_client,
        object_id=object_id,
        filters=filters,
        time_zone=request.user.time_zone
    )
    try:
        response = service.add_report(report)
    except ReportServiceError as e:
        return jsonify(str(e)), e.status_code
    response["report"] = ReportSchema(many=False).dump(response['report'])
    return jsonify(response), HTTPStatus.ACCEPTED


def create_export(service: ReportService, type_name: str, filters: dict, file_format: str):
    x_client = request.x_client
    report = ReportDto(
        type_name=type_name,
        request_user=request.user,
        language=request.user.locale,
        workspace_id=x_client,
        object_id=x_client,
        filters=filters,
        file_format=file_format,
        time_zone=request.user.time_zone
    )
    try:
        response = service.add_report(report)
    except ReportServiceError as e:
        return jsonify(str(e)), e.status_code
    response["report"] = ReportSchema(many=False).dump(response['report'])
    return jsonify(response), HTTPStatus.ACCEPTED
