import traceback
from http import HTTPStatus

from controller.schema.user_v2 import (
    UserDataFilterSchema,
    UserEnrollmentsFilterSchema,
    UsersListingFilterSchema,
    UsersStatsFilterSchema,
)
from custom.discord_webhook_logger import DiscordWebhookLogger
from domain.common.authorize import authorize
from domain.user.service_v2 import UserServiceV2
from elasticsearch import NotFoundError
from flask import jsonify, request
from flask_apispec import doc
from injector import inject
from keeps_flask.app import Application
from keeps_flask.marshmallow import marshal


class UserEndpointsV2:
    @inject
    def __init__(
        self,
        app: Application,
        user_service: UserServiceV2,
    ):
        self.app = app
        self.user_service = user_service
    def register_endpoints(self):
        """
        Contact endpoints factory functions.
        """

        @doc(tags=['user'], description='Returns general data for the specified user')
        @self.app.route('/api/v2/user/<user_id>', methods=['GET'])
        @authorize()
        @marshal(UserDataFilterSchema(), parameter_name='filters', locations=['query'])
        def get_user_data_v2(user_id, filters):
            print('[API] get_user_data_v2', user_id, filters)
            try:
                self.extractWorkspace(filters)
                user = self.user_service.get_user_data(user_id, filters)
                if not user['data']:
                    return '', HTTPStatus.NOT_FOUND
                return jsonify(user)
            except Exception as e:
                return self.handleException(e, 'Analytics API > User v2 > ID: ' + user_id)

        @doc(tags=['user'], description='Returns courses which the the specified user is enrolled')
        @self.app.route('/api/v2/user/<user_id>/enrollments', methods=['GET'])
        @authorize()
        @marshal(UserEnrollmentsFilterSchema(), parameter_name='filters', locations=['query'])
        def get_user_enrollments_v2(user_id, filters):
            print('[API] get_user_enrollments_v2', user_id, filters)
            try:
                self.extractWorkspace(filters)
                user = self.user_service.get_user_enrollments(user_id, filters)
                return jsonify(user)
            except Exception as e:
                return self.handleException(e, 'Analytics API > User v2 > Enrollments > ID: ' + user_id)

        @doc(tags=['user'], description='Returns the total users registered in the period')
        @self.app.route('/api/v2/users', methods=['GET'])
        @authorize()
        @marshal(UsersStatsFilterSchema(), parameter_name='filters', locations=['query'])
        def get_total_users_v2(filters):
            print('[API] get_total_users_v2', filters)
            try:
                self.extractWorkspace(filters)
                user_totals = self.user_service.get_total_users(filters)
                return jsonify(user_totals)
            except Exception as e:
                return self.handleException(e, 'Analytics API > User v2 > Total')

        @doc(tags=['user'], description='Returns the total active users with activities in the period')
        @self.app.route('/api/v2/users/active', methods=['GET'])
        @authorize()
        @marshal(UsersStatsFilterSchema(), parameter_name='filters', locations=['query'])
        def get_total_active_users_v2(filters):
            print('[API] get_total_active_users_v2', filters)
            try:
                self.extractWorkspace(filters)
                user_totals = self.user_service.get_active_users(filters)
                return jsonify(user_totals)
            except Exception as e:
                return self.handleException(e, 'Analytics API > User v2 > Active')

        @doc(tags=['user'], description='Returns the distribution of course enrollments per users in the period')
        @self.app.route('/api/v2/users/enrollment/distribution', methods=['GET'])
        @authorize()
        @marshal(UsersStatsFilterSchema(exclude=['interval']), parameter_name='filters', locations=['query'])
        def get_distribution_users_enrollments_v2(filters):
            print('[API] get_distribution_users_enrollments_v2', filters)
            try:
                self.extractWorkspace(filters)
                response = self.user_service.get_distribution_users_enrollments(filters)
                return jsonify(response)
            except Exception as e:
                return self.handleException(e, 'Analytics API > User v2 > Enrollments > Distribution')

        @doc(tags=['user'], description='Returns the average ratio of engagement factor for users, in the period')
        @self.app.route('/api/v2/users/engagement/rate', methods=['GET'])
        @authorize()
        @marshal(UsersStatsFilterSchema(exclude=['interval']), parameter_name='filters', locations=['query'])
        def get_engagement_rate_v2(filters):
            print('[API] get_engagement_rate_v2', filters)
            try:
                self.extractWorkspace(filters)
                response = self.user_service.get_engagement_rate(filters)
                return jsonify(response)
            except Exception as e:
                return self.handleException(e, 'Analytics API > User v2 > Engagement > Rate')

        @doc(tags=['user'], description='Returns the average hours of content consumed per user, in the period')
        @self.app.route('/api/v2/users/content/consumed', methods=['GET'])
        @authorize()
        @marshal(UsersStatsFilterSchema(exclude=['interval']), parameter_name='filters', locations=['query'])
        def get_content_consumed_per_user_v2(filters):
            print('[API] get_content_consumed_per_user_v2', filters)
            try:
                self.extractWorkspace(filters)
                response = self.user_service.get_content_consumed_per_user(filters)
                return jsonify(response)
            except Exception as e:
                return self.handleException(e, 'Analytics API > User v2 > Content Consumed')

        @doc(tags=['user'], description='Returns total users that created a course or channel in the period')
        @self.app.route('/api/v2/users/creators', methods=['GET'])
        @authorize()
        @marshal(UsersStatsFilterSchema(exclude=['interval']), parameter_name='filters', locations=['query'])
        def get_users_creators_v2(filters):
            print('[API] get_users_creators_v2', filters)
            try:
                self.extractWorkspace(filters)
                response = self.user_service.get_users_creators(filters)
                return jsonify(response)
            except Exception as e:
                return self.handleException(e, 'Analytics API > User v2 > Creators')

        @doc(tags=['user'], description='Returns the ranking of course categories with enrollment in the period')
        @self.app.route('/api/v2/users/enrollment/categories', methods=['GET'])
        @authorize()
        @marshal(UsersStatsFilterSchema(exclude=['interval']), parameter_name='filters', locations=['query'])
        def get_users_enrollment_categories_v2(filters):
            print('[API] get_users_enrollment_categories_v2', filters)
            try:
                self.extractWorkspace(filters)
                response = self.user_service.get_users_enrollment_categories(filters)
                return jsonify(response)
            except Exception as e:
                return self.handleException(e, 'Analytics API > User v2 > Enrollments > Categories')

        @doc(tags=['user'], description='Returns the ranking of most consumed content types')
        @self.app.route('/api/v2/users/activity/content-types', methods=['GET'])
        @authorize()
        @marshal(UsersStatsFilterSchema(exclude=['interval']), parameter_name='filters', locations=['query'])
        def get_users_activity_content_types_v2(filters):
            print('[API] get_users_activity_content_types_v2', filters)
            try:
                self.extractWorkspace(filters)
                response = self.user_service.get_users_activity_content_types(filters)
                return jsonify(response)
            except Exception as e:
                return self.handleException(e, 'Analytics API > User v2 > Activity > Content Types')

        @doc(tags=['user'], description='Users listing')
        @self.app.route('/api/v2/users/list', methods=['GET'])
        @authorize()
        @marshal(UsersListingFilterSchema(), parameter_name='filters', locations=['query'])
        def get_users_list_v2(filters):
            print('[API] get_users_list_v2', filters)
            try:
                self.extractWorkspace(filters)
                response = self.user_service.get_users_list(filters)
                return jsonify(response)
            except Exception as e:
                return self.handleException(e, 'Analytics API > User v2 > List')

    @staticmethod
    def handleException(e: Exception, message: str):
        if isinstance(e, NotFoundError):
            return jsonify(error=str(e)), HTTPStatus.NOT_FOUND
        else:
            traceback.print_exc()
            DiscordWebhookLogger().emit_short_message(message, e)
            # raise e  # re-raise # TODO fix keeps-flask error handling
            return jsonify(error=str(e)), HTTPStatus.BAD_REQUEST

    @staticmethod
    def extractWorkspace(filter):
        workspace_id = request.headers.get('x-client')
        filter.workspace_id = workspace_id
        return filter
