from domain.common.dependencies import DatabaseSession
from domain.elasticsearch.services import ElasticSearchService
from injector import inject
from keeps_flask.app import Application
from keeps_flask.healthcheck import register_healthchecks


class HealthCheckEndpoints:
    @inject
    def __init__(
        self,
        app: Application,
        es_service: ElasticSearchService,
        db_session: DatabaseSession
    ):
        self.app = app
        self.es_service = es_service
        self.db_session = db_session

    def elasticsearch_available(self):
        self.es_service.info()
        return True, 'ElastichSearch is ok'

    def database_available(self):
        is_database_working = True
        output = 'Database is ok'

        try:
            # to check database we will execute raw query
            self.db_session.execute('SELECT 1')
        except Exception as e:
            output = str(e)
            is_database_working = False

        return is_database_working, output

    def register_endpoints(self):
        register_healthchecks(self.app, checkers=[self.elasticsearch_available, self.database_available])
