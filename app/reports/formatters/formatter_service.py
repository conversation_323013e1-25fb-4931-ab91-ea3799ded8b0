from datetime import datetime
from typing import Any, Dict, Optional, Type

import pytz
from pandas import DataFrame
from pandas.core.dtypes.common import is_datetime64_any_dtype
from reports.formatters.formatter import Formatter


class FormatterService:
    def __init__(self):
        self.formatters: Dict[str, Type[Formatter]] = {}
        self.time_zone = None

    def set_time_zone(self, zone):
        self.time_zone = pytz.timezone(zone)

    def format_dataframe(
        self,
        data_frame: DataFrame,
        custom_formatters: Optional[Dict[str, Type[Formatter]]] = None,
    ) -> DataFrame:
        columns_types = data_frame.dtypes
        columns = list(columns_types.index.values)
        formatters = self.formatters
        if custom_formatters:
            formatters = custom_formatters
            columns = set(filter(lambda _column: _column in custom_formatters.keys(), columns))

        for column in columns:
            self._format_column(data_frame, column)

            formatter = formatters.get(column)
            if formatter:
                formatter_instance = formatter()
                data_frame.loc[:, column] = data_frame[column].apply(formatter_instance.execute)

        return data_frame

    def _format_column(self, data_frame: DataFrame, column: str) -> DataFrame:
        if is_datetime64_any_dtype(data_frame[column]):
            data_frame.loc[:, column] = data_frame[column].apply(lambda date: self._format_date(date))
        return data_frame

    def _format_date(self, value: Any):
        try:
            if hasattr(value, 'tz_convert') and hasattr(value, 'tz_localize'):
                return self._change_timezone_index(value)
            else:
                return self._change_timezone(value)
        except (ValueError, AttributeError, TypeError):
            return value

    def _change_timezone_index(self, value):
        if value.tz is None:
            value = value.tz_localize('UTC')
        value = value.tz_convert(self.time_zone.zone)
        value = value.tz_localize(None)
        return value

    def _change_timezone(self, value: datetime) -> datetime:
        if value.tzinfo is None:
            value = pytz.utc.localize(value)
        value = value.astimezone(self.time_zone)
        value = value.replace(tzinfo=None)
        return value
