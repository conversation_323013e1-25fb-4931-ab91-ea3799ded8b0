import math
from typing import Any, Optional

from pandas._libs import NaTType
from reports.formatters.formatter import Formatter


class PercentFormatter(Formatter):
    def execute(self, value: Any) -> Optional[str]:
        if isinstance(value, str):
            return value
        if value is None or isinstance(value, NaTType) or math.isnan(value):
            return None
        try:
            return f"{float(value) * 100:.1f}%".replace("0.0", "0")
        except (TypeError, ValueError):
            return None
