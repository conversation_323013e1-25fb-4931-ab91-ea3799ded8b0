from datetime import date, datetime
from typing import Union

from pandas._libs.tslibs.nattype import NaTType
from reports.formatters.formatter import Formatter


class DateFormatter(Formatter):
    date_format = '%Y-%m-%d'
    def execute(self, value: Union[date, datetime, str]):
        if not value:
            return None
        if isinstance(value, str):
            value = (datetime.fromisoformat(value))
        try:
            return value.strftime(self.date_format)
        except ValueError as error:
            if isinstance(value, NaTType):
                return None
            raise error
