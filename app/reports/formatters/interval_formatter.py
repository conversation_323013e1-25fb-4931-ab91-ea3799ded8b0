import math

from pandas import Timedelta
from reports.formatters.formatter import Formatter


class IntervalFormatter(Formatter):
    def execute(self, value: Timedelta):
        if not value:
            return
        total_seconds = value.total_seconds()
        if not total_seconds or math.isnan(total_seconds):
            return
        hours, remainder = divmod(total_seconds, 3600)
        minutes, seconds = divmod(remainder, 60)
        return '{}:{:02d}:{:02d}'.format(int(hours), int(minutes), int(seconds))
