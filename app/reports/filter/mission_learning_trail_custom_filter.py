from typing import List, Union

from reports.constants import LEARNING_TRAIL_STEP
from reports.filter.custom_filter import CustomFilter


class MissionLearningTrailCustomFilter(CustomFilter):
    def __init__(self):
        self.name = 'mission__learning_trail_id'
        self.table_name = 'mission'
        super().__init__(self.name, self.table_name)

    def create_sql_line(self, value: Union[List[str], str]) -> str:
        column = f"{LEARNING_TRAIL_STEP}.learning_trail_id"
        statement = self._create_statement(column, value)
        return f'{self.table_name}.id in (SELECT mission_id FROM {LEARNING_TRAIL_STEP} WHERE {statement})'
