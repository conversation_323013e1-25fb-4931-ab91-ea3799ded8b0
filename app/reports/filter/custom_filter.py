from abc import ABC, abstractmethod


class CustomFilter(ABC):
    def __init__(self, name: str, table_name: str):
        self.name = name
        self.table_name = table_name

    @abstractmethod
    def create_sql_line(self, value) -> str:
        pass

    @staticmethod
    def _create_in_statement(column, value):
        if not value:
            return ""
        string_values = "','".join(value)
        filter_line = f" {column} in ('{string_values}')"
        filter_line = filter_line.replace('__in', '')
        return filter_line

    def _create_statement(self, column, value):
        if isinstance(value, list):
            return self._create_in_statement(column, value)
        return f"{column} = '{value}'"
