import uuid

import pytest

from reports.filter.activities_learning_object_custom_filter import ActivitiesLearningObjectCustomFilter


@pytest.fixture
def mission_activities_learning_object_custom_filter():
    return ActivitiesLearningObjectCustomFilter()


def test_create_sql_line(mission_activities_learning_object_custom_filter):
    pulse_id = str(uuid.uuid4())
    mission_id = str(uuid.uuid4())
    object_ids = [pulse_id, mission_id]
    sql_statement = mission_activities_learning_object_custom_filter.create_sql_line(object_ids)
    assert sql_statement == (f"mission_enrollment.mission_id in ('{pulse_id}','{mission_id}') "
                             f"OR learn_content_activity.pulse_id in ('{pulse_id}','{mission_id}')")
