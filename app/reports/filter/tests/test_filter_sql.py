import uuid

import pytest

from reports.constants import MISSION_ENROLLMENT, MISSION
from reports.filter.filter_sql import FilterSql
from reports.filter.mission_enrollment_groups_custom_filter import MissionEnrollmentGroupCustomFilter
from reports.filter.mission_learning_trail_custom_filter import Mission<PERSON>earningTrailCustomFilter


@pytest.fixture
def filter_sql():
    return FilterSql([MissionEnrollmentGroupCustomFilter(), MissionLearningTrailCustomFilter()])


def test_filter_mission_enrollments_by_group(filter_sql):
    group_id = str(uuid.uuid4())
    sql_filter = filter_sql.builder_sql_filter(MISSION_ENROLLMENT, {'group_mission_id': group_id})
    assert sql_filter == f" AND (mission_enrollment.mission_id in " \
                         f"(SELECT mission_id FROM group_mission WHERE group_mission.group_id = '{group_id}'))"


def test_filter_missions_by_trail(filter_sql):
    trail_id = str(uuid.uuid4())
    sql_filter = filter_sql.builder_sql_filter(MISSION, {'mission__learning_trail_id': trail_id})
    assert sql_filter == f" AND (mission.id in (SELECT mission_id FROM learning_trail_step " \
                         f"WHERE learning_trail_step.learning_trail_id = '{trail_id}'))"


def test_date_range_format(filter_sql):
    filter_dates = (('start_date', 'end_date'),)
    filters = {'start_date': '2023-02-12', 'end_date': '2023-02-12'}
    default_filter_dates = filter_dates[0]

    sql_filter = filter_sql.builder_sql_filter(
        MISSION,
        filters,
        filter_dates,
        default_filter_dates,
        limit_filter_date_range=10
    )

    assert sql_filter == " AND mission.start_date = '2023-02-12 00:00:00' AND mission.end_date = '2023-02-12 23:59:59'"


def test_filter_with_completed_where_statement(filter_sql):
    mission_id = str(uuid.uuid4())
    sql_filter = filter_sql.builder_sql_filter(
        MISSION, {'id': mission_id}, completed_where_statement=True
    )
    assert sql_filter == f"WHERE mission.id = '{mission_id}'"
