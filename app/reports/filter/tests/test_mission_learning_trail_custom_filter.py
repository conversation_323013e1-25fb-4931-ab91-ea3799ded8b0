import uuid

import pytest

from reports.filter.mission_learning_trail_custom_filter import MissionLearningTrailCustomFilter


@pytest.fixture
def mission_learning_trail_custom_filter():
    return MissionLearningTrailCustomFilter()


def test_create_sql_line(mission_learning_trail_custom_filter):
    trail_id = str(uuid.uuid4())
    sql_statement = mission_learning_trail_custom_filter.create_sql_line(trail_id)
    assert sql_statement == f"mission.id in " \
                            f"(SELECT mission_id FROM learning_trail_step " \
                            f"WHERE learning_trail_step.learning_trail_id = '{trail_id}')"
