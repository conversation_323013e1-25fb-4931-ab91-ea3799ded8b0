import uuid

import pytest

from reports.filter.mission_enrollment_groups_custom_filter import MissionEnrollmentGroupCustomFilter


@pytest.fixture
def mission_enrollment_group_custom_filter():
    return MissionEnrollmentGroupCustomFilter()


def test_create_sql_line(mission_enrollment_group_custom_filter):
    group_id = str(uuid.uuid4())
    sql_statement = mission_enrollment_group_custom_filter.create_sql_line(group_id)
    assert sql_statement == f"mission_enrollment.mission_id in " \
                            f"(SELECT mission_id FROM group_mission WHERE group_mission.group_id = '{group_id}')"
