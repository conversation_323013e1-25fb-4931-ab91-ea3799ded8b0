from typing import List, Union

from reports.constants import GROUP_MISSION
from reports.filter.custom_filter import CustomFilter


class MissionEnrollmentGroupCustomFilter(CustomFilter):
    def __init__(self):
        self.name = 'group_mission_id'
        self.table_name = 'mission_enrollment'
        super().__init__(self.name, self.table_name)

    def create_sql_line(self, value: Union[List[str], str]) -> str:
        column = f"{GROUP_MISSION}.group_id"
        statement = self._create_statement(column, value)
        return f'{self.table_name}.mission_id in (SELECT mission_id FROM {GROUP_MISSION} WHERE {statement})'
