from typing import List, Union

from reports.filter.custom_filter import CustomFilter


class TrailEnrollmentActiveCustomFilter(CustomFilter):
    def __init__(self):
        self.name = 'trail_enrollment_active'
        self.table_name = 'learning_trail_enrollment'
        super().__init__(self.name, self.table_name)

    def create_sql_line(self, value: Union[List[str], str]) -> str:
        return f"{self.table_name}.status = 'STARTED' OR ({self.table_name}.status = 'ENROLLED' and {self.table_name}.progress > 0)"
