from typing import List, Union

from reports.filter.custom_filter import CustomFilter


class UserDeletedFromWorkspaceCustomFilter(CustomFilter):
    def __init__(self):
        self.name = 'user_deleted_from_workspace'
        self.table_name = 'mission_enrollment'
        super().__init__(self.name, self.table_name)

    def create_sql_line(self, value: Union[List[str], str]) -> str:
        if value:
            return "urw.user_id IS NULL"
        return "urw.user_id IS NOT NULL"


class TrailEnrollmentUserDeletedFromWorkspaceCustomFilter(CustomFilter):
    def __init__(self):
        self.name = 'user_deleted_from_workspace'
        self.table_name = 'learning_trail_enrollment'
        super().__init__(self.name, self.table_name)

    def create_sql_line(self, value: Union[List[str], str]) -> str:
        if value:
            return "urw.user_id IS NULL"
        return "urw.user_id IS NOT NULL"
