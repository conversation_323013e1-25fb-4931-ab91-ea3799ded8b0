from datetime import timed<PERSON><PERSON>
from typing import Optional

from controller.error import KeepsException
from dateutil.parser import parse
from dateutil.utils import today
from reports.filter.custom_filter import CustomFilter

RESERVED_POSTGRES_WORDS = ("user", "group")
AND_STATEMENT = " AND"


class FilterSql:
    def __init__(self, custom_filters=None):
        if custom_filters is None:
            custom_filters = []
        self._custom_filters = custom_filters

    def _get_custom_filter(self, key: str, table_name: str) -> Optional[CustomFilter]:
        filtered = next(filter(
            lambda custom: custom.name == key and custom.table_name == table_name, self._custom_filters
        ), None)
        return filtered

    def builder_sql_filter(
        self,
        table_name: str,
        filters: dict,
        keys_filter_dates: tuple = None,
        default_keys_filter_dates: tuple = None,
        limit_filter_date_range: int = None,
        foreign_tables_names: tuple = (),
        completed_where_statement: bool = False,
    ) -> str:
        # TODO: delete this after queries functions normalized
        if not filters:
            filters = {}
        self._table_name = table_name

        if keys_filter_dates:
            filters = self.builder_filter_dates(default_keys_filter_dates=default_keys_filter_dates,
                                                filters=filters,
                                                keys_filter_dates=keys_filter_dates,
                                                limit_filter_date_range=limit_filter_date_range)

        sql_filter = self._create_sql_filters(filters, table_name, foreign_tables_names)
        if completed_where_statement:
            sql_filter = sql_filter.replace(AND_STATEMENT, 'WHERE', 1)

        return sql_filter

    def builder_filter_dates(self, default_keys_filter_dates, filters, keys_filter_dates, limit_filter_date_range):
        empty_filter_dates = self._check_empty_filter_dates(filters=filters, keys_filter_dates=keys_filter_dates)

        if empty_filter_dates and limit_filter_date_range:
            filters = self._generate_default_filter_dates(default_keys_filter_dates=default_keys_filter_dates,
                                                          filters=filters, limit_days=limit_filter_date_range)
        if not empty_filter_dates:
            filters = self._format_filter_dates(filters=filters, keys_filter_dates=keys_filter_dates,
                                                limit_days=limit_filter_date_range)
        return filters

    @staticmethod
    def _check_empty_filter_dates(filters, keys_filter_dates):
        empty_filter_dates = True
        for keys in keys_filter_dates:
            keys_in_common = bool(set(filters.keys()) & set(keys))
            if keys_in_common:
                return False

        return empty_filter_dates

    def _format_filter_dates(self, filters, keys_filter_dates: tuple, limit_days: int = None):
        for date_filter in keys_filter_dates:
            initial_date_key = date_filter[0]
            end_date_key = date_filter[1]

            try:
                initial_date_value = parse(str(filters.get(initial_date_key))) if filters.get(initial_date_key) else None
                end_date_value = parse(str(filters.get(end_date_key))) if filters.get(end_date_key) else None
            except Exception as e:
                raise KeepsException(_msg=str(e))

            if (initial_date_value or end_date_value) and limit_days:
                initial_date_value, end_date_value = self._format_date_range(limit_days=limit_days,
                                                                             initial_date=initial_date_value,
                                                                             end_date=end_date_value)
            if initial_date_value:
                filters.update({initial_date_key: initial_date_value})
            if end_date_value:
                filters.update({end_date_key: end_date_value})

        return filters

    def _generate_default_filter_dates(self, default_keys_filter_dates, filters, limit_days):
        initial_date_key = default_keys_filter_dates[0]
        end_date_key = default_keys_filter_dates[1]
        initial_date, end_date = self._format_date_range(limit_days=limit_days)
        filters.update({initial_date_key: initial_date})
        filters.update({end_date_key: end_date})
        return filters

    @staticmethod
    def _format_date_range(limit_days: int, initial_date=None, end_date=None):
        limit_days = int(limit_days)
        if not end_date:
            end_date = today() + timedelta(days=1)
        if not initial_date:
            initial_date = today() - timedelta(days=limit_days)

        date_range = end_date - initial_date
        if date_range.days > limit_days:
            excess_days = date_range.days - limit_days
            initial_date = initial_date - timedelta(days=excess_days)

        end_date = end_date.replace(hour=23, minute=59, second=59)
        return initial_date, end_date

    def _create_sql_filters(self, filters: dict, table_name: str, foreign_key_names: tuple) -> str:
        sql_filter = ''
        for key in filters:
            custom_filter = self._get_custom_filter(key, table_name)
            if custom_filter:
                sql_line = f"{AND_STATEMENT} ({custom_filter.create_sql_line(filters.get(key))})"
            else:
                sql_line = self._create_filter_line(filters, key, table_name, foreign_key_names)
            sql_filter += sql_line
        return sql_filter

    def _create_filter_line(self, filters: dict, key: str, table_name: str, foreign_keys: tuple) -> str:
        column = self._make_column(foreign_keys, key, table_name)
        if '__in' in key:
            filter_line = self._create_list_filter_line(column, filters, key)
        else:
            filter_line = self._create_value_filter_line(column, filters, key)
        return filter_line

    @staticmethod
    def _create_value_filter_line(column, filters, key):
        value = filters.get(key)
        filter_line = ''
        if len(str(value)) > 0:
            filter_line = f"{AND_STATEMENT} {column} = '{value}'"
            filter_line = filter_line.replace('__lte =', ' <= ')
            filter_line = filter_line.replace('__gte =', ' >= ')
        return filter_line

    @staticmethod
    def _create_list_filter_line(column, filters, key):
        string_values = "','".join(str(value) for value in filters.get(key))
        filter_line = f"{AND_STATEMENT} {column} in ('{string_values}')"
        filter_line = filter_line.replace('__in', '')
        return filter_line if "in ('')" not in filter_line else ""

    @staticmethod
    def _make_column(foreign_keys, key, table_name):
        first_expression = key.split("__")[0]
        table_name = f'"{table_name}"' if table_name in RESERVED_POSTGRES_WORDS else table_name
        if first_expression in foreign_keys:
            new_key = key.replace(f"{first_expression}__", "")
            if first_expression in RESERVED_POSTGRES_WORDS:
                column = f'"{first_expression}".{new_key}'
            else:
                column = f"{first_expression}.{new_key}"
        else:
            column = f'{table_name}.{key}'
        return column
