from typing import List, Union

from reports.filter.custom_filter import CustomFilter
from reports.utils import list_to_sql_repr


class ActivitiesLearningObjectCustomFilter(CustomFilter):
    def __init__(self):
        self.name = 'learning_object_id__in'
        self.table_name = 'learn_content_activity'
        super().__init__(self.name, self.table_name)

    def create_sql_line(self, value: Union[List[str]]) -> str:
        value = list_to_sql_repr(value)
        return f"mission_enrollment.mission_id in ('{value}') OR {self.table_name}.pulse_id in ('{value}')"
