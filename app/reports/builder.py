import os
import uuid
import zipfile

import numpy as np
import openpyxl
from openpyxl import load_workbook
from pandas import <PERSON>Frame
from pandas import pandas as pd
from pandas.errors import DataError
from pypdf import PdfWriter
from pypdf import PdfReader
from reports.utils import ReportExport

COLOR_CHART_PRIMARY = "#3762E3"
COLOR_CHART_SECONDARY = "#376FFF"
PLT_STYLE = "fivethirtyeight"


class PDFBuilder:
    """
    Create, merge and upload PDF reports to S3

    pdf_creator: Instance of ReportLambdaClient that is a service (lambda) to compile PDF using Jasper file and dataset (json).
    uploader: Instance of ReportUploaderClient configured to upload PDF report to S3 into report bucket.

    """

    def __init__(self, pdf_creator, uploader, temp_dir):
        self.pdf_creator = pdf_creator
        self.uploader = uploader
        self.temp_dir = temp_dir

    def execute(self, pages, report_name=None, remove_temp_file=False):
        """
        Create, merge and upload PDF reports to S3

        :param pages: is a dict that contains keys:

            template -> Name of report template (see available in ReportLambdaClient doc)
            dataset -> Dict with report data (ex.: {"result": {"id": "xxx", "name": "keeps", ...}}).
                       Each report have your own dataset, check required key into .jasper file.
            params -> Dict with keys and data (ex.: {"id": 123, "name": "Keeps}
                       Each report have your own params, check required key into .jasper file.

        :param report_name: specific the report name. If None, will generate a random name if uuid4.
        :param remove_temp_file: if true remove page pdf after be merged in the principal pdf

        :return: Link to download/view report hosted in S3.
        """
        pdf_writer = PdfWriter()

        for page in pages:
            if page['dataset']:
                jasper_template = page['template']

                try:
                    jasper_sub_report_template = page['sub_template']
                except KeyError:
                    jasper_sub_report_template = None

                dataset = page['dataset']
                params = page['params']
                page_result = self.pdf_creator.json_to_pdf(jasper_template=jasper_template,
                                                           jasper_sub_report_template=jasper_sub_report_template,
                                                           dataset=dataset, params=params)

                try:
                    with open(page_result, 'rb') as pdf_file:
                        reader = PdfReader(pdf_file)
                        for page_num in range(len(reader.pages)):
                            pdf_writer.add_page(reader.pages[page_num])
                    
                    os.remove(page_result) if remove_temp_file else None

                except Exception as err:
                    page_result = self.error_page(err, page)
                    with open(page_result, 'rb') as pdf_file:
                        
                        reader = PdfReader(pdf_file)
                        for page_num in range(len(reader.pages)):
                            pdf_writer.add_page(reader.pages[page_num])
                    os.remove(page_result)

        if not report_name:
            report_name = f'{self.temp_dir}/{str(uuid.uuid4())}.pdf'
        else:
            report_name = f'{self.temp_dir}/{report_name}.pdf'

        with open(report_name, 'wb') as output_file:
            pdf_writer.write(output_file)
            
        pdf_url = self.uploader.upload_report(report_name, "pdf")
        return pdf_url

    def error_page(self, err, page):
        page_error = {"template": "error_report",
                      "dataset": {'report_name': page['template'],
                                  'report_error': "Error: {0}".format(err)},
                      "params": {}}
        page_result = self.pdf_creator.json_to_pdf(jasper_template=page_error['template'],
                                                   dataset=page_error['dataset'], params=page_error['params'])

        return page_result


class ExportBuilder:
    """
    Create, merge and upload XLSX reports to S3

    uploader: Instance of ReportUploaderClient configured to upload file report to S3 into report bucket.

    """

    def __init__(self, xlsx_creator: ReportExport, uploader, temp_dir):
        self.xlsx_creator = xlsx_creator
        self.uploader = uploader
        self.temp_dir = temp_dir

    def execute_csv(self, reports: list = None, report_name: str = None):
        """
        :param reports: a list of the sheets data -> {"name": str, "data_frame": DataFrame}
        :param report_name: report name
        """
        report_id = str(uuid.uuid4())
        report_name = report_name if report_name else str(report_id)
        file_zip = f'{self.temp_dir}/{report_name}.zip'

        report_files = []
        for report in reports:
            report_name = f'{self.temp_dir}/{report.get("name")}_id_{report_id}.csv'
            report_data = report.get('data_frame')
            report_index = report.get('index')

            report_data.to_csv(report_name, index=report_index)
            report_files.append(report_name)

        file_zip_path = self.compress(files_path=report_files, report_name=file_zip)

        file_zip_url = self.uploader.upload_report(file_zip_path, "zip")
        return file_zip_url

    def execute_xlsx(self, sheets: list = None, report_name: str = None):
        """
        :param sheets: a list of the sheets data -> {"name": str, "data_frame": DataFrame, "index": bool, "format": bool}
        :param report_name: report name
        """
        report_name = report_name if report_name else str(uuid.uuid4())

        file = f'{self.temp_dir}/{report_name}.xlsx'

        writer = pd.ExcelWriter(file, engine='xlsxwriter')
        for sheet in sheets:
            sheet_name = sheet.get('name')
            sheet_data = sheet.get('data_frame')
            sheet_index = sheet.get('index')
            sheet_format = sheet.get('format', True)

            if sheet_format:
                try:
                    sheet_data.to_excel(writer, sheet_name=sheet_name, startrow=1, index=sheet_index, header=False)
                    worksheet = writer.sheets[sheet_name]
                    self.worksheet_style_format(sheet_data, worksheet, writer)
                except Exception:
                    sheet_data.to_excel(writer, sheet_name=sheet_name, index=sheet_index)
            else:
                sheet_data.to_excel(writer, sheet_name=sheet_name, index=sheet_index)

        writer.close()

        xlsx_url = self.uploader.upload_report(file, "xlsx")
        return xlsx_url

    def worksheet_style_format(self, sheet_data, worksheet, writer):
        cell_format = writer.book.add_format({"font_name": "Arial", "font_size": 12, "align": 'left'})
        header_format = writer.book.add_format({
            'bold': True,
            'text_wrap': True,
            'valign': 'top',
            'align': 'center',
            'fg_color': '#808080',
            'font_color': '#FFFFFF',
            'border': 1})
        worksheet.set_column('A:AZ', None, cell_format)
        if sheet_data.index.names[0] is not None:
            for col_num, value in enumerate(sheet_data.index.names):
                worksheet.write(0, col_num, value, header_format)
                worksheet.set_column(col_num,
                                     col_num,
                                     self.get_index_column_width(data_frame=sheet_data, index_position=col_num))

        index_columns_count = len(sheet_data.index.names) - 1 if sheet_data.index.names[0] is None else len(sheet_data.index.names)
        for col_num, value in enumerate(sheet_data.columns.values):
            worksheet.write(0, col_num + index_columns_count, value, header_format)
            worksheet.set_column(col_num + index_columns_count,
                                 col_num + index_columns_count,
                                 self.get_column_width(data_frame=sheet_data, column_name=value),
                                 cell_format)

    @staticmethod
    def get_column_width(data_frame: DataFrame, column_name: str) -> int:
        column_values_max_width = len(max([str(value) for value in data_frame[column_name]], key=len))
        width_max = max([column_values_max_width, len(column_name)])

        return width_max + 2

    @staticmethod
    def get_index_column_width(data_frame: DataFrame, index_position: int) -> int:
        try:
            column_values_max_width = len(max([str(value[index_position]) for value in data_frame.index.values], key=len))
            width_max = max([column_values_max_width, len(data_frame.index.names[index_position])])
        except ValueError:
            width_max = 20

        return width_max + 2

    @staticmethod
    def pivot_sheet(data_frame, index=None, values=None, columns=None, aggfunc="mean",
                    margins=False, fill_values=None, margins_name="Avarage", observed=False):
        try:
            return pd.pivot_table(
                data_frame, values=values, index=index, columns=columns, margins=margins,
                aggfunc=aggfunc, margins_name=margins_name, fill_value=fill_values, observed=observed
            )
        except DataError as error:
            if str(error) != 'No numeric types to aggregate':
                raise error
            return DataFrame([])

    def pivot_data_with_subtotal(self, data_frame, index, values, prefix_margin, fill_values=0, aggfunc=np.sum):
        frequency = data_frame[index[0]].value_counts().to_dict()

        df_pivot = DataFrame()

        for principal in frequency:
            same_principal = data_frame[index[0]] == principal
            filtered_df = data_frame[same_principal]
            data_principal = self.pivot_sheet(data_frame=filtered_df,
                                              index=index,
                                              values=values,
                                              margins=True,
                                              margins_name=f"{prefix_margin} - {principal}",
                                              aggfunc=aggfunc,
                                              fill_values=fill_values)
            df_pivot = pd.concat([df_pivot, data_principal])

        return df_pivot

    @staticmethod
    def insert_image_in_dashboard(file_merged, file_image, sheet_name, cell, delete_image_after=True):
        workbook = load_workbook(file_merged)
        worksheet = workbook[sheet_name]
        image = openpyxl.drawing.image.Image(file_image)
        worksheet.add_image(image, cell)
        workbook.save(file_merged)

        if delete_image_after:
            os.remove(file_image)

        workbook.close()

    @staticmethod
    def compress(files_path, report_name):
        compression = zipfile.ZIP_DEFLATED

        zf = zipfile.ZipFile(report_name, mode="w")
        try:
            for file_path in files_path:
                partitioned_file_path = file_path.rpartition('/')
                file_name = file_path.replace(partitioned_file_path[0], '').replace('/', '')

                zf.write(file_path, file_name, compress_type=compression)
                os.remove(file_path)

        except FileNotFoundError:
            pass
        finally:
            zf.close()

        return report_name