import base64
import binascii
import json
import uuid

import requests


class JasperReportService:
    def __init__(self, jasper_report_server_url: str, temp_dir: str):
        self.base_url = jasper_report_server_url
        self.temp_dir = temp_dir
        self.report_jasper_map = {
            "smartzap_cover": "smartzap/cover_course.jasper",
            "smartzap_enrollment": "smartzap/enrollments_status.jasper",
            "smartzap_performance": "smartzap/lateral_performance_table.jasper",
            "smartzap_consume_content": "smartzap/lateral_consume_users_table.jasper",
            "smartzap_consume_quiz": "smartzap/lateral_consume_quiz_users_table.jasper",
            "smartzap_ranking_users_performance": "smartzap/lateral_ranking_users_performance.jasper",
            "smartzap_ranking_users_quiz_performance": "smartzap/lateral_ranking_users_quiz_performance.jasper",
            "smartzap_thanks": "smartzap/thank_report.jasper",
            "konquest_overview_cover": "konquest/overview/cover_company.jasper",
            "konquest_overview_general_information": "konquest/overview/general_information.jasper",
            "konquest_overview_users_records": "konquest/overview/users_records.jasper",
            "konquest_overview_enrollments_and_users": "konquest/overview/enrollments_and_users.jasper",
            "konquest_overview_enrollments_performance_analysis": "konquest/overview/enrollments_performance_analysis.jasper",
            "konquest_overview_contents_type_in_missions": "konquest/overview/contents_type_in_missions.jasper",
            "konquest_overview_pulses_analysis": "konquest/overview/pulses_analysis.jasper",
            "konquest_overview_consume_pulses_analysis": "konquest/overview/consume_pulses_analysis.jasper",
            "konquest_overview_contents_type_in_pulses": "konquest/overview/contents_type_in_pulses.jasper",
            "konquest_overview_ranking_user_performance": "konquest/overview/ranking_user_performance.jasper",
            "konquest_overview_groups_by_mission_general": "konquest/overview/groups_by_mission_general.jasper",
            "konquest_overview_groups_by_mission_detailed": "konquest/overview/groups_by_mission_detailed.jasper",
            "konquest_overview_thank_report": "konquest/overview/thank_report.jasper",
            "konquest_mission_cover": "konquest/mission_resume/cover_mission.jasper",
            "konquest_mission_general_information": "konquest/mission_resume/general_information.jasper",
            "konquest_mission_enrollments_analysis": "konquest/mission_resume/enrollments_analysis.jasper",
            "konquest_mission_enrollments_performance_analysis": "konquest/mission_resume/enrollments_performance_analysis.jasper",
            "konquest_mission_quizzes_performance_analysis": "konquest/mission_resume/enrollments_quizzes_performance_analysis.jasper",
            "konquest_mission_quizzes_analysis": "konquest/mission_resume/quizzes_analysis.jasper",
            "konquest_mission_ranking_user_performance": "konquest/mission_resume/ranking_user_performance.jasper",
            "konquest_mission_ranking_user_quizzes_performance": "konquest/mission_resume/ranking_user_quizzes_performance.jasper",
            "konquest_mission_groups_by_mission": "konquest/mission_resume/groups_by_mission.jasper",
            "konquest_user_mission": "konquest/user_overview/main_mission.jasper",
            "subreport_konquest_user_mission": "konquest/user_overview/subreport_mission_v2.jasper",
            "konquest_user_pulse": "konquest/user_overview/main_pulse.jasper",
            "subreport_konquest_user_pulse": "konquest/user_overview/subreport_pulse.jasper",
            "konquest_user_group": "konquest/user_overview/main_groups.jasper",
            "subreport_konquest_user_group": "konquest/user_overview/subreport_group.jasper",
            "thank_report": "generic/thank_report.jasper",
            "error_report": "generic/error_report.jasper",
            "user_overview_first_page": "konquest/user_overview_v2/firstPage.jasper",
            "user_overview_second_page": "konquest/user_overview_v2/secondPage.jasper",
            "user_overview_third_page": "konquest/user_overview_v2/thirdPage.jasper",
            "konquest_mission_enrollment_quiz_first_page": "konquest/quiz_by_mission/firstPage.jasper",
            "konquest_mission_enrollment_quiz_subreport_quiz_question": "konquest/quiz_by_mission/subreportQuizQuestion.jasper",
            "konquest_mission_enrollment_quiz_subreport_quiz_question_option": "konquest/quiz_by_mission/subreportQuizQuestionOption.jasper",
            "test_jasper_template_not_found": "this page does not exist"
        }

    def generate_error_page(self, jasper_template: str, error: str) -> str:
        dataset = {'report_name': jasper_template, 'report_error': "Error: {0}".format(error)}
        return self.json_to_pdf("error_report", dataset=dataset)

    @staticmethod
    def clean_data_set(data_set: dict) -> dict:
        return json.loads(json.dumps(data_set, default=str))

    def json_to_pdf(
        self,
        jasper_template: str,
        jasper_sub_report_template: str = None,
        dataset=None,
        params: dict = None,
        report_name: str = None
    ) -> str:
        report_name = report_name if report_name else str(uuid.uuid4())
        file_path = f'{self.temp_dir}/{report_name}.pdf'
        jasper_file_url = self.report_jasper_map[jasper_template]
        jasper_sub_file_url = self.report_jasper_map[jasper_sub_report_template] if jasper_sub_report_template else None

        response = requests.post(
            f"{self.base_url}/",
            json={
                "jasper_file_url": f"/{jasper_file_url}",
                "jasper_sub_file_url": f"/{jasper_sub_file_url}" if jasper_sub_file_url else None,
                "dataset": self.clean_data_set(dataset),
                "params": params if params else {}
            }
        )

        data = response.text
        if response.status_code != 200 or "error" in data:
            raise binascii.Error
        with open(file_path, "wb") as file:
            file.write(base64.b64decode(data))

        return file_path
