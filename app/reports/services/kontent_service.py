from typing import Sequence

from config.default import Config
from elasticsearch import Elasticsearch
from elasticsearch_dsl import connections
from injector import inject


class KontentService:
    @inject
    def __init__(self, url, auth):
        self.conn = Elasticsearch(url, http_auth=auth)
        connections.create_connection(
            hosts=[url],
            timeout=20,
            http_auth=auth
        )

    def get_contents(self, content_uuids: Sequence[str]) -> Sequence[dict]:
        contents = []
        query = {
            "bool": {
                "filter": {
                  "ids": {
                    "values": content_uuids
                  }
                }
            }
        }
        main_hits = self.conn.search(
            index=Config.ELASTICSEARCH_INDEX_KONTENT,
            body={"query": query, "size": len(content_uuids)}
        )["hits"]
        hits = main_hits["hits"] if main_hits else []
        for hit in hits:
            content = hit["_source"]
            content_minimized = {
                "id": hit.get("_id"),
                "name": content.get("name"),
                "content_type": content.get("content_type").get("name"),
                "duration": content.get("duration")
            }

            contents.append(content_minimized)

        return contents
