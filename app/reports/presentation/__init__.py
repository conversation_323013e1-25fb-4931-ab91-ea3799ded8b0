import binascii
import time
import typing
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Optional, List
import os
import uuid

import math
from Locales import Locales
from pypdf import PdfMerger
from dataclasses import dataclass

from config.default import Config
from custom.discord_webhook_logger import DiscordWebhookLogger
from domain.report.models import Report
from reports.query import QueryExecutor
from reports.utils import ReportLambdaClient, ReportUploaderClient


@dataclass
class PresentationPage:
    name: str
    template_key: str
    dataset_lambda: callable
    params_lambda: Optional[callable] = None
    _params: Optional[dict] = None
    sub_template_key: Optional[str] = None
    sub_template_second_key: Optional[str] = None
    _dataset: Optional[dict] = None
    is_active: bool = True

    @property
    def dataset(self) -> dict:
        if not self._dataset:
            self.load_data()
        return self._dataset

    def clean_dataset(self):
        self._dataset =  None

    @property
    def params(self) -> dict:
        if not self._params and self.params_lambda:
            self.load_params()
        return self._params

    def load_data(self) -> None:
        try:
            self._dataset = self.dataset_lambda()
        except Exception as error:
            if Config.DEBUG:
                raise error
            DiscordWebhookLogger().emit_short_message(f'Presentation PDF Error. Template {self.template_key}', error)
            self._dataset = None
        return None

    def load_params(self) -> None:
        self._params = self.params_lambda() if self.params_lambda else None
        return None


class PresentationCreator:
    def __init__(
            self,
            pdf_creator: ReportLambdaClient,
            report_uploader: ReportUploaderClient,
            locale: Locales,
            webhook_logger: DiscordWebhookLogger,
            temp_dir: str = f'{Config.BASE_DIR}/reports/temp',
    ) -> None:
        self._file_type = "pdf"
        self._report_client = pdf_creator
        self._uploader = report_uploader
        self._pages: List[PresentationPage] = []
        self._report_name = None
        self._temp_dir = temp_dir
        self._locale = Locales(Config.LOCALE_JSON)
        self._webhook_logger = webhook_logger
        self._locale = locale

    def create(self, pages: List[PresentationPage], report_name: str, language: str, number_of_reports: int) -> Optional[str]:
        if language in self._locale.languages:
            self._locale.set_default_lang(language)
        self._pages = list(filter(lambda _page: _page.dataset, pages))
        self._report_name = report_name if report_name else uuid.uuid4()
        main_report_path = None

        if self._pages:
            pdf_pages = self._generate_pdf_file_pages()
            main_report_path = self._merge_pdfs(pdf_pages)
        for _ in range(number_of_reports - 1):
            [page.clean_dataset() for page in self._pages]
            self._pages = list(filter(lambda _page: _page.dataset, pages))
            pdf_pages = self._generate_pdf_file_pages()
            pdf_pages.insert(0, main_report_path) if main_report_path else None
            main_report_path = self._merge_pdfs(pdf_pages)

        if not main_report_path:
            return None
        return self._uploader.upload_report(main_report_path, self._file_type).get('url')

    # todo: refactor this class to remove duplicated code (export.ExportCreator._generate_file_path)
    def _generate_file_path(self, extension: str):
        translated_report_name = self._translate_text(self._report_name)
        file_path = f'{self._temp_dir}/{translated_report_name}-{datetime.today()}-{uuid.uuid4()}.{extension}'
        return file_path

    def _translate_text(self, text: str) -> str:
        if text in self._locale.messages:
            try:
                return self._locale.get(text)
            except KeyError:
                return text
        return text

    def _clean_dataset(self, dataset: typing.Union[dict, List]) -> typing.Union[dict, List]:
        if type(dataset) == list:
            return self._clean_list(dataset)
        for key, value in dataset.items():
            if type(value) is float:
                if math.isnan(value):
                    dataset[key] = 0
            elif isinstance(value, dict):
                self._clean_dataset(value)
        return dataset

    def _clean_list(self, values: list) -> list:
        clean_list = []
        for value in values:
            clean_list.append(self._clean_dataset(value))
        return clean_list

    def _generate_pdf_file_pages(self) -> list:
        """Convert PresentationPage to PDF file"""
        pdf_pages = []
        for page in self._pages:
            try:
                dataset = self._clean_dataset(page.dataset)
                page_path = self._report_client.json_to_pdf(page.template_key, page.sub_template_key,
                                                            dataset=dataset, params=page.params)
            except binascii.Error as error:
                page_path = self._report_client.generate_error_page(page.template_key, 'Jasper ERROR')
                self.log_page_error(page, error)

            pdf_pages.append(page_path)

        return pdf_pages

    def log_page_error(self, page: PresentationPage, error: BaseException) -> None:
        title = f'Presentation PDF Error. TK:{page.template_key}\nDATA:{page.dataset}\n{page.params}'
        self._webhook_logger.emit_short_message(title, error)

    def _merge_pdfs(self, pdfs: [str]) -> str:
        pdf_merger = PdfMerger()
        pdf_path = self._generate_file_path("pdf")

        for pdf in pdfs:
            pdf_merger = self._extend_pdf_merger(pdf_merger, pdf)

        pdf_merger.write(pdf_path)
        pdf_merger.close()

        return pdf_path

    @staticmethod
    def _extend_pdf_merger(pdf_merger: PdfMerger, pdf_to_append: str) -> PdfMerger:
        pdf_merger.append(pdf_to_append)
        os.remove(pdf_to_append)
        return pdf_merger


class PresentationService(ABC):
    def __init__(
        self,
        report_creator: PresentationCreator,
        query_selector: QueryExecutor,
        report_name: str = None,
        locales: Locales = None,
    ) -> None:
        self._report_creator = report_creator
        self._main_query = query_selector
        self._report_name = report_name if report_name else str(uuid.uuid4())
        self._locale = locales
        self._pages = []

    @abstractmethod
    def generate(self, report: Report, number_of_reports: int = 1) -> Report:
        if self._locale:
            self._locale.set_default_lang(report.language)
        self._load_pages()
        if not self._pages:
            raise AttributeError("presentation pages cannot be empty, load the self._pages variable"
                                 "in the abstractmethod _load_pages")
        pages = list(filter(lambda page: page.is_active, self._pages))

        process_started_at = time.time()
        report.url = self._report_creator.create(pages, self._report_name, report.language, number_of_reports)
        process_ended_at = time.time()

        report.processing_time = process_ended_at - process_started_at
        report.status = "DONE"
        return report

    @abstractmethod
    def _load_pages(self) -> None:
        self._pages = []

    def _translate_text(self, text: str) -> str:
        if text in self._locale.messages:
            try:
                return self._locale.get(text)
            except KeyError:
                return text
        return text

    def translate_value(self, value):
        if isinstance(value, dict):
            return self.translate_dict(value)
        elif isinstance(value, list):
            return [self.translate_value(item) for item in value]
        elif isinstance(value, str):
            return self._translate_text(value)
        return value

    def translate_dict(self, input_dict: dict) -> dict:
        if not input_dict:
            return {}
        translated_dict = {}
        for key, value in input_dict.items():
            translated_value = self.translate_value(value)
            translated_dict[key] = translated_value
        return translated_dict