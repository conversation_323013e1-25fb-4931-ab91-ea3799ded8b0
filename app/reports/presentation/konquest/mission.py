import datetime
from typing import List

import pandas as pd
from config.default import Config
from domain.common.dependencies import Report
from reports.presentation import PresentationCreator, PresentationPage, PresentationService
from reports.query.konquest.mission import KonquestMissionQuery
from reports.query.kontent.query import KontentQuery
from reports.utils import format_datetime_to_string, format_seconds_to_HM


class MissionPresentation(PresentationService):
    def __init__(
            self,
            report_creator: PresentationC<PERSON>,
            mission_query: KonquestMissionQuery,
            kontent_query: KontentQuery,
            report_name: str = None,
    ):
        self._mission_id = None
        self._company_id = None
        self._mission_name = None
        super().__init__(report_creator, mission_query, report_name)
        self._main_query = mission_query
        self._kontent_query = kontent_query

    def generate(self, report: Report, number_of_reports: int = 1):
        self._mission_id = report.object_id
        self._company_id = report.workspace_id
        self.load_mission_name()
        return super().generate(report)

    def _load_pages(self) -> None:
        self._pages = [
            PresentationPage(
                name="cover",
                template_key="konquest_mission_cover",
                dataset_lambda=self.get_cover_data,
                params_lambda=self.get_generic_params
            ),
            PresentationPage(
                name="general_information",
                template_key="konquest_mission_general_information",
                dataset_lambda=self.get_general_data,
                params_lambda=self.get_generic_params
            ),
            PresentationPage(
                name="enrollments_analysis",
                template_key="konquest_mission_enrollments_analysis",
                dataset_lambda=self.get_enrollments_data,
                params_lambda=self.get_generic_params
            ),
            PresentationPage(
                name="enrollments_performance_analysis",
                template_key="konquest_mission_enrollments_performance_analysis",
                dataset_lambda=self.get_enrollments_performance_data,
                params_lambda=self.get_generic_params
            ),
            PresentationPage(
                name="quizzes_performance_analysis",
                template_key="konquest_mission_quizzes_performance_analysis",
                dataset_lambda=self.get_quizzes_performances_data,
                params_lambda=self.get_generic_params
            ),
            PresentationPage(
                name="quizzes_analysis",
                template_key="konquest_mission_quizzes_analysis",
                dataset_lambda=self.get_quizzes_data,
                params_lambda=self.get_generic_params
            ),
            PresentationPage(
                name="ranking_user_performance",
                template_key="konquest_mission_ranking_user_performance",
                dataset_lambda=self.get_ranking_user_performance,
                params_lambda=self.get_generic_params
            ),
            PresentationPage(
                name="ranking_user_quizzes_performance",
                template_key="konquest_mission_ranking_user_quizzes_performance",
                dataset_lambda=self.get_ranking_user_quizzes_performance,
                params_lambda=self.get_generic_params
            ),
            PresentationPage(
                name="groups_by_mission",
                template_key="konquest_mission_groups_by_mission",
                dataset_lambda=self.get_groups_data,
                params_lambda=self.get_generic_params
            ),
            PresentationPage(
                name="thanks",
                template_key="thank_report",
                dataset_lambda=self.get_thanks_data,
                params_lambda=self.get_thanks_data
            )
        ]

    def get_generic_params(self) -> dict:
        return {'mission_name': self._mission_name}

    @staticmethod
    def get_cover_data() -> dict:
        return {"cover": None}

    def get_general_data(self) -> dict:
        mission_data = self._main_query.get_general_mission_data(self._mission_id)
        count_content_types = self.count_content_types()
        general_data = {
            "mission_created_user": mission_data["user_name"],
            "user_creator_image": mission_data["user_avatar"],
            "mission_created_date": format_datetime_to_string(mission_data["created_date"]),
            "mission_duration_time": format_seconds_to_HM(mission_data["duration_time"]),
            "mission_points": mission_data["points"] if mission_data["points"] else 0,
            "mission_rating": float(mission_data["rating_avg"]) if mission_data["rating_avg"] else 0.0,
        }
        general_data.update(count_content_types)

        return general_data

    def count_content_types(self) -> dict:
        content_ids = self._main_query.list_mission_learn_content_ids(self._mission_id)
        contents = self._kontent_query.list_learn_contents(content_ids)
        count_types = {}
        for content in contents:
            content_type = str(content.get("content_type_id"))
            if content_type in count_types:
                count_types[content_type] = count_types[content_type] + 1
            else:
                count_types.update({content_type: 1})

        count_content_types = {
            "count_video": count_types.get(Config.CONTENT_TYPE_VIDEO_ID, 0),
            "count_podcast": count_types.get(Config.CONTENT_TYPE_PODCAST_ID, 0),
            "count_docs": count_types.get(Config.CONTENT_TYPE_TEXT_ID, 0),
            "count_spreadsheet": count_types.get(Config.CONTENT_TYPE_SPREADSHEET_ID, 0),
            "count_pdf": count_types.get(Config.CONTENT_TYPE_PDF_ID, 0),
            "count_image": count_types.get(Config.CONTENT_TYPE_IMAGE_ID, 0),
            "count_presentation": count_types.get(Config.CONTENT_TYPE_PRESENTATION_ID, 0),
            "count_quiz": count_types.get(Config.CONTENT_TYPE_QUESTION_ID, 0)
        }
        return count_content_types

    def get_enrollments_data(self) -> dict:
        enrollments_data = self._main_query.get_enrollments_mission_data(self._mission_id)
        count_total = enrollments_data["count_enrollments"]
        if not count_total:
            return {}

        count_done = enrollments_data["count_enrollments_done"]
        count_in_progress = enrollments_data["count_enrollments_in_progress"]
        conclusion_rate = count_done / count_total
        general_data = {
            "enrollments_conclusion_rate": "{:.0%}".format(round(conclusion_rate, 4)),
            "count_enrollments_finished": count_done,
            "count_enrollments_in_progress": count_in_progress
        }

        week_enrollments_data = self._main_query.get_enrollments_mission_data_by_days_filter(self._mission_id, 7)
        month_enrollments_data = self._main_query.get_enrollments_mission_data_by_days_filter(self._mission_id, 30)
        data_by_date_range = {
            "count_enrollments_finished_week": week_enrollments_data["count_enrollments_done"],
            "count_enrollments_started_week": week_enrollments_data["count_enrollments_in_progress"],
            "count_enrollments_finished_month": month_enrollments_data["count_enrollments_done"],
            "count_enrollments_started_month": month_enrollments_data["count_enrollments_in_progress"]
        }
        general_data.update(data_by_date_range)
        return general_data

    def get_enrollments_performance_data(self) -> dict:
        ranges_performance = self._main_query.get_enrollment_range_performance_mission(self._mission_id)
        data = {
            "count_enrollments_performance_0_10": ranges_performance.get("count_performances_0_10"),
            "count_enrollments_performance_10_30": ranges_performance.get("count_performances_10_30"),
            "count_enrollments_performance_30_50": ranges_performance.get("count_performances_30_50"),
            "count_enrollments_performance_50_75": ranges_performance.get("count_performances_50_75"),
            "count_enrollments_performance_75_100": ranges_performance.get("count_performances_75_100")
        }

        return data

    def get_quizzes_performances_data(self) -> dict:
        users_correct_answers = self._main_query.list_users_correct_answers_by_mission(self._mission_id)
        total_questions = self._main_query.get_mission_total_questions(self._mission_id)
        user_performances = []
        for user_answers in users_correct_answers:
            user_performances.append(user_answers['count_hits'] / total_questions)
        data_frame = pd.DataFrame.from_dict({"performance": user_performances})

        data = {
            "count_quizzes_performance_0_10": len(data_frame.query("performance >= 0 and performance <= 0.10 ")),
            "count_quizzes_performance_10_30": len(data_frame.query("performance > 0.10 and performance <= 0.30 ")),
            "count_quizzes_performance_30_50": len(data_frame.query("performance > 0.30 and performance <= 0.50 ")),
            "count_quizzes_performance_50_75": len(data_frame.query("performance > 0.50 and performance <= 0.75 ")),
            "count_quizzes_performance_75_100": len(data_frame.query("performance > 0.75 and performance <= 1 "))
        }

        return data

    def get_quizzes_data(self) -> List[dict]:
        data = []
        exams = self._main_query.list_mission_exams_information(self._mission_id)
        for exam in exams:
            count_hits = exam.get('count_hits')
            count_answers = exam.get('count_answers')
            hits_percentage = "{:.0%}".format(round((count_hits / count_answers), 2)) if count_answers else "0%"
            quiz_data = {
                "quiz_name": exam.get('exam_name'),
                "count_total_questions": exam.get('count_questions'),
                "count_total_answers": exam.get('count_answers'),
                "count_total_hits": exam.get('count_hits'),
                "hits_percentage": hits_percentage,
            }
            data.append(quiz_data)

        return data

    def get_ranking_user_performance(self) -> List[dict]:
        top_performances = self._main_query.list_mission_ranking_performances(self._mission_id)
        data = []
        for performance in top_performances:
            performance_data = {
                "user_name": performance["user_name"].upper(),
                "performance_percentage": "{:.0%}".format(round(float(performance["performance"]), 4)),
                "points": performance["points"]
            }
            data.append(performance_data)

        return data

    def get_ranking_user_quizzes_performance(self) -> List[dict]:
        top_users_hits = self._main_query.list_mission_top_users_quiz_hits(self._mission_id)
        total_questions = self._main_query.get_mission_total_questions(self._mission_id)
        data = []
        for user_hits in top_users_hits:
            count_hits = user_hits["count_hits"] if user_hits["count_hits"] < total_questions else total_questions
            count_error = total_questions - int(count_hits)
            data_add = {
                "user_name": user_hits["user_name"],
                "count_errors": count_error,
                "count_hits": count_hits,
                "total_questions": total_questions,
                "performance_quiz": "{:.0%}".format(round(int(count_hits) / total_questions, 4)),
            }
            data.append(data_add)

        return data

    def get_groups_data(self) -> List[dict]:
        groups = self._main_query.list_mission_groups_data(self._mission_id)
        data = []

        for group in groups:
            group = {
                "group_name": group["group_name"],
                "count_users_group": group["count_users"],
                "count_user_enrollments_finished": group["finished_mission_enrollments"],
                "count_user_enrollments_in_progress": group["started_mission_enrollments"],
                "count_user_enrollments_not_started": group["enrolled_mission_enrollments"]
            }
            data.append(group)

        return data

    @staticmethod
    def get_thanks_data() -> dict:
        return {"generate_date": str(datetime.date.today())}

    def load_mission_name(self) -> None:
        mission_data = self._main_query.get_mission_by_id(self._mission_id)
        self._mission_name = mission_data.get('name')
        return None
