from datetime import date

import numpy as np
from config.default import Config
from domain.report.models import Report
from Locales import Locales
from pandas import DataFrame, NaT
from reports.presentation import PresentationCreator, PresentationPage, PresentationService
from reports.query.account.query import AccountQuery
from reports.query.konquest.user import KonquestUser<PERSON>uery
from reports.query.konquest.workspace import KonquestWorkspaceQuery
from reports.utils import (
    calc_days_difference,
    calc_rate,
    float_to_percentage,
    format_datetime_to_string,
    format_seconds_to_HM,
)


class UserPresentation(PresentationService):
    def __init__(
            self,
            report_creator: PresentationCreator,
            user_query: KonquestUserQuery,
            workspace_query: KonquestWorkspaceQuery,
            account_query: AccountQuery,
            locales: Locales,
            report_name: str = None,
    ):
        super().__init__(report_creator, user_query, report_name, locales=locales)
        self._main_query = user_query
        self._account_query = account_query
        self._workspace_query = workspace_query
        self._today = None
        self._user_id = None
        self._workspace_id = None
        self._date_format = "%d/%m/%Y"
        self._default_value = "---"
        self.user_data = {}


    def generate(self, report: Report, number_of_reports: int = 1) -> Report:
        self._user_id = report.object_id
        self._workspace_id = report.workspace_id
        self._today = date.today()

        self.user_data = self._get_user_data()
        return super().generate(report)

    def _load_pages(self) -> None:
        self._pages = [
            PresentationPage(
                name="first page",
                template_key="user_overview_first_page",
                sub_template_key="subreport_konquest_user_mission",
                dataset_lambda=self.get_first_page_data,
                params_lambda=self.load_first_params
            ),
            PresentationPage(
                name="second page",
                template_key="user_overview_second_page",
                dataset_lambda=self.get_second_page_data,
                params_lambda=self.load_second_params
            ),
            PresentationPage(
                name="third page",
                template_key="user_overview_third_page",
                dataset_lambda=self.get_third_page_data,
                params_lambda=self.load_third_params
            )
        ]

    @staticmethod
    def load_first_params() -> dict:
        return {
            "trail_enrollments_completed_subreport": "konquest/user_overview_v2/subreportTrailsCompleted.jasper",
            "trail_enrollments_in_progress_subreport": "konquest/user_overview_v2/subreportTrailsInProgress.jasper"
        }

    @staticmethod
    def load_second_params() -> dict:
        return {
            "missions_completed_subreport": "konquest/user_overview_v2/subreportMissionsCompleted.jasper",
            "missions_in_progress_subreport": "konquest/user_overview_v2/subreportMissionsInProgress.jasper"
        }

    @staticmethod
    def load_third_params() -> dict:
        return {
            "pulses_subreport": "konquest/user_overview_v2/subreportPulses.jasper",
            "groups_subreport": "konquest/user_overview_v2/subreportGroups.jasper"
        }

    @staticmethod
    def create_answer_performance(total_correct_answers: int, total_mission_questions: int):
        performance = calc_rate(total_correct_answers, total_mission_questions)
        return f"{performance} ({total_correct_answers}/{total_mission_questions})"

    def get_second_page_data(self) -> dict:
        main_data = {
            "enrollments_completed": [],
            "enrollments_in_progress": []
        }
        enrollments = DataFrame(self._main_query.list_mission_enrollments(self._user_id, self._workspace_id))
        if enrollments.empty:
            return {}
        enrollments = self._format_mission_enrollments(enrollments)

        enrollments_completed = enrollments[enrollments["status"].isin(["COMPLETED", "REPROVED"])]
        enrollments_in_progress = enrollments[~enrollments["status"].isin(["COMPLETED", "REPROVED"])]

        answers_performance = []
        for _, enrollment in enrollments_completed.iterrows():
            answers_performance.append(self.create_answer_performance(
                enrollment['total_correct_answers'], enrollment['total_mission_questions']
            ))
        enrollments_completed = enrollments_completed.assign(quizzes=answers_performance)

        self._set_mission_enrollments_statics(enrollments, enrollments_completed, enrollments_in_progress, main_data)

        enrollments_completed = enrollments_completed.replace([np.nan, -np.inf, NaT], self._default_value)
        enrollments_in_progress = enrollments_in_progress.replace([np.nan, -np.inf, NaT], self._default_value)

        enrollments_completed["performance"] = enrollments_completed["performance"].apply(
            lambda performance: float_to_percentage(performance)
        )

        main_data["mission_enrollments_completed"] = enrollments_completed.to_dict("records")
        main_data["mission_enrollments_in_progress"] = enrollments_in_progress.to_dict("records")

        return self.translate_dict(main_data)

    @staticmethod
    def _set_mission_enrollments_statics(enrollments, enrollments_completed, enrollments_in_progress, main_data):
        performance_average = float_to_percentage(enrollments_completed["performance"].aggregate('mean'))
        main_data["mission_enrollments_in_progress_total"] = enrollments_in_progress.shape[0]
        main_data["mission_enrollments_completed_total"] = enrollments_completed.shape[0]
        main_data["mission_enrollments_give_up_total"] = enrollments[
            enrollments["status"] == "GIVE_UP"
            ].shape[0]
        main_data["mission_enrollments_reproved_total"] = enrollments[
            enrollments["status"] == "REPROVED"
            ].shape[0]
        main_data["mission_enrollments_completed_percentage"] = calc_rate(
            enrollments_completed.shape[0], len(enrollments)
        )
        main_data["mission_enrollments_performance_average"] = performance_average

    def _format_mission_enrollments(self, enrollments: DataFrame):
        if enrollments.empty:
            return enrollments
        enrollments["start_date"] = enrollments["start_date"].dt.strftime(self._date_format)
        enrollments["end_date"] = enrollments["end_date"].apply(lambda end_date: format_datetime_to_string(end_date))
        enrollments["goal_date"] = enrollments["goal_date"].apply(
            lambda goal_date: format_datetime_to_string(goal_date)
        )
        enrollments["minimum_performance"] = enrollments["minimum_performance"].apply(
            lambda performance: float_to_percentage(performance)
        )
        enrollments["progress"] = enrollments["progress"].apply(
            lambda performance: float_to_percentage(performance)
        )
        return enrollments

    def get_first_page_data(self) -> dict:
        main_data = {
            "trail_enrollments_in_progress_total": 0,
            "trail_enrollments_completed_total": 0,
            "trail_enrollments_give_up_total": 0,
            "trail_enrollments_reproved_total": 0,
            "trail_enrollments_completed_percentage": 0,
            "trail_enrollments_performance_average": 0
        }
        main_data.update(self.user_data)
        trail_enrollments = DataFrame(self._main_query.list_trail_enrollments(self._user_id, self._workspace_id))
        if trail_enrollments.empty:
            return main_data

        trail_enrollments["start_date"] = trail_enrollments["start_date"].apply(lambda start_date: format_datetime_to_string(start_date))
        trail_enrollments["end_date"] = trail_enrollments["end_date"].apply(lambda end_date: format_datetime_to_string(end_date))
        trail_enrollments = trail_enrollments.replace([np.nan, -np.inf, NaT], self._default_value)

        trail_enrollments_done = trail_enrollments[trail_enrollments["status"].isin(["COMPLETED", "REPROVED"])]
        trail_enrollments_in_progress = trail_enrollments[~trail_enrollments["status"].isin(["COMPLETED", "REPROVED"])]
        trail_enrollments_in_progress.loc[:, "delay_in_days"] = trail_enrollments_in_progress.loc[:, "goal_date"].apply(
            lambda goal_date: calc_days_difference(self._today, goal_date)
        )
        trail_enrollments_done = trail_enrollments_done.replace([np.nan, -np.inf, NaT], self._default_value)
        performance_average = float_to_percentage(trail_enrollments_done["performance"].aggregate('mean'))
        trail_enrollments_in_progress = trail_enrollments_in_progress.replace([np.nan, -np.inf, NaT], self._default_value)

        self._set_trail_enrollments_statics(
            main_data,
            performance_average,
            trail_enrollments,
            trail_enrollments_done,
            trail_enrollments_in_progress
        )

        trail_enrollments_done["progress"] = trail_enrollments_done["progress"].apply(
            lambda progress: float_to_percentage(progress)
        )
        trail_enrollments_in_progress["progress"] = trail_enrollments_in_progress["progress"].apply(
            lambda progress: float_to_percentage(progress)
        )
        trail_enrollments_done["performance"] = trail_enrollments_done["performance"].apply(
            lambda progress: float_to_percentage(progress)
        )
        trail_enrollments_in_progress["goal_date"] = trail_enrollments_in_progress["goal_date"].apply(
            lambda goal_date: format_datetime_to_string(goal_date)
        )

        main_data["trail_enrollments_completed"] = trail_enrollments_done.to_dict('records')
        main_data["trail_enrollments_in_progress"] = trail_enrollments_in_progress.to_dict('records')
        return self.translate_dict(main_data)

    @staticmethod
    def _set_trail_enrollments_statics(
        main_data, performance_average, trail_enrollments, trail_enrollments_done, trail_enrollments_in_progress
    ):
        main_data["trail_enrollments_in_progress_total"] = trail_enrollments_in_progress.shape[0]
        main_data["trail_enrollments_completed_total"] = trail_enrollments_done.shape[0]
        main_data["trail_enrollments_give_up_total"] = trail_enrollments[
            trail_enrollments["status"] == "GIVE_UP"
            ].shape[0]
        main_data["trail_enrollments_reproved_total"] = trail_enrollments[
            trail_enrollments["status"] == "REPROVED"
            ].shape[0]
        main_data["trail_enrollments_completed_percentage"] = calc_rate(trail_enrollments_done.shape[0],
                                                                        trail_enrollments.shape[0])
        main_data["trail_enrollments_performance_average"] = performance_average

    def get_third_page_data(self) -> dict:
        main_data = {}
        main_data.update(self._get_pulses_data())
        main_data.update(self._get_groups_data())
        return main_data

    def _get_pulses_data(self) -> dict:
        pulses_data = self._main_query.get_pulses_general_data(self._user_id, self._workspace_id)
        _pulses_consumed = self._main_query.list_pulses_consumed(self._user_id, self._workspace_id)

        pulses_consumed = []
        for pulse in _pulses_consumed:
            consume_duration = pulse["consume_duration"].total_seconds() / 60 if pulse["consume_duration"] else 0
            pulse_duration = pulse['duration'] / 60 if pulse["duration"] else 0
            consume_progress = calc_rate(consume_duration, pulse_duration) if consume_duration <= pulse_duration else "100%"
            pulses_consumed.append({
                "pulse_name": pulse["name"],
                "channel_name": pulse["channel_name"],
                "pulse_category": pulse["category"],
                "pulse_type": pulse["type"],
                "consume_progress": consume_progress
            })

        data = {
            "pulses_consumed_total": len(pulses_consumed),
            "pulses_created_total": pulses_data["count_pulses_created"],
            "pulses_consumed": pulses_consumed
        }
        return data

    def _get_groups_data(self) -> dict:
        general_data = self._main_query.get_groups_general_data(self._user_id, self._workspace_id)
        _user_groups = self._main_query.list_user_groups(self._user_id, self._workspace_id)

        user_groups = []
        for user_group in _user_groups:
            user_groups.append({
                "group_name": user_group["name"],
                "created_date": format_datetime_to_string(user_group["created_date"]),
                "trail_enrollments_total": user_group["trail_enrollments_total"],
                "trail_enrollments_in_progress": user_group["trail_enrollments_in_progress_total"],
                "trail_enrollments_completed": user_group["trail_enrollments_completed_total"],
                "mission_enrollments_total": user_group["mission_enrollments_total"],
                "mission_enrollment_in_progress": user_group["mission_enrollments_in_progress_total"],
                "mission_enrollment_completed": user_group["mission_enrollments_completed_total"],
                "progress_average": float_to_percentage(user_group["progress_average"]),
                "performance_average": float_to_percentage(user_group["performance_average"]),
            })

        data = {
            "groups_total": general_data["count_groups_linked"],
            "groups": user_groups
        }
        data.update(self.user_data)
        return data

    def _get_user_data(self) -> dict:
        konquest_user_data = self._main_query.get_user_general_data(self._user_id, self._workspace_id, 30, self._today)
        account_data = self._account_query.get_user(self._user_id, self._workspace_id)
        categories = self._main_query.list_top_categories_consumed(self._user_id, self._workspace_id)
        user_engagement = self._calc_profile_rate(konquest_user_data)
        created_date = account_data["created_date"].strftime(self._date_format) if account_data["created_date"] else self._default_value
        last_access_date = konquest_user_data["user_last_access"].strftime(self._date_format) if konquest_user_data["user_last_access"] else self._default_value

        missions_consumed_total = konquest_user_data["missions_consumed_total"]
        mission_enrollment_total = konquest_user_data["mission_enrollment_total"]
        data = {
            "user_name": account_data["name"],
            "user_email": account_data["email"],
            "user_director": account_data["director"] or self._default_value,
            "user_manager": account_data["manager"] or self._default_value,
            "user_area_of_activity": account_data["area_of_activity"] or self._default_value,
            "user_related_leader": account_data["related_user_leader"] or self._default_value,
            "user_job_position": account_data["job"] or self._default_value,
            "user_job_function": account_data["job_function"] or self._default_value,
            "user_created_date": created_date or self._default_value,
            "user_last_access_date": last_access_date or self._default_value,
            "missions_consumed_total": missions_consumed_total,
            "user_consumption_time": format_seconds_to_HM(konquest_user_data["consumption_time_in_pulses"] + konquest_user_data["consumption_time_in_missions"]),
            "enrollment_completed_percentage": calc_rate(missions_consumed_total, mission_enrollment_total),
            "performance_average": "{:.0%}".format(konquest_user_data["performance_average"]),
            "pulses_consumed_total": konquest_user_data["pulses_consumed_total"],
            "top_categories_consumed": ", ".join([category["name"] for category in categories]),
            "user_engagement": "{:.0%}".format(user_engagement)
        }
        return data

    def _calc_profile_rate(self, consume_data: dict) -> float:
        workspace_data = self._workspace_query.get_workspace_count_missions_and_pulses(self._workspace_id)
        count_missions_consumed = min(
            consume_data["mission_enrollments_completed_on_last_days"], Config.MAXIMUM_MISSIONS_COMPLETED
        )
        count_pulses_consumed = min(consume_data["count_pulse_consume_days_on_last_days"], Config.MAXIMUM_PULSES_CONSUMED)
        consume_days = min(
            consume_data["count_days_consuming_pulses_on_last_days"] + consume_data["count_days_consuming_mission_on_last_days"],
            Config.MAXIMUM_DAYS_CONSUMED
        )
        performance_average = consume_data["performance_average_on_last_days"]

        if consume_days == 0:
            return 0

        if workspace_data["count_missions"] < Config.MINIMUM_WORKSPACE_MISSIONS:
            count_missions_consumed = Config.MAXIMUM_MISSIONS_COMPLETED
            performance_average = 1
        if workspace_data["count_pulses"] < Config.MINIMUM_WORKSPACE_PULSES:
            count_pulses_consumed = Config.MAXIMUM_PULSES_CONSUMED

        consume_mission_points = count_missions_consumed * Config.MULTIPLIER_MISSIONS_CONSUMED
        consume_pulse_points = count_pulses_consumed * Config.MULTIPLIER_PULSES_CONSUMED
        consume_days_points = consume_days * Config.MULTIPLIER_DAYS_CONSUME
        sum_points = sum([
            (performance_average * Config.WEIGHT_PERFORMANCE),
            (consume_mission_points * Config.WEIGHT_MISSIONS_CONSUMED),
            (consume_pulse_points * Config.WEIGHT_PULSES_CONSUMED),
            (consume_days_points * Config.WEIGHT_DAYS_CONSUMED)
        ])
        engagement_rate = (
            sum_points / sum([
                Config.WEIGHT_PERFORMANCE,
                Config.WEIGHT_MISSIONS_CONSUMED,
                Config.WEIGHT_PULSES_CONSUMED,
                Config.WEIGHT_DAYS_CONSUMED
            ])
        )
        return engagement_rate
