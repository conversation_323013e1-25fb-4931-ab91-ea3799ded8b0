import datetime
from typing import List

from config.default import Config
from domain.report.models import Report
from reports.constants import CONTENT_TYPE_EXAM
from reports.presentation import PresentationCreator, PresentationPage, PresentationService
from reports.query.account.query import AccountQuery
from reports.query.konquest.workspace import KonquestWorkspaceQuery
from reports.query.kontent.query import KontentQuery


class WorkspacePresentation(PresentationService):
    def __init__(
            self,
            report_creator: PresentationC<PERSON>,
            workspace_selector: KonquestWorkspaceQuery,
            account_selector: AccountQuery,
            kontent_selector: KontentQuery,
            report_name: str
    ):
        self._workspace_id = None
        self._workspace_name = None
        self._today_date = None
        super().__init__(report_creator, workspace_selector, report_name)
        self._main_query = workspace_selector
        self._account_query = account_selector
        self._kontent_query = kontent_selector
        self._users = None

    def _load_pages(self) -> None:
        self._pages = [
            PresentationPage(
                name="cover",
                template_key="konquest_overview_cover",
                dataset_lambda=self.get_cover_data,
                params_lambda=self.get_generic_params
            ),
            PresentationPage(
                name="general_information",
                template_key="konquest_overview_general_information",
                dataset_lambda=self.get_general_data
            ),
            PresentationPage(
                name="users_records",
                template_key="konquest_overview_users_records",
                dataset_lambda=self.get_users_records_data
            ),
            PresentationPage(
                name="enrollments_and_users",
                template_key="konquest_overview_enrollments_and_users",
                dataset_lambda=self.get_enrollments_data
            ),
            PresentationPage(
                name="enrollments_performance_analysis",
                template_key="konquest_overview_enrollments_performance_analysis",
                dataset_lambda=self.get_enrollments_performance_data
            ),
            PresentationPage(
                name="contents_type_in_missions",
                template_key="konquest_overview_contents_type_in_missions",
                dataset_lambda=self.get_missions_content_types_data
            ),
            PresentationPage(
                name="pulses_analysis",
                template_key="konquest_overview_pulses_analysis",
                dataset_lambda=self.get_pulses_data
            ),
            PresentationPage(
                name="consume_pulses_analysis",
                template_key="konquest_overview_consume_pulses_analysis",
                dataset_lambda=self.get_consume_pulses_data_by_content_type
            ),
            PresentationPage(
                name="contents_type_in_pulses",
                template_key="konquest_overview_contents_type_in_pulses",
                dataset_lambda=self.get_pulse_contents_types_data
            ),
            PresentationPage(
                name="ranking_user_performance",
                template_key="konquest_overview_ranking_user_performance",
                dataset_lambda=self.get_performance_ranking
            ),
            PresentationPage(
                name="groups_by_mission_general",
                template_key="konquest_overview_groups_by_mission_general",
                dataset_lambda=self.get_groups_data
            ),
            PresentationPage(
                name="groups_by_mission_detailed",
                template_key="konquest_overview_groups_by_mission_detailed",
                dataset_lambda=self.get_group_missions_data
            )
        ]

    def generate(self, report: Report, number_of_reports: int = 1) -> Report:
        self._workspace_id = report.workspace_id
        self._today_date = datetime.date.today()
        self._users = self._account_query.get_konquest_users_by_workspace(self._workspace_id)
        self._load_workspace_name()
        return super().generate(report)

    def _load_workspace_name(self):
        workspace_data = self._main_query.get_by_id(self._workspace_id)
        self._workspace_name = workspace_data.get('name')
        return None

    def get_general_data(self) -> dict:
        workspace_data = self._main_query.get_general_data(self._workspace_id, [user.get("id") for user in self._users])
        general_data = {
            "count_missions_created": workspace_data["count_missions_created"],
            "count_pulses_created": workspace_data["count_pulses_created"],
            "count_total_users": len(self._users),
            "count_hours_duration_missions": workspace_data["sum_missions_duration"] / 3600,
            "count_hours_duration_pulses": workspace_data["sum_pulses_duration"] / 3600,
            "count_users_active": workspace_data["count_users_active"]
        }

        return general_data

    def get_users_records_data(self) -> dict:
        week_data = self._main_query.get_activity_user_data(self._workspace_id, 7, self._today_date)
        month_data = self._main_query.get_activity_user_data(self._workspace_id, 30, self._today_date)
        user_ids = [user.get("id") for user in self._users]
        week_growth = week_data.get("growth_user_active")
        month_growth = month_data.get("growth_user_active")
        data = {
            "total_users_registered": len(self._users),
            "count_users_active_week": week_data["count_users_active"],
            "count_users_finished_mission_week": week_data["count_users_completed_mission"],
            "count_users_finished_pulse_week": week_data["count_users_consumed_pulse"],
            "percentage_growth_week": "{:.0%}".format(week_growth),
            "arrow_image_week": Config.ICON_URL_ARROW_UP if week_growth >= 0 else Config.ICON_URL_ARROW_DOWN,
            "count_users_active_month": month_data["count_users_active"],
            "count_users_finished_mission_month": month_data["count_users_completed_mission"],
            "count_users_finished_pulse_month": month_data["count_users_consumed_pulse"],
            "percentage_growth_month": "{:.0%}".format(month_growth),
            "arrow_image_month": Config.ICON_URL_ARROW_UP if month_growth >= 0 else Config.ICON_URL_ARROW_DOWN,
            "count_users_absent": self._main_query.count_users_ghost(user_ids)
        }
        return data

    def get_enrollments_data(self) -> dict:
        all_data = self._main_query.get_mission_enrollments_data(self._workspace_id)

        data = {
            "count_unique_users": len(self._users),
            "count_enrollments_finished": all_data["count_enrollments_completed"],
            "count_enrollments_in_progress": all_data["count_enrollments_in_progress"],
        }
        data.update(self._get_week_enrollments_data())
        data.update(self._get_month_enrollments_data())
        return data

    def _get_week_enrollments_data(self) -> dict:
        enrollments_data = self._main_query.get_mission_enrollments_interval_data(self._workspace_id, 7, self._today_date)
        completed_growth = enrollments_data["growth_enrollments_completed"]
        started_growth = enrollments_data["growth_enrollments_in_progress"]
        completed_growth_image = Config.ICON_URL_ARROW_UP if completed_growth >= 0 else Config.ICON_URL_ARROW_DOWN
        started_growth_image = Config.ICON_URL_ARROW_UP if started_growth >= 0 else Config.ICON_URL_ARROW_DOWN

        data = {
            "count_total_enrollments_finished_week": enrollments_data["count_enrollments_completed"],
            "count_distinct_enrollments_finished_week": enrollments_data["count_enrollments_completed_users"],
            "percentage_enrollments_finished_growth_week": "{:.0%}".format(completed_growth),
            "enrollments_finished_week_arrow_image": completed_growth_image,
            "count_total_enrollments_in_progress_week": enrollments_data["count_enrollments_in_progress"],
            "count_distinct_enrollments_in_progress_week": enrollments_data["count_enrollments_in_progress_users"],
            "percentage_enrollments_in_progress_growth_week": "{:.0%}".format(started_growth),
            "enrollments_in_progress_week_arrow_image": started_growth_image,
        }
        return data

    def _get_month_enrollments_data(self) -> dict:
        enrollments_data = self._main_query.get_mission_enrollments_interval_data(self._workspace_id, 30, self._today_date)
        completed_growth = enrollments_data["growth_enrollments_completed"]
        started_growth = enrollments_data["growth_enrollments_in_progress"]
        completed_growth_image = Config.ICON_URL_ARROW_UP if completed_growth >= 0 else Config.ICON_URL_ARROW_DOWN
        started_growth_image = Config.ICON_URL_ARROW_UP if started_growth >= 0 else Config.ICON_URL_ARROW_DOWN

        data = {
            "count_total_enrollments_finished_month": enrollments_data["count_enrollments_completed"],
            "count_distinct_enrollments_finished_month": enrollments_data["count_enrollments_completed_users"],
            "percentage_enrollments_finished_growth_month": "{:.0%}".format(completed_growth),
            "enrollments_finished_month_arrow_image": completed_growth_image,
            "count_total_enrollments_in_progress_month": enrollments_data["count_enrollments_in_progress"],
            "count_distinct_enrollments_in_progress_month": enrollments_data["count_enrollments_in_progress_users"],
            "percentage_enrollments_in_progress_growth_month": "{:.0%}".format(started_growth),
            "enrollments_in_progress_month_arrow_image": started_growth_image
        }
        return data

    def get_enrollments_performance_data(self) -> dict:
        performance_ranges = self._main_query.get_enrollment_range_performance(self._workspace_id)
        data = {
            "count_enrollments_performance_0_10": performance_ranges.get("count_performances_0_10"),
            "count_enrollments_performance_10_30": performance_ranges.get("count_performances_10_30"),
            "count_enrollments_performance_30_50": performance_ranges.get("count_performances_30_50"),
            "count_enrollments_performance_50_75": performance_ranges.get("count_performances_50_75"),
            "count_enrollments_performance_75_100": performance_ranges.get("count_performances_75_100")
        }
        return data

    def get_missions_content_types_data(self) -> dict:
        contents = self._main_query.list_missions_learn_contents(self._workspace_id)
        content_ids = [content.get('learn_content_id') for content in contents] if contents else []
        count_quizzes = len(list(filter(lambda content: content["content_type"] == CONTENT_TYPE_EXAM, contents)))

        count_contents_by_type = self._kontent_query.count_content_types(content_ids)
        data = {
            "count_video": count_contents_by_type.get(Config.CONTENT_TYPE_VIDEO_ID, 0),
            "count_podcast": count_contents_by_type.get(Config.CONTENT_TYPE_PODCAST_ID, 0),
            "count_doc": count_contents_by_type.get(Config.CONTENT_TYPE_TEXT_ID, 0),
            "count_spreadsheet": count_contents_by_type.get(Config.CONTENT_TYPE_SPREADSHEET_ID, 0),
            "count_pdf": count_contents_by_type.get(Config.CONTENT_TYPE_PDF_ID, 0),
            "count_image": count_contents_by_type.get(Config.CONTENT_TYPE_IMAGE_ID, 0),
            "count_presentation": count_contents_by_type.get(Config.CONTENT_TYPE_PRESENTATION_ID, 0),
            "count_quiz": count_quizzes
        }
        return data

    def get_pulses_data(self) -> dict:
        all_data = self._main_query.get_pulses_data(self._workspace_id)
        data = {
            "count_total_pulses": all_data["count_pulses"],
            "count_total_consume": all_data["count_pulses_consumed"],
            "popular_channel": all_data["popular_channel"] or "---"
        }
        data.update(self._get_week_pulses_data())
        data.update(self._get_month_pulses_data())

        return data

    def _get_week_pulses_data(self) -> dict:
        week_data = self._main_query.get_pulses_data_interval(self._workspace_id, 7, self._today_date)
        created_growth = week_data["growth_pulses"]
        consume_growth = week_data["growth_pulses_consumed"]
        created_growth_image = Config.ICON_URL_ARROW_UP if created_growth >= 0 else Config.ICON_URL_ARROW_DOWN
        consumed_growth_image = Config.ICON_URL_ARROW_UP if consume_growth >= 0 else Config.ICON_URL_ARROW_DOWN
        data = {
            "count_pulses_created_week": week_data["count_pulses"],
            "count_consume_week": week_data["count_pulses_consumed"],
            "pulses_created_week_arrow_image": created_growth_image,
            "percentage_pulses_created_week": "{:.0%}".format(created_growth),
            "consume_week_arrow_image": consumed_growth_image,
            "percentage_consume_week": "{:.0%}".format(consume_growth)
        }

        return data

    def _get_month_pulses_data(self) -> dict:
        month_data = self._main_query.get_pulses_data_interval(self._workspace_id, 30, self._today_date)
        created_growth = month_data["growth_pulses"]
        consume_growth = month_data["growth_pulses_consumed"]
        created_growth_image = Config.ICON_URL_ARROW_UP if created_growth >= 0 else Config.ICON_URL_ARROW_DOWN
        consumed_growth_image = Config.ICON_URL_ARROW_UP if consume_growth >= 0 else Config.ICON_URL_ARROW_DOWN
        data = {
            "count_pulses_created_month": month_data["count_pulses"],
            "count_consume_month": month_data["count_pulses_consumed"],
            "pulses_created_month_arrow_image": created_growth_image,
            "percentage_pulses_created_month": "{:.0%}".format(created_growth),
            "consume_month_arrow_image": consumed_growth_image,
            "percentage_consume_month": "{:.0%}".format(consume_growth),
        }
        return data

    def get_consume_pulses_data_by_content_type(self) -> dict:
        count_contents_consumes = self._main_query.list_count_consumes_by_pulse_content(self._workspace_id)
        if not count_contents_consumes:
            return {}
        content_ids = [row["learn_content_id"] for row in count_contents_consumes]
        learn_contents = self._kontent_query.list_learn_contents(content_ids)
        count_consumes_by_type, total_consume = self._count_consumes_by_type(count_contents_consumes, learn_contents)
        if not total_consume:
            return {}

        data = {
            "percentage_consume_video": count_consumes_by_type.get(Config.CONTENT_TYPE_VIDEO_ID, 0) / total_consume,
            "percentage_consume_podcast": count_consumes_by_type.get(Config.CONTENT_TYPE_PODCAST_ID, 0) / total_consume,
            "percentage_consume_doc": count_consumes_by_type.get(Config.CONTENT_TYPE_TEXT_ID, 0) / total_consume,
            "percentage_consume_spreadsheet": count_consumes_by_type.get(Config.CONTENT_TYPE_SPREADSHEET_ID, 0) / total_consume,
            "percentage_consume_pdf": count_consumes_by_type.get(Config.CONTENT_TYPE_PDF_ID, 0) / total_consume,
            "percentage_consume_image": count_consumes_by_type.get(Config.CONTENT_TYPE_IMAGE_ID, 0) / total_consume,
            "percentage_consume_presentation": count_consumes_by_type.get(Config.CONTENT_TYPE_PRESENTATION_ID, 0) / total_consume,
            "percentage_consume_quiz": count_consumes_by_type.get(Config.CONTENT_TYPE_QUESTION_ID, 0) / total_consume,
        }
        for key in data:
            data[key] = "{:.0%}".format(data[key])

        return data

    @staticmethod
    def _count_consumes_by_type(count_contents_consumes, learn_contents) -> (dict, int):
        count_consumes_by_type = {}
        total_consume = 0
        for count_consume in count_contents_consumes:
            learn_content = list(filter(lambda content: content['id'] == count_consume["learn_content_id"], learn_contents))
            if not learn_content:
                continue
            learn_content = learn_content[0]
            learn_content_type = str(learn_content["content_type_id"])
            if learn_content_type in count_consumes_by_type:
                count_consumes_by_type[learn_content_type] += count_consume["count_consume"]
            else:
                count_consumes_by_type.update({learn_content_type: count_consume["count_consume"]})
            total_consume += count_consume["count_consume"]

        return count_consumes_by_type, total_consume

    def get_pulse_contents_types_data(self) -> dict:
        content_ids = self._main_query.list_pulse_learn_contents_ids(self._workspace_id)
        count_contents_by_type = self._kontent_query.count_content_types(content_ids)
        data = {
            "count_video": count_contents_by_type.get(Config.CONTENT_TYPE_VIDEO_ID, 0),
            "count_podcast": count_contents_by_type.get(Config.CONTENT_TYPE_PODCAST_ID, 0),
            "count_doc": count_contents_by_type.get(Config.CONTENT_TYPE_TEXT_ID, 0),
            "count_spreadsheet": count_contents_by_type.get(Config.CONTENT_TYPE_SPREADSHEET_ID, 0),
            "count_pdf": count_contents_by_type.get(Config.CONTENT_TYPE_PDF_ID, 0),
            "count_image": count_contents_by_type.get(Config.CONTENT_TYPE_IMAGE_ID, 0),
            "count_presentation": count_contents_by_type.get(Config.CONTENT_TYPE_PRESENTATION_ID, 0),
            "count_quiz": count_contents_by_type.get(Config.CONTENT_TYPE_QUESTION_ID, 0)
        }
        return data

    def get_performance_ranking(self) -> List[dict]:
        performances = self._main_query.list_top_user_performances(self._workspace_id)
        data = []
        for user in performances:
            if user["performance"] == "None":
                continue
            performance_data = {
                "user_name": user["user_name"].upper(),
                "performance_percentage": "{:.1%}".format(user["performance"]),
                "points": float(user["points"])
            }
            data.append(performance_data)

        return data

    def get_groups_data(self) -> List[dict]:
        groups = self._main_query.list_group_general_data(self._workspace_id)
        data = []
        for group in groups:
            data_add = {
                "group_name": group["group_name"],
                "count_missions_by_group": group["count_group_missions"],
                "count_users_by_group": group["count_group_users"]
            }
            data.append(data_add)

        return data

    def get_group_missions_data(self) -> List[dict]:
        group_missions = self._main_query.list_group_missions_data(self._workspace_id)
        data = []
        for group_mission in group_missions:
            total_enrollments = group_mission["count_enrollments"]
            count_completed_enrollments = group_mission["count_completed_enrollments"]
            count_progress_enrollments = group_mission["count_enrollments_in_progress"]
            count_enrolled_enrollments = group_mission["count_enrolled_enrollments"]
            if not total_enrollments:
                completed_enrollments_per = "0%"
                progress_enrollments_per = "0%"
                enrolled_enrollments_per = "0%"
            else:
                completed_enrollments_per = "{:.0%}".format(count_completed_enrollments / total_enrollments)
                progress_enrollments_per = "{:.0%}".format(count_progress_enrollments / total_enrollments)
                enrolled_enrollments_per = "{:.0%}".format(count_enrolled_enrollments / total_enrollments)
            data_add = {
                "group_name": group_mission["group_name"],
                "count_missions_by_group": group_mission["count_group_missions"],
                "count_user_enrollments_finished": f'{count_completed_enrollments} ({completed_enrollments_per})',
                "count_user_enrollments_in_progress": f'{count_progress_enrollments} ({progress_enrollments_per})',
                "count_user_enrollments_not_started": f'{ count_enrolled_enrollments} ({enrolled_enrollments_per})'
            }
            data.append(data_add)

        return data

    def get_thank_params(self) -> dict:
        return {"generate_date": str(self._today_date)}

    def get_thank_data(self) -> dict:
        return {"data": str(self._today_date)}

    def get_generic_params(self) -> dict:
        return {"workspace_name": self._workspace_name.upper()}

    def get_cover_data(self) -> dict:
        return {"data": str(self._today_date)}
