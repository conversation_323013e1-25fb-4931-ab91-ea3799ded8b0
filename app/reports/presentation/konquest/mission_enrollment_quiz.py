from domain.report.models import Report
from Locales import Locales
from pandas import DataFrame
from reports.presentation import PresentationCreator, PresentationPage, PresentationService
from reports.query.konquest.mission_enrollment_quiz import KonquestMissionEnrollmentQuizQuery
from reports.utils import calc_rate, float_to_percentage, remove_html_tags

DATA_POSITION = 1

class MissionEnrollmentQuizPresentation(PresentationService):
    def __init__(
        self,
        report_creator: PresentationCreator,
        query: KonquestMissionEnrollmentQuizQuery,
        locales: Locales,
        report_name: str = None,
    ):
        self._mission_id = None
        self._workspace_id = None
        self._mission_name = None
        self._filters = None
        self._report_generation_position: int = 0
        super().__init__(report_creator, query, report_name, locales)
        self._main_query = query
        self._enrollments: DataFrame = None
        self._options_by_question: DataFrame = None

    def generate(self, report: Report, **kwargs) -> Report:
        self._workspace_id = report.workspace_id
        self._filters = report.filters
        self._enrollments = self._main_query.list_mission_enrollments(self._workspace_id, self._filters)
        if self._enrollments.empty:
            return super().generate(report)

        self._enrollments["performance"] = self._enrollments["performance"].apply(lambda performance: float_to_percentage(performance))
        mission_ids = self._enrollments["mission_id"].to_list()
        self._options_by_question = self._main_query.list_question_and_answers(mission_ids)
        number_of_reports = self._enrollments.shape[0]

        return super().generate(report, number_of_reports)

    def get_main_data(self):
        if self._enrollments.empty or self._options_by_question.empty:
            return
        enrollment = list(self._enrollments.iterrows())[self._report_generation_position][DATA_POSITION]
        self._report_generation_position += 1
        if not enrollment["user_email"]:
            return

        total_correct_answers = enrollment["total_correct_answers"] or 0
        total_mission_questions = enrollment["total_mission_questions"] or 0
        quiz_performance = calc_rate(total_correct_answers, total_mission_questions)
        end_date = enrollment["end_date"].strftime("%Y/%m/%d %H:%M:%S") if enrollment["end_date"] else '-----'

        data = {
            "workspace_name": enrollment["workspace_name"],
            "workspace_logo_url": enrollment["workspace_logo_url"],
            "primary_color": enrollment["workspace_color"],
            "secondary_color": enrollment["workspace_color"],
            "user_name": enrollment["user_name"],
            "user_email": enrollment["user_email"],
            "user_director": enrollment["user_director"],
            "user_manager": enrollment["user_manager"],
            "user_area_of_activity": enrollment["user_area_of_activity"],
            "user_job_position": enrollment["user_job"],
            "user_job_function": enrollment["user_job_function"],
            "mission_name": enrollment["mission_name"],
            "assessment_type": self._translate_text(enrollment["assessment_type"]) or '----',
            "enrollment_created_date": enrollment["created_date"],
            "enrollment_end_date": end_date,
            "enrollment_progress": enrollment["progress"],
            "enrollment_quiz_performance": f"{quiz_performance} ({total_correct_answers}/{total_mission_questions})",
            "enrollment_performance": enrollment["performance"],
            "enrollment_status": self._translate_text(enrollment["enrollment_status"])
        }

        options_enrollment_filter = self._options_by_question["mission_enrollment_id"] == enrollment["id"]
        options_created_after_enroll_filter = self._options_by_question["question_created_date"] < enrollment["end_date"]
        options_by_question = self._options_by_question[options_enrollment_filter]
        if options_by_question.empty:
            return
        options_by_question = options_by_question.loc[options_created_after_enroll_filter]
        if options_by_question.empty:
            return

        questions = []
        options_df = options_by_question.drop_duplicates('question_id')
        for _, option in options_df.iterrows():
            question_answers = options_by_question[options_by_question["question_id"] == option["question_id"]]
            options = self._get_question_options(question_answers)
            questions.append({
                "title": remove_html_tags(option["exam_question"]),
                "answer_date": option["answer_date"],
                "options": options
            })

        data.update({"quizzes": questions})
        return data

    @staticmethod
    def _get_question_options(question_answers):
        options = []
        for _, question_option in question_answers.iterrows():
            options.append(
                {
                    "text": question_option["option"],
                    "is_correct": question_option["correct_answer"],
                    "checked": question_option["checked"]
                }
            )
        return options

    def _load_pages(self) -> None:
        self._pages = [
            PresentationPage(
                name="cover",
                template_key="konquest_mission_enrollment_quiz_first_page",
                dataset_lambda=self.get_main_data,
                params_lambda=self._get_params
            )
        ]

    @staticmethod
    def _get_params():
        return {
            "jasper_subreport_path": "konquest/quiz_by_mission/subreportQuizQuestion.jasper",
            "jasper_subreport_second_level": "konquest/quiz_by_mission/subreportQuizQuestionOption.jasper"
        }
