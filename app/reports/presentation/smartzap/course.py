import datetime

from domain.report.models import Report
from reports.presentation import PresentationCreator, PresentationPage, PresentationService
from reports.query.account.query import AccountQuery
from reports.query.kontent.query import KontentQuery
from reports.query.smartzap.course import SmartzapCourseQuery


class CoursePresentation(PresentationService):
    def __init__(
            self,
            report_creator: PresentationCreator,
            course_selector: SmartzapCourseQuery,
            account_selector: AccountQuery,
            kontent_selector: KontentQuery,
            report_name: str,
    ):
        super().__init__(report_creator, course_selector, report_name)
        self._main_query = course_selector
        self._account_query = account_selector
        self._kontent_query = kontent_selector
        self._company_id = None
        self._course_id = None
        self._company_name = None
        self._today_date = None

    def _load_pages(self) -> None:
        self._pages = [
            PresentationPage(
                name="cover",
                template_key="smartzap_cover",
                dataset_lambda=self.get_cover_data,
                params_lambda=self.get_cover_data
            ),
            PresentationPage(
                name="enrollments",
                template_key="smartzap_enrollment",
                dataset_lambda=self.get_enrollments_data,
            ),
            PresentationPage(
                name="performances",
                template_key="smartzap_performance",
                dataset_lambda=self.get_performances_data,
            ),
            PresentationPage(
                name="consume_content",
                template_key="smartzap_consume_content",
                dataset_lambda=self.get_contents_consume_data,
            ),
            PresentationPage(
                name="consume_quiz",
                template_key="smartzap_consume_quiz",
                dataset_lambda=self.get_quizzes_answers_data,
            ),
            PresentationPage(
                name="performance_ranking",
                template_key="smartzap_ranking_users_performance",
                dataset_lambda=self.get_performance_ranking,
            ),
            PresentationPage(
                name="performance_in_quizzes_ranking",
                template_key="smartzap_ranking_users_quiz_performance",
                dataset_lambda=self.get_quizzes_ranking,
            ),
            PresentationPage(
                name="thanks",
                template_key="thank_report",
                dataset_lambda=self.get_thanks_data,
                params_lambda=self.get_thanks_data
            ),
        ]

    def generate(self, report: Report, number_of_reports: int = 1) -> Report:
        self._course_id = report.object_id
        self._today_date = datetime.date.today()
        self._load_course_name()
        return super().generate(report)

    def get_cover_data(self) -> dict:
        return {"course_name": self._course_name}

    def _load_course_name(self):
        course_data = self._main_query.get_by_id(self._course_id)
        self._course_name = course_data.get('name')
        return None

    def get_enrollments_data(self) -> dict:
        enrollments_data = self._main_query.get_enrollments_data(self._course_id)
        return {
            "total_enrollments": enrollments_data["count_enrollments"],
            "enrollments_active": enrollments_data["count_active_enrollments"],
            "enrollments_finished": enrollments_data["count_finished_enrollments"],
            "enrollments_waiting": enrollments_data["count_waiting_enrollments"],
            "enrollments_refused": enrollments_data["count_refused_enrollments"],
            "errors_enrollments": enrollments_data["count_error_enrollments"]
        }

    def get_performances_data(self) -> dict:
        performances_range = self._main_query.get_enrollment_performances_range_data(self._course_id)
        data = {
            "num_performance_0": performances_range['0'],
            "num_performance_10": performances_range['0-10'],
            "num_performance_10-30": performances_range['10-30'],
            "num_performance_30-50": performances_range['30-50'],
            "num_performance_50-70": performances_range['50-70'],
            "num_performance_70-100": performances_range['70-100'],
            "total_enrollments": performances_range["count_enrollments"]
        }
        return data

    def get_contents_consume_data(self) -> list:
        contents = self._main_query.list_contents_consume_data(self._course_id)
        data = []
        for content in contents:
            content_consume = {
                "label": content["content_name"],
                "totalUsers": content["count_user_access"],
                "totalHours": round(content['sum_consume_time'] / 3600)
            }
            data.append(content_consume)

        return data

    def get_quizzes_answers_data(self) -> list:
        contents = self._main_query.list_question_contents(self._course_id)
        learn_content_ids = [contents['learn_content_id'] for contents in contents]
        quizzes = self._kontent_query.list_quizzes_answers_data(learn_content_ids)
        data = []

        for quiz in quizzes:
            quiz_data = {
                "quiz_name": quiz["quiz_title"],
                "num_users_accessed": quiz["count_users_accessed"],
                "num_users_finished": quiz["count_users_finished"]
            }
            data.append(quiz_data)

        return data

    def get_performance_ranking(self) -> list:
        enrollments = self._main_query.list_enrollment_performance_ranking(self._course_id)
        data = []
        for enrollment in enrollments:
            performance = enrollment["performance"] if enrollment["performance"] <= 1 else 1
            enrollment_data = {
                "user_phone": enrollment["user_phone"],
                "user_name": enrollment["user_name"],
                "user_tags": enrollment["user_tags"],
                "user_performance": "{:.0%}".format(round(performance, 1)),
                "user_points": round(enrollment["points"])
            }
            data.append(enrollment_data)

        return data

    def get_quizzes_ranking(self) -> list:
        enrollments = self._main_query.list_enrollments(self._course_id)
        enrollment_ids = [enrollment['id'] for enrollment in enrollments]
        quizzes_ranking = self._kontent_query.list_answer_hit_ranking_by_enrollment(enrollment_ids)
        count_questions = self._count_questions()
        data = []

        for position in quizzes_ranking:
            enrollment = next(filter(lambda row: row['id'] == position["enrollment_id"], enrollments), None)
            if not enrollment:
                continue

            count_hits = position["count_hits"]
            count_mistakes = count_questions - count_hits
            performance = count_hits / count_questions if count_questions else 1

            data.append({
                "user_name": enrollment["user_name"],
                "tags": enrollment["user_tags"],
                "num_error": count_mistakes,
                "num_hit": count_hits,
                "total_question": count_questions,
                "performance": "{:.0%}".format(round(performance, 1))
            })

        return data

    def _count_questions(self) -> int:
        contents = self._main_query.list_question_contents(self._course_id)
        learn_content_ids = [contents['learn_content_id'] for contents in contents]
        return self._kontent_query.count_questions(learn_content_ids)

    def get_thanks_data(self) -> dict:
        return {"generate_date": str(self._today_date)}
