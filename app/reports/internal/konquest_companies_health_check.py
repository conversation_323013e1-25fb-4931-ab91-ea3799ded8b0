from domain.report.models import Report
from pandas import DataFrame, to_datetime
from reports.export import Export<PERSON>uilder, ExportCsvCreator, ExportPart, ExportXlsxCreator
from reports.formatters.formatter_service import FormatterService
from reports.internal.query.billing_query import KonquestBillingQuery


class KonquestCompaniesHealthCheck(ExportBuilder):

    def __init__(
        self,
        xlsx_creator: ExportXlsxCreator,
        csv_creator: ExportCsvCreator,
        query_selector: KonquestBillingQuery,
        formatter_service: FormatterService,
        report_name: str = None
    ):
        super().__init__(xlsx_creator, csv_creator, query_selector, formatter_service, report_name)
        self._main_query = query_selector
        self._workspace_id = None
        self._filters = {}

    def generate(self, report: Report) -> Report:
        self._workspace_id = report.workspace_id
        self._filters = report.filters
        return super().generate(report)

    def load_summary_data(self) -> DataFrame:
        latest_period = self._main_data["period"].max()

        latest_df = self._main_data[self._main_data["period"] == latest_period][["company_name", "unique_users"]]
        latest_df = latest_df.rename(columns={"unique_users": "unique_users_last_month"})

        avg_df = self._main_data.groupby("company_name")["unique_users"].mean().reset_index()
        avg_df = avg_df.rename(columns={"unique_users": "avg_users_with_activate"})

        summary_df = latest_df.merge(avg_df, on="company_name", how="outer")

        summary_df = summary_df.fillna(0)

        return summary_df

    def transform_main_data(self, df: DataFrame) -> DataFrame:
        df["period"] = df["period"].apply(lambda x: to_datetime(x).strftime("%m/%Y"))

        df["company_name"] = df.apply(
            lambda row: f"{row['company_name']} ({str(row['company_id'])[:4]})", axis=1
        )

        df = df.drop(columns=["company_id", "activity_count"])

        return df

    def load_main_data(self) -> DataFrame:
        start_date = self._filters["start_date"]
        end_date = self._filters["end_date"]

        companies = self._main_query.list_companies_billing(start_date, end_date, auth_workspace_id=self._workspace_id)

        return self.transform_main_data(companies)

    def get_main_data(self) -> DataFrame:
        return self._main_data

    def _load_parts(self) -> None:
        self._parts = [
            ExportPart(
                name="raw",
                data_lambda=self.get_main_data,
                index=False
            ),
            ExportPart(
                name="summary",
                data_lambda=self.load_summary_data,
                index=False
            ),
        ]
