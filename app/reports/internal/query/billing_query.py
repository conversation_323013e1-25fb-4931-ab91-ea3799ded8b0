from typing import Dict, List

from config.default import Config
from pandas import DataFrame, concat
from reports.internal.query.user_activities_by_month_query import UserActivitiesByMonthQuery
from reports.query import QueryExecutor
from reports.query.account.export import AccountExportQuery
from sqlalchemy.engine import Engine


class KonquestBillingQuery(QueryExecutor):
    def __init__(
        self,
        konquest_database_engine: Engine,
        account_export_query: AccountExportQuery,
        user_activities_by_month_query: UserActivitiesByMonthQuery,
        template_folder: str
    ):
        super().__init__(konquest_database_engine, template_folder)
        self.account_export_query = account_export_query
        self.user_activities_by_month_query = user_activities_by_month_query

    def list_companies_billing(self, start_date: str, end_date: str, auth_workspace_id: str) -> DataFrame:
        if str(auth_workspace_id) != Config.KEEPS_WORKSPACE_ID:
            raise RuntimeError("Unable to generate billing report for workspace")

        workspaces_df = self.account_export_query.list_workspaces_by_companies(Config.KONQUEST_APPLICATION_ID)
        if workspaces_df.empty:
            return DataFrame()
        companies_df = workspaces_df[["company_id", "company_name"]].drop_duplicates(subset=["company_id"])

        workspaces_grouped_by_company: Dict[str, List[str]] = {}
        for _, row in workspaces_df.iterrows():
            company_id = row["company_id"]
            workspace_id = row["workspace_id"]

            if company_id not in workspaces_grouped_by_company:
                workspaces_grouped_by_company[company_id] = []

            workspaces_grouped_by_company[company_id].append(workspace_id)

        all_activities = []
        for company_id, workspace_ids in workspaces_grouped_by_company.items():
            activities_by_month = self.user_activities_by_month_query.list(start_date, end_date, workspace_ids)
            if not activities_by_month.empty:
                activities_by_month["company_id"] = company_id
                all_activities.append(activities_by_month)

        if not all_activities:
            return DataFrame()

        activities_by_month_main = concat(all_activities, ignore_index=True)

        return activities_by_month_main.merge(companies_df, how="left", on="company_id", validate="many_to_one")
