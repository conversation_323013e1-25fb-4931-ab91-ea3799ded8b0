import datetime
from typing import Dict, List

import pandas as pd
from domain.user.models_v2 import UsersStatsFilter
from domain.user.service_v2 import UserServiceV2
from pandas import DataFrame


class UserActivitiesByMonthQuery:
    def __init__(self, user_service: UserServiceV2):
        self.user_service = user_service

    def list(self, start_date: datetime, end_date: datetime, workspace_ids: List) -> DataFrame:
        user_stats_filter = UsersStatsFilter(
            start_date=start_date,
            end_date=end_date,
            workspace_ids=workspace_ids
        )

        response = self.user_service.get_active_users(user_stats_filter)
        results = self._list_results(response, start_date, end_date)

        return DataFrame(results)

    @staticmethod
    def _list_results(response: Dict, start_date: datetime.date, end_date: datetime.date) -> List[Dict]:
        buckets: List[Dict] = response.get("aggs", {}).get("activity_histogram", {}).get("buckets", [])
        result_list = []

        periods_str = pd.date_range(start=start_date, end=end_date, freq='MS').strftime('%Y-%m-%dT00:00:00.000Z').tolist()

        bucket_map = {bucket.get('key_as_string'): bucket for bucket in buckets}

        for period in periods_str:
            bucket = bucket_map.get(period)
            if bucket:
                result_list.append({
                    "period": period,
                    "activity_count": bucket.get("doc_count", 0),
                    "unique_users": bucket.get("users_per_interval", {}).get("value", 0),
                })
            else:
                result_list.append({
                    "period": period,
                    "activity_count": 0,
                    "unique_users": 0,
                })

        return result_list
