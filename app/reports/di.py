import datetime as dt
import logging

import database_konquest
import database_kontent
import database_myaccount
import database_smartzap
import injector
import reports as reports
from config.default import Config
from custom.discord_webhook_logger import DiscordWebhookLogger
from domain.common.dependencies import ElasticsearchConnection
from domain.elasticsearch.search_factory import ElasticSearchFactory
from domain.user.service_v2 import UserServiceV2
from elasticsearch import Elasticsearch
from elasticsearch_dsl.connections import connections
from Locales import Locales
from reports.builder import ExportBuilder
from reports.constants import (
    KONQUEST_COMPANIES_HEALTH_CHECK,
    KONQUEST_CONTENTS_EXPORT,
    KONQUEST_GROUPS_CHANNELS_USERS_EXPORT,
    KONQUEST_GROUPS_MISSIONS_USERS_EXPORT,
    KONQUEST_MISSION_CONTENTS_ACTIVITIES_EXPORT,
    KONQUEST_MISSION_ENROLLMENT_QUIZZES_PRESENTATION,
    KONQUEST_MISSION_EVALUATIONS_STATISTICS_EXPORT,
    KONQUEST_MISSION_PRESENTATION,
    KONQUEST_MISSIONS_ENROLLMENTS_EXPORT,
    KONQUEST_MISSIONS_EVALUATIONS_EXPORT,
    KONQUEST_MISSIONS_EXPORT,
    KONQUEST_MISSIONS_QUIZZES_ANSWERS_EXPORT,
    KONQUEST_PULSES_CHANNELS_EXPORT,
    KONQUEST_PULSES_QUIZZES_ANSWERS_EXPORT,
    KONQUEST_TRAILS_COMPLETION_RATE_EXPORT,
    KONQUEST_TRAILS_ENROLLMENTS_EXPORT,
    KONQUEST_TRAILS_EXPORT,
    KONQUEST_USER_CONSUMPTIONS_EXPORT,
    KONQUEST_USER_GENERAL_STATISTICS_EXPORT,
    KONQUEST_USER_PRESENTATION,
    KONQUEST_USERS_ACCESS_BY_DATE_EXPORT,
    KONQUEST_USERS_EXPORT,
    KONQUEST_USERS_PULSES_ACTIVITIES_EXPORT,
    KONQUEST_WORKSPACE_PRESENTATION,
    MYACCOUNT_USER_PERMISSIONS_EXPORT,
    SMARTZAP_COURSE_PRESENTATION,
    SMARTZAP_ENROLLMENTS_EXPORT,
    SMARTZAP_USER_ACTIVITIES_EXPORT,
)
from reports.export import ExportCsvCreator, ExportXlsxCreator
from reports.export.info_sheet_service import InfoSheetService
from reports.export.konquest.contents import KonquestContentExport
from reports.export.konquest.groups_channels_users import KonquestGroupsChannelsUsersExport
from reports.export.konquest.groups_missions_users import KonquestGroupsMissionsUsersExport
from reports.export.konquest.learning_trail_completion_rate import KonquestTrailsCompletionRateExport
from reports.export.konquest.mission import KonquestMissionExport
from reports.export.konquest.mission_contents_activities import KonquestMissionContentsActivitiesExport
from reports.export.konquest.mission_enrollment import KonquestMissionEnrollmentExport
from reports.export.konquest.mission_evaluations_statistics import KonquestMissionEvaluationsStatisticsExport
from reports.export.konquest.missions_evaluations import KonquestMissionsEvaluationsExport
from reports.export.konquest.missions_quizzes_answers import KonquestMissionsQuizzesAnswers
from reports.export.konquest.pulse_activities import KonquestPulseActivityExport
from reports.export.konquest.pulses_channels import KonquestPulsesChannelsExport
from reports.export.konquest.pulses_quizzes_answers import KonquestPulsesQuizzesAnswers
from reports.export.konquest.trail import KonquestTrailExport
from reports.export.konquest.trail_enrollment import KonquestTrailEnrollmentExport
from reports.export.konquest.user_consumption import KonquestUserConsumptionExport
from reports.export.konquest.users import KonquestUsersExport
from reports.export.konquest.users_access_by_date import KonquestUsersAccessByDateExport
from reports.export.konquest.users_general_statistics import KonquestUserGeneralStatisticsExport
from reports.export.main.generic_export_builder import GenericExportBuilder
from reports.filter.activities_learning_object_custom_filter import ActivitiesLearningObjectCustomFilter
from reports.filter.filter_sql import FilterSql
from reports.filter.mission_enrollment_active_filter import MissionEnrollmentActiveCustomFilter
from reports.filter.mission_enrollment_groups_custom_filter import MissionEnrollmentGroupCustomFilter
from reports.filter.mission_learning_trail_custom_filter import MissionLearningTrailCustomFilter
from reports.filter.trail_enrollment_active_filter import TrailEnrollmentActiveCustomFilter
from reports.filter.user_deleted_from_workspace_filter import (
    TrailEnrollmentUserDeletedFromWorkspaceCustomFilter,
    UserDeletedFromWorkspaceCustomFilter,
)
from reports.formatters.formatter_service import FormatterService
from reports.internal.konquest_companies_health_check import KonquestCompaniesHealthCheck
from reports.internal.query.billing_query import KonquestBillingQuery
from reports.internal.query.user_activities_by_month_query import UserActivitiesByMonthQuery
from reports.presentation import PresentationCreator
from reports.presentation.konquest.mission import MissionPresentation
from reports.presentation.konquest.mission_enrollment_quiz import MissionEnrollmentQuizPresentation
from reports.presentation.konquest.user import UserPresentation
from reports.presentation.konquest.workspace import WorkspacePresentation
from reports.presentation.smartzap.course import CoursePresentation
from reports.query.account.export import AccountExportQuery
from reports.query.account.query import AccountQuery
from reports.query.konquest.export import KonquestExportQuery
from reports.query.konquest.mission import KonquestMissionQuery
from reports.query.konquest.mission_enrollment_quiz import KonquestMissionEnrollmentQuizQuery
from reports.query.konquest.user import KonquestUserQuery
from reports.query.konquest.user_consumptions import KonquestUserConsumptionsQuery
from reports.query.konquest.workspace import KonquestWorkspaceQuery
from reports.query.kontent.query import KontentQuery
from reports.query.query_executor_with_config_injectable import QueryExecutorWithConfigInjectable
from reports.query.query_selector_config import QuerySelectorConfig
from reports.query.smartzap.course import SmartzapCourseQuery
from reports.services.jasper_report_service import JasperReportService
from reports.utils import ReportLambdaClient

QUERIES_FOLDER_KONQUEST_PDF = "konquest/pdf/queries/"
QUERIES_FOLDER_KONQUEST_EXPORT = "konquest/export/queries/"
TEMPLATE_FOLDER_SMARTZAP = "smartzap/smartzap_query/"
SQL_TEMPLATE_FOLDER_KONQUEST = "reports/query/konquest/sql_templates"
SQL_TEMPLATE_FOLDER_KONQUEST_EXPORT = "reports/query/konquest/sql_templates/filterable"
SQL_TEMPLATE_FOLDER_KONTENT = "reports/query/kontent/sql_templates"
SQL_TEMPLATE_FOLDER_ACCOUNT = "reports/query/account/sql_templates"
SQL_TEMPLATE_FOLDER_ACCOUNT_EXPORT = "reports/query/account/sql_templates/filterable"
SQL_TEMPLATE_FOLDER_SMARTZAP = "reports/query/smartzap/sql_templates"
SQL_TEMPLATE_FOLDER_SMARTZAP_FILTRABLES = "reports/query/smartzap/sql_templates/filterables"


class ReportContainer:
    presentation_generators = {}

    def __init__(self):
        self._filters = FilterSql(
            [
                MissionLearningTrailCustomFilter(),
                MissionEnrollmentGroupCustomFilter(),
                MissionEnrollmentActiveCustomFilter(),
                UserDeletedFromWorkspaceCustomFilter(),
                TrailEnrollmentUserDeletedFromWorkspaceCustomFilter(),
                TrailEnrollmentActiveCustomFilter(),
                ActivitiesLearningObjectCustomFilter()
            ]
        )
        self._webhook_logger = DiscordWebhookLogger()
        self._locales = Locales(Config.LOCALE_JSON)
        self._kontent_service = reports.services.KontentService(
            url=Config.ELASTICSEARCH_URL,
            auth=Config.ELASTICSEARCH_AUTH
        )
        self._report_lambda_client = reports.utils.ReportLambdaClient(
            Config.AWS_LAMBDA_ACCESS_KEY_ID,
            Config.AWS_LAMBDA_SECRET_ACCESS_KEY,
            Config.AWS_LAMBDA_REGION_NAME,
            f'{Config.BASE_DIR}/reports/temp'
        )
        self._jasper_report_service = JasperReportService(Config.JASPER_REPORT_SERVER_URL, f'{Config.BASE_DIR}/reports/temp')
        self._report_export = reports.utils.ReportExport(f'{Config.BASE_DIR}/reports/temp')
        self._report_uploader = reports.utils.ReportUploaderClient(
            Config.AWS_S3_ACCESS_KEY_ID,
            Config.AWS_S3_SECRET_ACCESS_KEY,
            Config.AWS_S3_REGION_NAME,
            Config.AWS_BUCKET_NAME_REPORT,
            Config.AWS_BASE_CDN_URL,
            Config.AWS_BUCKET_PATH
        )
        self._pdf_builder = reports.builder.PDFBuilder(
            self._report_lambda_client,
            self._report_uploader,
            f'{Config.BASE_DIR}/reports/temp'
        )
        self._export_builder = ExportBuilder(
            self._report_export,
            self._report_uploader,
            f'{Config.BASE_DIR}/reports/temp'
        )

        self._presentation_creator = PresentationCreator(
            self._jasper_report_service,
            self._report_uploader,
            self._locales,
            self._webhook_logger,
        )
        self._konquest_mission_query = KonquestMissionQuery(database_konquest.engine, SQL_TEMPLATE_FOLDER_KONQUEST)
        self._konquest_user_query = KonquestUserQuery(database_konquest.engine, SQL_TEMPLATE_FOLDER_KONQUEST)
        self._kontent_query = KontentQuery(database_kontent.engine, SQL_TEMPLATE_FOLDER_KONTENT)
        self._account_query = AccountQuery(database_myaccount.engine, SQL_TEMPLATE_FOLDER_ACCOUNT)
        self._account_export_query = AccountExportQuery(database_myaccount.engine, SQL_TEMPLATE_FOLDER_ACCOUNT_EXPORT)
        self._konquest_workspace_query = KonquestWorkspaceQuery(database_konquest.engine, SQL_TEMPLATE_FOLDER_KONQUEST)
        self._konquest_mission_enrollment_quiz_query = KonquestMissionEnrollmentQuizQuery(
            database_konquest.engine, self._account_export_query, SQL_TEMPLATE_FOLDER_KONQUEST, self._filters
        )
        self._smartzap_course_query = SmartzapCourseQuery(database_smartzap.engine, SQL_TEMPLATE_FOLDER_SMARTZAP)

        self._info_sheet_service = InfoSheetService()
        self._export_xlsx_creator = ExportXlsxCreator(
            self._report_uploader,
            self._locales,
            self._webhook_logger,
            self._info_sheet_service,
            Config.MAX_COLUMN_WIDTH
        )
        self._export_csv_creator = ExportCsvCreator(self._report_uploader, self._locales)
        self._export_query = KonquestExportQuery(
            database_konquest.engine,
            self._account_export_query,
            SQL_TEMPLATE_FOLDER_KONQUEST_EXPORT,
            self._filters
        )
        self._smartzap_user_activities_query = QueryExecutorWithConfigInjectable(QuerySelectorConfig(
            database_engine=database_smartzap.engine,
            template_folder=SQL_TEMPLATE_FOLDER_SMARTZAP_FILTRABLES,
            filter_sql=self._filters,
            table_name="activity",
            foreign_tables_names=("user", "content", "enrollment",),
            query_name="list_workspace_user_activities",
            keys_filters_dates=(("created_at", "updated_at"),)
        ))
        self._smartzap_enrollments_query = QueryExecutorWithConfigInjectable(QuerySelectorConfig(
            database_engine=database_smartzap.engine,
            template_folder=SQL_TEMPLATE_FOLDER_SMARTZAP_FILTRABLES,
            filter_sql=self._filters,
            table_name="enrollment",
            foreign_tables_names=("user", "lesson", "content",),
            query_name="list_workspace_enrollments",
            keys_filters_dates=(("created_at", "updated_at"),)
        ))
        self._user_consumption_query = KonquestUserConsumptionsQuery(
            database_konquest.engine,
            self._account_export_query,
            SQL_TEMPLATE_FOLDER_KONQUEST_EXPORT
        )
        self._myaccount_user_permissions_query = QueryExecutorWithConfigInjectable(QuerySelectorConfig(
            database_engine=database_myaccount.engine,
            template_folder=SQL_TEMPLATE_FOLDER_ACCOUNT_EXPORT,
            filter_sql=self._filters,
            table_name="user",
            foreign_tables_names=("user_profile_workspace",),
            query_name="list_workspace_user_permissions",
            keys_filters_dates=(("created_date", "updated_date"),)
        ))
        self._formatter_service = FormatterService()

        self._elasticsearch_connection = self._create_elasticsearch_connection()
        self._elasticsearch_factory = self._create_elasticsearch_factory()
        self._user_service = self._create_user_service()

    default_config_map = {
        "today_date": reports.utils.Utils.format_date_to_string(dt.datetime.now()),
        "seven_days_ago": dt.datetime.now() - dt.timedelta(days=7),
        "thirty_days_ago": dt.datetime.now() - dt.timedelta(days=30),
        "week_before": dt.datetime.now() - dt.timedelta(days=7) - dt.timedelta(days=7),
        "month_before": dt.datetime.now() - dt.timedelta(days=30) - dt.timedelta(days=30),
        "default_avatar": 'https://s3.amazonaws.com/keeps.reports/assets/mascote_icon.png',
        "pdf_content_id": '0faac34b-2393-4352-8a94-a9ee0659f824',
        "video_content_id": '569cc389-ac1d-4fa0-9692-f715b475b59b',
        "spreadsheet_content_id": '673e4c02-ae1c-4e61-830b-706d35bd0b11',
        "podcast_content_id": '799f766c-a956-4c03-b5aa-bde9ba357de8',
        "question_content_id": '7a41a8e0-ee37-4d0b-ad4f-35bada67134d',
        "presentation_content_id": '7ee375e4-b781-46e6-b0de-0323ebb94b96',
        "text_content_id": 'b7094e27-b263-4fed-a928-6f0a78439cbe',
        "image_content_id": '2284bfce-fdfc-4477-9143-39c380cc653c'
    }

    def _create_elasticsearch_connection(self) -> ElasticsearchConnection:
        conn = Elasticsearch(Config.ELASTICSEARCH_URL, http_auth=Config.ELASTICSEARCH_AUTH)
        connections.create_connection(
            hosts=[Config.ELASTICSEARCH_URL],
            timeout=20,
            http_auth=Config.ELASTICSEARCH_AUTH
        )

        return conn

    def _create_elasticsearch_factory(self) -> ElasticSearchFactory:
        return ElasticSearchFactory(
            logger=logging,
            es=self._elasticsearch_connection,
        )

    def _create_user_service(self) -> UserServiceV2:
        return UserServiceV2(
            logger=logging,
            esf=self._elasticsearch_factory,
        )

    def report_lambda_client(self) -> ReportLambdaClient:
        return self._report_lambda_client

    def presentation_creator(self) -> PresentationCreator:
        return self._presentation_creator

    def konquest_mission_presentation(self) -> MissionPresentation:
        presentation_creator = PresentationCreator(
            self._jasper_report_service, self._report_uploader, self._locales, self._webhook_logger
        )
        return MissionPresentation(presentation_creator, self._konquest_mission_query, self._kontent_query, KONQUEST_MISSION_PRESENTATION)

    def smartzap_course_presentation(self) -> CoursePresentation:
        presentation_creator = PresentationCreator(
            self._jasper_report_service, self._report_uploader, self._locales, self._webhook_logger
        )
        return CoursePresentation(
            presentation_creator,
            self._smartzap_course_query,
            self._account_query,
            self._kontent_query,
            SMARTZAP_COURSE_PRESENTATION
        )

    def konquest_workspace_presentation(self) -> WorkspacePresentation:
        presentation_creator = PresentationCreator(
            self._jasper_report_service, self._report_uploader, self._locales, self._webhook_logger
        )
        return WorkspacePresentation(
            presentation_creator,
            self._konquest_workspace_query,
            self._account_query,
            self._kontent_query,
            KONQUEST_WORKSPACE_PRESENTATION
        )

    def konquest_user_presentation(self) -> UserPresentation:
        presentation_creator = PresentationCreator(
            self._jasper_report_service, self._report_uploader, self._locales, self._webhook_logger
        )
        return UserPresentation(
            presentation_creator,
            self._konquest_user_query,
            self._konquest_workspace_query,
            self._account_query,
            self._locales,
            KONQUEST_USER_PRESENTATION
        )

    def konquest_mission_enrollment_quiz_presentation(self) -> MissionEnrollmentQuizPresentation:
        presentation_creator = PresentationCreator(
            self._jasper_report_service, self._report_uploader, self._locales, self._webhook_logger
        )
        return MissionEnrollmentQuizPresentation(
            presentation_creator,
            self._konquest_mission_enrollment_quiz_query,
            self._locales,
            KONQUEST_MISSION_ENROLLMENT_QUIZZES_PRESENTATION
        )

    def konquest_mission_export(self) -> KonquestMissionExport:
        export_csv_creator, export_xlsx_creator = self.get_export_creators()
        return KonquestMissionExport(
            export_xlsx_creator, export_csv_creator, self._export_query, self._formatter_service, KONQUEST_MISSIONS_EXPORT
        )

    def konquest_mission_enrollments_export(self) -> KonquestMissionEnrollmentExport:
        export_csv_creator, export_xlsx_creator = self.get_export_creators()
        return KonquestMissionEnrollmentExport(
            export_xlsx_creator,
            export_csv_creator,
            self._export_query,
            self._account_export_query,
            self._formatter_service,
            KONQUEST_MISSIONS_ENROLLMENTS_EXPORT
        )

    def konquest_groups_missions_users(self) -> KonquestGroupsMissionsUsersExport:
        export_csv_creator, export_xlsx_creator = self.get_export_creators()
        return KonquestGroupsMissionsUsersExport(
            export_xlsx_creator,
            export_csv_creator,
            self._export_query,
            self._formatter_service,
            KONQUEST_GROUPS_MISSIONS_USERS_EXPORT
        )

    def konquest_pulse_activities_export(self) -> KonquestPulseActivityExport:
        export_csv_creator, export_xlsx_creator = self.get_export_creators()
        return KonquestPulseActivityExport(
            export_xlsx_creator, export_csv_creator, self._export_query, self._formatter_service, KONQUEST_USERS_PULSES_ACTIVITIES_EXPORT
        )

    def konquest_content_export(self) -> KonquestContentExport:
        export_csv_creator, export_xlsx_creator = self.get_export_creators()
        return KonquestContentExport(
            export_xlsx_creator, export_csv_creator, self._export_query, self._kontent_query, self._formatter_service, KONQUEST_CONTENTS_EXPORT
        )

    def konquest_groups_channels_users_export(self) -> KonquestGroupsChannelsUsersExport:
        export_csv_creator, export_xlsx_creator = self.get_export_creators()
        return KonquestGroupsChannelsUsersExport(
            export_xlsx_creator, export_csv_creator, self._export_query, self._formatter_service, KONQUEST_GROUPS_CHANNELS_USERS_EXPORT
        )

    def konquest_users_export(self) -> KonquestUsersExport:
        export_csv_creator, export_xlsx_creator = self.get_export_creators()
        return KonquestUsersExport(
            export_xlsx_creator,
            export_csv_creator,
            self._export_query,
            self._formatter_service,
            Config.MAX_DAYS_OF_USER_INACTIVITY,
            KONQUEST_USERS_EXPORT
        )

    def konquest_missions_quizzes_answers_export(self) -> KonquestMissionsQuizzesAnswers:
        export_csv_creator, export_xlsx_creator = self.get_export_creators()
        return KonquestMissionsQuizzesAnswers(
            export_xlsx_creator,
            export_csv_creator,
            self._export_query,
            self._formatter_service,
            KONQUEST_MISSIONS_QUIZZES_ANSWERS_EXPORT
        )

    def konquest_pulses_quizzes_answers_export(self) -> KonquestPulsesQuizzesAnswers:
        export_csv_creator, export_xlsx_creator = self.get_export_creators()
        return KonquestPulsesQuizzesAnswers(
            export_xlsx_creator, export_csv_creator, self._export_query, self._formatter_service, KONQUEST_PULSES_QUIZZES_ANSWERS_EXPORT
        )

    def konquest_mission_contents_activities_export(self) -> KonquestMissionContentsActivitiesExport:
        export_csv_creator, export_xlsx_creator = self.get_export_creators()
        return KonquestMissionContentsActivitiesExport(
            export_xlsx_creator,
            export_csv_creator,
            self._export_query,
            self._kontent_service,
            self._formatter_service,
            KONQUEST_MISSION_CONTENTS_ACTIVITIES_EXPORT
        )

    def konquest_pulses_channels_export(self) -> KonquestPulsesChannelsExport:
        export_csv_creator, export_xlsx_creator = self.get_export_creators()
        return KonquestPulsesChannelsExport(
            export_xlsx_creator,
            export_csv_creator,
            self._export_query,
            self._formatter_service,
            KONQUEST_PULSES_CHANNELS_EXPORT
        )

    def konquest_users_access_by_date_export(self) -> KonquestUsersAccessByDateExport:
        export_csv_creator, export_xlsx_creator = self.get_export_creators()
        return KonquestUsersAccessByDateExport(
            export_xlsx_creator,
            export_csv_creator,
            self._export_query,
            self._formatter_service,
            KONQUEST_USERS_ACCESS_BY_DATE_EXPORT
        )

    def konquest_missions_evaluations_export(self) -> KonquestMissionsEvaluationsExport:
        export_csv_creator, export_xlsx_creator = self.get_export_creators()
        return KonquestMissionsEvaluationsExport(
            export_xlsx_creator, export_csv_creator, self._export_query, self._formatter_service, KONQUEST_MISSIONS_EVALUATIONS_EXPORT
        )

    def konquest_user_consumption_export(self) -> KonquestUserConsumptionExport:
        export_csv_creator, export_xlsx_creator = self.get_export_creators()
        return KonquestUserConsumptionExport(
            export_xlsx_creator, export_csv_creator, self._user_consumption_query, self._formatter_service, KONQUEST_USER_CONSUMPTIONS_EXPORT
        )

    def konquest_user_general_statistics_export(self) -> KonquestUserGeneralStatisticsExport:
        export_csv_creator, export_xlsx_creator = self.get_export_creators()
        return KonquestUserGeneralStatisticsExport(
            export_xlsx_creator,
            export_csv_creator,
            self._export_query,
            self._formatter_service,
            KONQUEST_USER_GENERAL_STATISTICS_EXPORT
        )

    def konquest_mission_evaluations_statistics_export(self) -> KonquestMissionEvaluationsStatisticsExport:
        export_csv_creator, export_xlsx_creator = self.get_export_creators()
        return KonquestMissionEvaluationsStatisticsExport(
            export_xlsx_creator,
            export_csv_creator,
            self._export_query,
            self._formatter_service,
            KONQUEST_MISSION_EVALUATIONS_STATISTICS_EXPORT
        )

    def smartzap_user_activities_export(self) -> GenericExportBuilder:
        export_csv_creator, export_xlsx_creator = self.get_export_creators()
        return GenericExportBuilder(
            export_xlsx_creator,
            export_csv_creator,
            self._smartzap_user_activities_query,
            self._formatter_service,
            SMARTZAP_USER_ACTIVITIES_EXPORT
        )

    def smartzap_enrollments_export(self) -> GenericExportBuilder:
        export_csv_creator, export_xlsx_creator = self.get_export_creators()
        return GenericExportBuilder(
            export_xlsx_creator,
            export_csv_creator,
            self._smartzap_enrollments_query,
            self._formatter_service,
            SMARTZAP_ENROLLMENTS_EXPORT
        )

    def konquest_trail_enrollments_export(self) -> KonquestTrailEnrollmentExport:
        export_csv_creator, export_xlsx_creator = self.get_export_creators()
        return KonquestTrailEnrollmentExport(
            export_xlsx_creator,
            export_csv_creator,
            self._export_query,
            self._formatter_service,
            self._account_export_query,
            KONQUEST_TRAILS_ENROLLMENTS_EXPORT
        )

    def konquest_trail_export(self) -> KonquestTrailExport:
        export_csv_creator, export_xlsx_creator = self.get_export_creators()
        return KonquestTrailExport(
            export_xlsx_creator, export_csv_creator, self._export_query, self._formatter_service, KONQUEST_TRAILS_EXPORT
        )

    def konquest_trails_completion_rate_export(self) -> KonquestTrailsCompletionRateExport:
        export_csv_creator, export_xlsx_creator = self.get_export_creators()
        return KonquestTrailsCompletionRateExport(
            export_xlsx_creator, export_csv_creator, self._export_query, self._formatter_service, KONQUEST_TRAILS_COMPLETION_RATE_EXPORT
        )

    def myaccount_user_permissions_export(self) -> GenericExportBuilder:
        export_csv_creator, export_xlsx_creator = self.get_export_creators()
        return GenericExportBuilder(
            export_xlsx_creator,
            export_csv_creator,
            self._myaccount_user_permissions_query,
            self._formatter_service,
            MYACCOUNT_USER_PERMISSIONS_EXPORT
        )

    def users_activities_by_month_query(self) -> UserActivitiesByMonthQuery:
        return UserActivitiesByMonthQuery(self._user_service)

    def billing_query(self) -> KonquestBillingQuery:
        return KonquestBillingQuery(
            database_konquest.engine,
            self._account_export_query,
            self.users_activities_by_month_query(),
            MYACCOUNT_USER_PERMISSIONS_EXPORT
        )

    def konquest_companies_health_check(self) -> KonquestCompaniesHealthCheck:
        export_csv_creator, export_xlsx_creator = self.get_export_creators()
        return KonquestCompaniesHealthCheck(
            export_xlsx_creator,
            export_csv_creator,
            self.billing_query(),
            self._formatter_service,
            KONQUEST_COMPANIES_HEALTH_CHECK,
        )

    def get_export_creators(self):
        export_xlsx_creator = ExportXlsxCreator(self._report_uploader, self._locales, self._webhook_logger, self._info_sheet_service)
        export_csv_creator = ExportCsvCreator(self._report_uploader, self._locales)
        return export_csv_creator, export_xlsx_creator

    def report_export(self):
        return self._report_export

    def report_uploader(self):
        return self._report_uploader

    def pdf_builder(self):
        return self._pdf_builder

    def export_builder(self):
        return self._export_builder

    def konquest_query(self):
        return self.konquest_query

    def export_xlsx_creator(self) -> ExportXlsxCreator:
        return self._export_xlsx_creator

    def export_csv_creator(self) -> ExportCsvCreator:
        return self._export_csv_creator

    def konquest_export_query(self) -> KonquestExportQuery:
        return self._export_query

    def formatter_service(self) -> FormatterService:
        return self._formatter_service

    def configure(self, binder):
        binder.bind(reports.utils.ReportLambdaClient, to=injector.CallableProvider(self._report_lambda_client))
        binder.bind(reports.utils.ReportUploaderClient, to=injector.CallableProvider(self._report_uploader))
        binder.bind(reports.builder.PDFBuilder, to=injector.CallableProvider(self._pdf_builder))
        binder.bind(reports.builder.ExportBuilder, to=injector.CallableProvider(self._export_builder))
        binder.bind(MissionPresentation, to=injector.CallableProvider(self.konquest_mission_presentation))
        binder.bind(WorkspacePresentation, to=injector.CallableProvider(self.konquest_workspace_presentation))
        binder.bind(UserPresentation, to=injector.CallableProvider(self.konquest_user_presentation))
        binder.bind(CoursePresentation, to=injector.CallableProvider(self.smartzap_course_presentation))
        binder.bind(KonquestMissionExport, to=injector.CallableProvider(self.konquest_mission_export))
        binder.bind(KonquestContentExport, to=injector.CallableProvider(self.konquest_content_export))
        binder.bind(KonquestGroupsMissionsUsersExport, to=injector.CallableProvider(self.konquest_groups_missions_users))
        binder.bind(KonquestGroupsChannelsUsersExport, to=injector.CallableProvider(self.konquest_groups_channels_users_export))
        binder.bind(KonquestUsersExport, to=injector.CallableProvider(self.konquest_users_export))
        binder.bind(KonquestMissionsQuizzesAnswers, to=injector.CallableProvider(self.konquest_missions_quizzes_answers_export))
        binder.bind(KonquestPulsesQuizzesAnswers, to=injector.CallableProvider(self.konquest_pulses_quizzes_answers_export))
        binder.bind(KonquestMissionContentsActivitiesExport, to=injector.CallableProvider(self.konquest_mission_contents_activities_export))
        binder.bind(KonquestPulsesChannelsExport, to=injector.CallableProvider(self.konquest_pulses_channels_export))
        binder.bind(KonquestUsersAccessByDateExport, to=injector.CallableProvider(self.konquest_users_access_by_date_export))
        binder.bind(KonquestMissionsEvaluationsExport, to=injector.CallableProvider(self.konquest_missions_evaluations_export))
        binder.bind(KonquestUserConsumptionsQuery, to=injector.CallableProvider(self.konquest_user_consumption_export))

    def get_report_services_map(self):
        return {
            KONQUEST_MISSION_PRESENTATION: self.konquest_mission_presentation,
            KONQUEST_WORKSPACE_PRESENTATION: self.konquest_workspace_presentation,
            KONQUEST_USER_PRESENTATION: self.konquest_user_presentation,
            SMARTZAP_COURSE_PRESENTATION: self.smartzap_course_presentation,
            KONQUEST_CONTENTS_EXPORT: self.konquest_content_export,
            KONQUEST_USERS_PULSES_ACTIVITIES_EXPORT: self.konquest_pulse_activities_export,
            KONQUEST_MISSIONS_ENROLLMENTS_EXPORT: self.konquest_mission_enrollments_export,
            KONQUEST_GROUPS_MISSIONS_USERS_EXPORT: self.konquest_groups_missions_users,
            KONQUEST_MISSIONS_EXPORT: self.konquest_mission_export,
            KONQUEST_GROUPS_CHANNELS_USERS_EXPORT: self.konquest_groups_channels_users_export,
            KONQUEST_USERS_EXPORT: self.konquest_users_export,
            KONQUEST_MISSIONS_QUIZZES_ANSWERS_EXPORT: self.konquest_missions_quizzes_answers_export,
            KONQUEST_PULSES_QUIZZES_ANSWERS_EXPORT: self.konquest_pulses_quizzes_answers_export,
            KONQUEST_MISSION_CONTENTS_ACTIVITIES_EXPORT: self.konquest_mission_contents_activities_export,
            KONQUEST_PULSES_CHANNELS_EXPORT: self.konquest_pulses_channels_export,
            KONQUEST_USERS_ACCESS_BY_DATE_EXPORT: self.konquest_users_access_by_date_export,
            KONQUEST_MISSIONS_EVALUATIONS_EXPORT: self.konquest_missions_evaluations_export,
            KONQUEST_USER_CONSUMPTIONS_EXPORT: self.konquest_user_consumption_export,
            KONQUEST_USER_GENERAL_STATISTICS_EXPORT: self.konquest_user_general_statistics_export,
            KONQUEST_MISSION_EVALUATIONS_STATISTICS_EXPORT: self.konquest_mission_evaluations_statistics_export,
            KONQUEST_TRAILS_ENROLLMENTS_EXPORT: self.konquest_trail_enrollments_export,
            SMARTZAP_ENROLLMENTS_EXPORT: self.smartzap_enrollments_export,
            SMARTZAP_USER_ACTIVITIES_EXPORT: self.smartzap_user_activities_export,
            KONQUEST_MISSION_ENROLLMENT_QUIZZES_PRESENTATION: self.konquest_mission_enrollment_quiz_presentation,
            KONQUEST_TRAILS_EXPORT: self.konquest_trail_export,
            KONQUEST_TRAILS_COMPLETION_RATE_EXPORT: self.konquest_trails_completion_rate_export,
            MYACCOUNT_USER_PERMISSIONS_EXPORT: self.myaccount_user_permissions_export,
            KONQUEST_COMPANIES_HEALTH_CHECK: self.konquest_companies_health_check
        }
