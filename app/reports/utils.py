import base64
import binascii
import json
import math
import os
import re
import uuid
from datetime import date, datetime
from time import gmtime, strftime
from typing import Optional, Sequence, Set, Union

import boto3
import pandas as pd
from boto3.s3.transfer import TransferConfig
from pandas import DataFrame, ExcelWriter, Timedelta
from reports.abstracts.report_uploader_client_abc import ReportUploaderClientABC
from unidecode import unidecode


class ReportUploaderClient(ReportUploaderClientABC):
    """
    access_key: AWS access key
    secret_key: AWS secret key
    region_name: Region of Bucket
    bucket: Name of bucket to upload file
    aws_default_url: URL default to return the right link to access file uploaded.
    """

    def __init__(self, access_key, secret_key, region_name, bucket, aws_default_url, bucket_path):
        self.url = aws_default_url
        self.bucket = bucket
        self.bucket_path = bucket_path
        self.client = boto3.client('s3',
                                   aws_access_key_id=access_key,
                                   aws_secret_access_key=secret_key,
                                   region_name=region_name)
        self.config = TransferConfig(
            multipart_threshold=64 * 1024 * 1024,
            max_concurrency=10,
            num_download_attempts=10,
            multipart_chunksize=16 * 1024 * 1024,
            max_io_queue=10000
        )
        self.file_type = {
            "pdf": "application/pdf",
            "xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "zip": "application/zip"
        }

    def upload_report(self, file_path, type_name, client='default', remove_after_upload=True):
        """
        Upload file to AWS S3

        :param remove_after_upload: If user want delete ou keep file after uploaded.
        :param type_name: can be pdf or xlsx or zip.
        :param file_path: where file to upload is located
        :param client: account or company identity (uuid or name).

        :return: {name: string, url: string}

        """
        filename = f'{self.bucket_path}/reports/{client}/{unidecode(file_path.split("/")[-1]).replace(" ", "_")}'

        content_type = self.file_type[type_name]

        self.client.upload_file(file_path,
                                self.bucket,
                                filename,
                                Config=self.config,
                                ExtraArgs={'ACL': 'public-read', 'ContentType': content_type})

        response = {
            'name': filename,
            'url': '{}/{}'.format(self.url, filename)
        }

        if remove_after_upload:
            os.remove(file_path)

        return response


class ReportLambdaClient:
    """
    Client in JAVA to deploy in AWS lambda that generate jasper reports in PDF.

    These service receive a path to S3 where is located the jasper file (.jasper) and dataset/params
    to generate reports and return a PDF file in base64.

    Project available: https://gitlab.com/learn-platform/learn-analytics/report-generator
    """

    def __init__(self, access_key, secret_key, region_name, temp_dir):
        self.client = boto3.client('lambda',
                                   aws_access_key_id=access_key,
                                   aws_secret_access_key=secret_key,
                                   region_name=region_name)

        self.temp_dir = temp_dir
        self.report_jasper_map = {
            "smartzap_cover": "smartzap/cover_course.jasper",
            "smartzap_enrollment": "smartzap/enrollments_status.jasper",
            "smartzap_performance": "smartzap/lateral_performance_table.jasper",
            "smartzap_consume_content": "smartzap/lateral_consume_users_table.jasper",
            "smartzap_consume_quiz": "smartzap/lateral_consume_quiz_users_table.jasper",
            "smartzap_ranking_users_performance": "smartzap/lateral_ranking_users_performance.jasper",
            "smartzap_ranking_users_quiz_performance": "smartzap/lateral_ranking_users_quiz_performance.jasper",
            "smartzap_thanks": "smartzap/thank_report.jasper",
            "konquest_overview_cover": "konquest/overview/cover_company.jasper",
            "konquest_overview_general_information": "konquest/overview/general_information.jasper",
            "konquest_overview_users_records": "konquest/overview/users_records.jasper",
            "konquest_overview_enrollments_and_users": "konquest/overview/enrollments_and_users.jasper",
            "konquest_overview_enrollments_performance_analysis": "konquest/overview/enrollments_performance_analysis.jasper",
            "konquest_overview_contents_type_in_missions": "konquest/overview/contents_type_in_missions.jasper",
            "konquest_overview_pulses_analysis": "konquest/overview/pulses_analysis.jasper",
            "konquest_overview_consume_pulses_analysis": "konquest/overview/consume_pulses_analysis.jasper",
            "konquest_overview_contents_type_in_pulses": "konquest/overview/contents_type_in_pulses.jasper",
            "konquest_overview_ranking_user_performance": "konquest/overview/ranking_user_performance.jasper",
            "konquest_overview_groups_by_mission_general": "konquest/overview/groups_by_mission_general.jasper",
            "konquest_overview_groups_by_mission_detailed": "konquest/overview/groups_by_mission_detailed.jasper",
            "konquest_overview_thank_report": "konquest/overview/thank_report.jasper",
            "konquest_mission_cover": "konquest/mission_resume/cover_mission.jasper",
            "konquest_mission_general_information": "konquest/mission_resume/general_information.jasper",
            "konquest_mission_enrollments_analysis": "konquest/mission_resume/enrollments_analysis.jasper",
            "konquest_mission_enrollments_performance_analysis": "konquest/mission_resume/enrollments_performance_analysis.jasper",
            "konquest_mission_quizzes_performance_analysis": "konquest/mission_resume/enrollments_quizzes_performance_analysis.jasper",
            "konquest_mission_quizzes_analysis": "konquest/mission_resume/quizzes_analysis.jasper",
            "konquest_mission_ranking_user_performance": "konquest/mission_resume/ranking_user_performance.jasper",
            "konquest_mission_ranking_user_quizzes_performance": "konquest/mission_resume/ranking_user_quizzes_performance.jasper",
            "konquest_mission_groups_by_mission": "konquest/mission_resume/groups_by_mission.jasper",
            "konquest_user_mission": "konquest/user_overview/main_mission.jasper",
            "subreport_konquest_user_mission": "konquest/user_overview/subreport_mission_v2.jasper",
            "konquest_user_pulse": "konquest/user_overview/main_pulse.jasper",
            "subreport_konquest_user_pulse": "konquest/user_overview/subreport_pulse.jasper",
            "konquest_user_group": "konquest/user_overview/main_groups.jasper",
            "subreport_konquest_user_group": "konquest/user_overview/subreport_group.jasper",
            "thank_report": "generic/thank_report.jasper",
            "error_report": "generic/error_report.jasper",
            "konquest_mission_enrollment_quiz_first_page": "konquest/quiz_by_mission/firstPage.jasper",
            "konquest_mission_enrollment_quiz_subreport_quiz_question": "konquest/quiz_by_mission/subreportQuizQuestion.jasper",
            "konquest_mission_enrollment_quiz_subreport_quiz_question_option": "konquest/quiz_by_mission/subreportQuizQuestionOption.jasper",
            "user_overview_first_page": "konquest/user_overview_v2/firstPage.jasper",
            "user_overview_second_page": "konquest/user_overview_v2/secondPage.jasper",
            "test_jasper_template_not_found": "this page does not exist"
        }

    def generate_error_page(self, jasper_template: str, error: str) -> str:
        page_error = {"template": "error_report",
                      "dataset": {'report_name': jasper_template,
                                  'report_error': "Error: {0}".format(error)},
                      "params": {}}
        page_result = self.json_to_pdf(jasper_template=page_error['template'], dataset=page_error['dataset'],
                                       params=page_error['params'])

        return page_result

    def json_to_pdf(
        self,
        jasper_template: str,
        jasper_sub_report_template: str = None,
        dataset=None,
        params: dict = None,
        report_name: str = None
    ) -> str:
        report_name = report_name if report_name else str(uuid.uuid4())
        file_path = f'{self.temp_dir}/{report_name}.pdf'
        jasper_file_url = self.report_jasper_map[jasper_template]
        jasper_sub_file_url = self.report_jasper_map[jasper_sub_report_template] if jasper_sub_report_template else None

        response = self.client.invoke(
            FunctionName='json-pdf-report',
            InvocationType='RequestResponse',
            Payload=json.dumps(
                {
                    "jasper_file_url": jasper_file_url,
                    "jasper_sub_file_url": jasper_sub_file_url,
                    "dataset": dataset,
                    "params": params if params else {}
                },
                default=str
            )
        )

        data = response['Payload'].read().decode("utf-8")
        if not data or "error" in data:
            raise binascii.Error
        with open(file_path, "wb") as file:
            file.write(base64.b64decode(data))

        return file_path


class ReportExport:
    def __init__(self, temp_dir):
        self.temp_dir = temp_dir

    def data_frame_to_xlsx(self, data_frame, report_name=None):
        if report_name is None:
            report_name = str(uuid.uuid4())

        file = f'{self.temp_dir}/{report_name}.xlsx'

        with ExcelWriter(file) as writer:
            data_frame.to_excel(writer, index=False, sheet_name='raw')

        return file

    def list_to_csv(self, list_data, report_name=None):
        """
        :param list_data: List of objects to generate csv
        :param report_name: Nome to write the file
        :return: ('file_path'): name the file and your directory
        """

        if report_name is None:
            report_name = str(uuid.uuid4())

        file = None
        if list_data:
            file = f'{self.temp_dir}/{report_name}.csv'
            df = pd.DataFrame(list_data)
            df.to_csv(file, index=False)

        return file

    def list_to_xlsx(self, list_data, report_name=None):
        """
        :param list_data: List of objects to generate xlsx
        :param report_name: Nome to write the file
        :return: ('file_path'): name the file and your directory
        """

        if report_name is None:
            report_name = str(uuid.uuid4())

        file = f'{self.temp_dir}/{report_name}.xlsx'
        df = pd.DataFrame(list_data)

        with ExcelWriter(file) as writer:
            df.to_excel(writer, index=False, sheet_name='raw')

        return file

    def merge_csv(self, file_main, file_complementary, column_name, report_name=None):
        """
        :param file_main: str, principal file csv
        :param file_complementary: str, file csv to add
        :param column_name: Column name to join the files
        :param report_name: report name
        :return: ('file'): name the file and your directory
        """

        if report_name is None:
            report_name = str(uuid.uuid4())

        file = f'{self.temp_dir}/{report_name}.xlsx'
        if not os.path.getsize(file_complementary) == 1:
            file_a = pd.read_csv(file_main)
            file_b = pd.read_csv(file_complementary)
            merged = file_a.merge(file_b, on=column_name, how='left')
            merged.to_excel(file, index=None, header=True, sheet_name='raw')

        else:
            read_file = pd.read_csv(file_main)
            read_file.to_excel(file, index=None, header=True, sheet_name='raw')

        return file


class Utils:

    def __init__(self, locale=None):
        self.locale = locale

    def list_result_proxy(self, result_proxy):
        d, a = {}, []
        for row in result_proxy:
            for column, value in row.items():
                column = self.translate(column)
                value = self.translate(value)
                d = {**d, **{column: value}}

            a.append(d)

        return a

    def translate(self, text):
        try:
            if text in self.locale.messages:
                return self.locale.get(text)

            return text
        except Exception:
            return text

    # Return the arrow image url depending on the float_value
    @staticmethod
    def get_arrow_image(float_value):
        if float_value > 0:
            return 'https://s3.amazonaws.com/keeps.reports/assets/icon_arrow_up.png'
        else:
            return 'https://s3.amazonaws.com/keeps.reports/assets/icon_arrow_down.png'

    @staticmethod
    def calc_the_rate(today_value, yesterday_value):
        """
        :param today_value: int
        :param yesterday_value: int

        :return: the value growth in float
        """
        if not yesterday_value:
            return 0
        difference_value = today_value - yesterday_value
        return round(difference_value / yesterday_value, 4)

    @staticmethod
    def format_to_percentage(float_value, round_dp):
        """
        :param float_value: float
        :param round_dp: int

        :return: the value growth in float
        """
        return "{:.0%}".format(round(float_value, round_dp))

    @staticmethod
    def format_to_hours(time_in_seconds):
        """
        param time_in_seconds: int

        :return: string format in 'HH:MM'
        """
        return strftime("%H:%M", gmtime(time_in_seconds)) if time_in_seconds else "--:--"

    @staticmethod
    def get_first_item(result_proxy):
        """
        :param result_proxy: ResultProxy

        :return: the first item of the rs
        """
        try:
            first_item = [item[0] for item in result_proxy].__getitem__(0)
        except Exception:
            first_item = ''

        return first_item

    @staticmethod
    def format_int_to_string(int_value):
        if int_value:
            return str(int_value)
        else:
            return "-"

    @staticmethod
    def format_date_to_string(date_time):
        if date_time:
            return date_time.strftime("%d/%m/%Y")
        else:
            return "--/--/--"

    @staticmethod
    def filter_list(list_to_filter, value, column):
        """
        Filter the list by a interval of values in a specific column

        :param list_to_filter: list
        :param value: any
        :param column: str or int, can be the name column or the index number

        :return: list filtered
        """
        return list(filter(lambda item: str(item[column]) == str(value), list_to_filter))

    @staticmethod
    def filter_list_interval(list_to_filter, minimum, maximum, column=None):
        """
        Filter the list by a interval of values in a specific column

        :param list_to_filter: list
        :param minimum: float, minimum interval value
        :param maximum: float, maximum interval value
        :param column: str or int, can be the name column or the index number

        :return: list filtered
        """
        if column:
            return list(filter(lambda item: minimum <= item[column] < maximum, list_to_filter))
        else:
            return list(filter(lambda item: minimum <= item < maximum, list_to_filter))

    @staticmethod
    def check_empty_number(number):
        """
        Check if a number is a number

        :param number: int or float

        :return: int, the number or 0
        """
        if number is not None:
            return float(number)
        else:
            return 0

    @staticmethod
    def format_list_to_string(list_data):
        return str(list_data).replace('[', '').replace(']', '')

    @staticmethod
    def is_valid_uuid(uuid_to_test, version=4):
        """
        Check if uuid_to_test is a valid UUID.

        :param uuid_to_test : str
        :param version : {1, 2, 3, 4}

        :return: `True` if uuid_to_test is a valid UUID, otherwise `False`.
        """

        try:
            uuid_obj = uuid.UUID(uuid_to_test, version=version)
        except ValueError:
            return False

        return str(uuid_obj) == uuid_to_test

    @staticmethod
    def stop_watch(value, time_name):
        """Use for format the seconds to minutes:seconds"""

        value_d = (((value / 365) / 24) / 60)
        days = int(value_d)

        value_h = (value_d - days) * 365
        hours = int(value_h)

        value_m = (value_h - hours) * 24
        minutes = int(value_m)

        value_s = (value_m - minutes) * 60
        seconds = value_s

        return time_name, "=", minutes, "M:", seconds, "S"

    @staticmethod
    def drop_empty_row(data_frame):

        data_frame.dropna(
            axis=0,
            how='all',
            thresh=None,
            subset=None,
            inplace=True
        )

        return data_frame

    @staticmethod
    def seconds_to_time(seconds):
        if math.isnan(seconds):
            return "0:00"

        minutes, seconds = divmod(seconds, 60)
        hours, minutes = divmod(minutes, 60)
        return "%d:%d" % (hours, minutes) if minutes >= 10 else "%d:0%d" % (hours, minutes)


def format_datetime_to_string(date_time: datetime, language: str = "pt-br") -> str:
    format_map = {
        "pt-br": "%d/%m/%Y",
        "en": "%m/%d/%y",
        "es": "%d/%m/%Y"
    }
    date_format = format_map.get(language)
    if not date_format:
        raise ValueError("invalid language")

    return date_time.strftime(date_format) if type(date_time) in [datetime, date, pd.core.indexes.datetimes.DatetimeIndex] else None


def format_seconds_to_HM(seconds: int) -> str:
    if not seconds or math.isnan(seconds):
        return '00m'
    hours, remainder = divmod(seconds, 3600)
    minutes, _ = divmod(remainder, 60)
    if hours == 0:
        return '{:02d}m'.format(int(minutes))
    return '{:02d}h'.format(int(hours))


def calc_rate(dividend: Union[int, float], divider: Union[int, float]) -> str:
    try:
        return "{:.0%}".format(dividend / divider)
    except (TypeError, ZeroDivisionError):
        return "0%"


def calc_float_rate(dividend: Union[int, float], divider: Union[int, float]) -> float:
    try:
        return dividend / divider
    except (TypeError, ZeroDivisionError):
        return 0


def float_to_percentage(number: float) -> str:
    if math.isnan(number):
        return '0%'
    return "{:.0%}".format(number) if number else '0%'


def convert_timedelta_to_str(interval: Timedelta) -> Optional[str]:
    total_seconds = interval.total_seconds()
    if not total_seconds or math.isnan(total_seconds):
        return
    hours, remainder = divmod(total_seconds, 3600)
    minutes, seconds = divmod(remainder, 60)
    return '{:02d}:{:02d}:{:02d}'.format(int(hours), int(minutes), int(seconds))


def filter_columns(users: DataFrame, column_to_filter: Sequence[str]):
    columns_to_drop = list(filter(lambda column: column not in column_to_filter, users.columns))
    return users.drop(columns=columns_to_drop)


def calc_days_difference(start_date: date, end_date: date):
    return (start_date - end_date).days


def list_to_sql_repr(emails: Union[Sequence[str], Set[str]],) -> str:
    return "','".join(str(value) for value in emails)


def remove_html_tags(text: str) -> str:
    text = text.replace("</p>", " ")
    text = text.replace("&nbsp;", " ")
    return re.sub(r"<[^>]*>", "", text)
