from datetime import datetime
from http import HTTPStatus

from domain.report.models import Report
from flask import jsonify
from reports.constants import KONQUEST_GROUPS_MISSIONS_USERS_EXPORT
from reports.di import ReportContainer


def get_container():
    return ReportContainer()


def test_report(data=None, x_client=None):
    start_time = datetime.now()
    workspace_id = x_client or data["object_id"]
    report = Report(
        workspace_id=workspace_id,
        report_type_id=data["report_type"]["id"],
        object_id=data["id"],
        file_format=data["file_format"],
        user_creator={},
        language=data["language"],
        time_zone=data["time_zone"]
    )
    func = get_container().get_report_services_map()[data["report_type"]["name"]]
    url = func().generate(report).url
    time = datetime.now() - start_time
    minutes = int(time.seconds/60)
    seconds = time.seconds - minutes*60
    result = {"url": url,
              "elapsed time": "{:02d}:{:02d}".format(minutes, seconds)}
    if "caller" not in data or data["caller"] != "terminal":
        return jsonify(result, HTTPStatus.ACCEPTED)
    print(result)


if __name__ == '__main__':
    '''
    Se for chamar esse script pelo terminal, atualize a variável data abaixo,
    principalmente o report_type['name'], que define o método que será executado.

    Se for chamar pelo Postman, apenas passe via POST o payload retornado pelo
    endpoint de criação do relatório. Exemplo de uso no Postman:
    http://localhost:5000/api/v1/get-report
    '''
    data = {
        "file_format": "XLSX",
        "filters": {
        },
        "id": "d02b1dc3-134a-4b6b-8aaa-de08fdf4636b",
        "language": "pt-BR",
        "object_id": "e76b5082-f4fe-4f41-be79-1977840e16a8",
        "report_type": {
            "id": "7facca22-6f7c-405e-a2c4-11b3d3171ba8",
            "name": KONQUEST_GROUPS_MISSIONS_USERS_EXPORT,
        },
        "time_zone": "America/Sao_Paulo",
        "caller": "terminal"
    }
    test_report(data)
