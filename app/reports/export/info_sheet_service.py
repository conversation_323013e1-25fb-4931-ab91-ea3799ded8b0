import os
from datetime import datetime

import pandas as pd
import pytz
from config.default import Config
from domain.report.models import Report


class InfoSheetService():

    def add_info_sheet(self, writer: pd.ExcelWriter, report: Report, report_name: str, sheet_name: str):
        sheet_base_path = f"{Config.BASE_DIR}/sheets_base/{report_name}/{report.language}.xlsx"
        if not os.path.exists(sheet_base_path):
            return

        values = self._get_values(report)

        sheet_base = pd.read_excel(sheet_base_path, sheet_name=0)

        sheet_base.to_excel(writer, sheet_name=sheet_name, index=False)
        workbook = writer.book
        worksheet = writer.sheets[sheet_name]

        default_format, uppercase_format, subtitle_format = self._get_formats(workbook)

        self._apply_formatting(sheet_base, worksheet, values, uppercase_format, subtitle_format, default_format)

        self._apply_merge_ranges(worksheet, sheet_base, uppercase_format)

    def _get_values(self, report):
        timezone = pytz.timezone(report.time_zone)
        created_date = datetime.now(timezone).strftime("%Y-%m-%d %H:%M:%S")
        return {
            "created_date": created_date,
            "user_creator_email": report.user_creator.get("email")
        }

    def _get_formats(self, workbook):
        default_format = workbook.add_format({'text_wrap': True, 'align': 'center'})
        uppercase_format = workbook.add_format({
            'bold': True,
            'font_color': 'white',
            'bg_color': 'black',
            'align': 'center',
            'valign': 'vcenter'
        })
        subtitle_format = workbook.add_format({
            'bold': True,
            'font_color': 'white',
            'bg_color': 'gray',
            'align': 'center',
            'valign': 'vcenter'
        })
        return default_format, uppercase_format, subtitle_format

    def _apply_formatting(self, sheet_base, worksheet, values, uppercase_format, subtitle_format, default_format):
        worksheet.set_column('A:B', 60, default_format)

        for row_num, row_data in enumerate(sheet_base.values, start=1):
            for col_num, cell_value in enumerate(row_data):
                if isinstance(cell_value, str):
                    format_cell = self._get_cell_format(cell_value, row_data, uppercase_format, subtitle_format)
                    worksheet.write(row_num, col_num, cell_value, format_cell)

                    cell_value = self._replace_variables(cell_value, values)
                    if cell_value != row_data[col_num]:
                        worksheet.write(row_num, col_num, cell_value)

    def _apply_merge_ranges(self, worksheet, sheet_base, uppercase_format):
        worksheet.merge_range('A1:B1', sheet_base.columns[0], uppercase_format)
        worksheet.merge_range('A2:B2', sheet_base.iloc[0, 0])
        worksheet.merge_range('A9:B9', sheet_base.iloc[7, 0], uppercase_format)

    def _get_cell_format(self, cell_value, row_data, uppercase_format, subtitle_format):
        if cell_value.isupper():
            if str(row_data[0]).isupper() and str(row_data[1]).isupper():
                return subtitle_format
            return uppercase_format
        return None

    def _replace_variables(self, cell_value, values):
        if cell_value.startswith("{") and cell_value.endswith("}"):
            key = cell_value[1:-1]
            return values.get(key, cell_value)
        return cell_value
