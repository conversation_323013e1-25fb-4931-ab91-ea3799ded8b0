from domain.report.models import Report
from pandas import DataFrame
from reports.export import Export<PERSON>uilder, ExportCsvCreator, ExportPart, ExportXlsxCreator
from reports.formatters.date_formatter import DateFormatter
from reports.formatters.formatter_service import FormatterService
from reports.formatters.interval_formatter import IntervalFormatter
from reports.query.konquest.export import KonquestExportQuery
from reports.utils import calc_rate


class KonquestTrailExport(ExportBuilder):
    formatters_by_column = {
        "created_date": DateFormatter,
        "duration_trail": IntervalFormatter,
    }

    def __init__(
        self,
        xlsx_creator: ExportXlsxCreator,
        csv_creator: ExportCsvCreator,
        query_selector: KonquestExportQuery,
        formatter_service: FormatterService,
        report_name: str = None
    ):
        super().__init__(xlsx_creator, csv_creator, query_selector, formatter_service, report_name)
        self._main_query = query_selector
        self._workspace_id = None
        self._filters = {}

    def generate(self, report: Report) -> Report:
        self._workspace_id = report.workspace_id
        self._filters = report.filters
        return super().generate(report)

    def _load_parts(self) -> None:
        self._parts = [
            ExportPart(
                name="raw",
                data_lambda=self.get_main_data,
                index=False
            ),
        ]

    def load_main_data(self) -> DataFrame:
        trails = self._main_query.list_trails(self._workspace_id, None, self._filters)
        if not trails.empty:
            trails['completion_rate'] = trails.apply(
                lambda x: calc_rate(x['enrollments_completed'], x['enrollments']), axis=1
            )
        return trails

    def get_main_data(self) -> DataFrame:
        return self._main_data
