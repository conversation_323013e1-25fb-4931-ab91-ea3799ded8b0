from config.default import Config
from domain.report.models import Report
from pandas import DataFrame
from reports.export import Export<PERSON>uilder, ExportCsvCreator, ExportPart, ExportXlsxCreator
from reports.formatters.date_time_HM_formatter import DateTimeHMFormatter
from reports.formatters.formatter_service import FormatterService
from reports.query.konquest.export import KonquestExportQuery
from reports.utils import calc_rate, remove_html_tags


class KonquestMissionsQuizzesAnswers(ExportBuilder):
    formatters_by_column = {
        "enrolment_end_date": DateTimeHMFormatter,
        "answer_date": DateTimeHMFormatter
    }

    def __init__(
        self,
        xlsx_creator: ExportXlsxCreator,
        csv_creator: ExportCsvCreator,
        query_selector: KonquestExportQuery,
        formatter_service: FormatterService,
        report_name: str = None,
    ):
        super().__init__(xlsx_creator, csv_creator, query_selector, formatter_service, report_name)
        self._main_query = query_selector
        self._workspace_id = None
        self._filters = {}
        self._time_zone = None

    def generate(self, report: Report) -> Report:
        self._workspace_id = report.workspace_id
        self._filters = report.filters
        self._time_zone = report.time_zone
        return super().generate(report)

    def load_main_data(self) -> DataFrame:
        data = self._main_query.list_missions_quizzes_answers(
            self._workspace_id,
            Config.KONQUEST_EXPORT_MISSIONS_QUIZZES_ANSWERS_LIMIT_DAYS,
            self._filters,
            self._time_zone
        )
        
        if data.empty:
            return data
        
        data['total_answers'] = data.apply(
            lambda row: len(
                self._filter_mission_quiz_activities(data, row["enrollment_id"])
            ),
            axis=1
        )

        data["exam_question"] = data["exam_question"].apply(remove_html_tags)

        return data

    def _load_hits_by_questions_data(self) -> DataFrame:
        hits_by_questions = self.pivot_table_with_subtotal(
            data_frame=self._main_data,
            index=[
                "mission_name",
                "user_name",
                "exam_question"
            ],
            values=["hit"],
            prefix_margin='Total'
        )
        return hits_by_questions

    @staticmethod
    def _filter_mission_quiz_activities(data_frame: DataFrame, enrollment_id: str) -> DataFrame:
        return data_frame[(data_frame["enrollment_id"] == enrollment_id)]

    def _load_hits_by_enrollment_data(self) -> DataFrame:
        indexes = ["enrollment_id", "mission_name", "user_name", "enrollment_status", "enrollment_performance"]
        user_hits = self.pivot_table(
            data=self._main_data,
            index=indexes,
            values=["hit"],
            aggfunc="sum"
        )
        total_answers = self.pivot_table(
            data=self._main_data,
            index=indexes,
            values=['total_answers'],
        )
        user_hits = user_hits.merge(total_answers, on=indexes, how="left")
        user_hits["hits('%')"] = user_hits.apply(
            lambda x: calc_rate(
                x["hit"], x['total_answers']
            ),
            axis=1
        )
        return user_hits

    def _load_parts(self) -> None:
        self._parts = [
            ExportPart(
                name="raw",
                data_lambda=self._get_main_data,
                index=False
            ),
            ExportPart(
                name="hits by questions",
                data_lambda=self._load_hits_by_questions_data,
                index=True
            ),
            ExportPart(
                name="hits by enrollment",
                data_lambda=self._load_hits_by_enrollment_data,
                index=True
            ),
        ]
