import pandas as pd
from domain.report.models import Report
from pandas import DataFrame
from reports.export import ExportBuilder, ExportCsvCreator, ExportPart, ExportXlsxCreator
from reports.formatters.date_formatter import DateFormatter
from reports.formatters.formatter_service import FormatterService
from reports.formatters.interval_formatter import IntervalFormatter
from reports.query.konquest.export import KonquestExportQuery
from reports.utils import calc_rate


class KonquestTrailsCompletionRateExport(ExportBuilder):
    formatters_by_column = {
        "created_date": DateFormatter,
        "missions_duration_sum": IntervalFormatter,
        "sum_estimated_consume": IntervalFormatter
    }

    def __init__(
        self,
        xlsx_creator: ExportXlsxCreator,
        csv_creator: ExportCsvCreator,
        query_selector: KonquestExportQuery,
        formatter_service: FormatterService,
        report_name: str = None
    ):
        super().__init__(xlsx_creator, csv_creator, query_selector, formatter_service, report_name)
        self._main_query = query_selector
        self._workspace_id = None
        self._filters = {}

    def generate(self, report: Report) -> Report:
        self._workspace_id = report.workspace_id
        self._filters = report.filters
        return super().generate(report)

    def _load_parts(self) -> None:
        self._parts = [
            ExportPart(
                name="completion_rate_by_director",
                data_lambda=self._load_data_by_user_director,
                index=False
            ),
            ExportPart(
                name="completion_rate_by_manager",
                data_lambda=self._load_data_by_user_manager,
                index=False
            ),
            ExportPart(
                name="completion_rate_activity_area",
                data_lambda=self._load_data_by_activity_area,
                index=False
            ),
            ExportPart(
                name="completion_rate_by_leader",
                data_lambda=self._load_data_by_user_leader,
                index=False
            ),
        ]

    def load_main_data(self) -> DataFrame:
        trails = self._main_query.list_learning_trail_completion_rate(self._workspace_id, None, self._filters)
        return trails

    def _load_data_by_user_director(self) -> DataFrame:
        group_by = ['id', 'learning_trail_name', 'user_director']
        df = self._get_grouped_data_by_columns(group_by)
        df = df.drop('id', axis=1)
        df = df.sort_values('learning_trail_name')
        return df

    def _load_data_by_user_manager(self) -> DataFrame:
        group_by = ['user_manager']
        df = self._get_grouped_data_by_columns(group_by)
        df = df.drop('points', axis=1)
        df = df.drop('performance', axis=1)
        df = df.sort_values('user_manager')
        return df

    def _load_data_by_activity_area(self) -> DataFrame:
        group_by = ['user_area_of_activity']
        df = self._get_grouped_data_by_columns(group_by)
        df = df.drop('points', axis=1)
        df = df.drop('performance', axis=1)
        df = df.sort_values('user_area_of_activity')
        return df

    def _load_data_by_user_leader(self) -> DataFrame:
        group_by = ['user_leader_email']
        df = self._get_grouped_data_by_columns(group_by)
        df = df.drop('points', axis=1)
        df = df.drop('performance', axis=1)
        df = df.sort_values('user_leader_email')
        return df

    def _get_grouped_data_by_columns(self, group_by_list) -> DataFrame:
        status_counts = self._main_data.groupby(group_by_list + ['status']).size().unstack(fill_value=0)
        status_counts.columns = [f'enrollments_{col.lower()}' for col in status_counts.columns]
        for column in ['ENROLLED', 'STARTED', 'COMPLETED', 'REPROVED', 'INACTIVATED']:
            if f'enrollments_{column.lower()}' not in status_counts.columns:
                status_counts[f'enrollments_{column.lower()}'] = 0

        aggregates = self._main_data.groupby(group_by_list).agg(
            enrollments=('enrollment_count', 'sum'),
            points=('points', 'sum'),
            performance=('performance', 'mean')
        ).reset_index()

        df = pd.merge(status_counts.reset_index(), aggregates, how='outer', on=group_by_list)

        df['completion_rate'] = df.apply(
            lambda x: calc_rate(x['enrollments_completed'], x['enrollments']), axis=1
        )

        columns_to_fill_and_order = group_by_list + [
            'enrollments', 'enrollments_enrolled', 'enrollments_started', 'enrollments_completed',
            'enrollments_reproved', 'enrollments_inactivated', 'completion_rate', 'points', 'performance']
        for column in columns_to_fill_and_order:
            if column not in df:
                df[column] = 0

        df = df.reindex(columns=columns_to_fill_and_order)
        return df
