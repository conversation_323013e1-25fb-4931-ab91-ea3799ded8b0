import pandas as pd
from config.default import Config
from domain.report.models import Report
from pandas import DataFrame
from reports.export import Export<PERSON>uilder, ExportCsvCreator, ExportPart, ExportXlsxCreator
from reports.formatters.date_formatter import DateFormatter
from reports.formatters.formatter_service import FormatterService
from reports.query.konquest.export import KonquestExportQuery


class KonquestMissionEvaluationsStatisticsExport(ExportBuilder):
    formatters_by_column = {
        "last_evaluation_date": DateFormatter,
        "mission_created_date": DateFormatter
    }
    def __init__(
        self,
        xlsx_creator: ExportXlsxCreator,
        csv_creator: ExportCsvCreator,
        query_selector: KonquestExportQuery,
        formatter_service: FormatterService,
        report_name: str
    ):
        super().__init__(xlsx_creator, csv_creator, query_selector, formatter_service, report_name)
        self._main_query = query_selector
        self._workspace_id = None
        self._filters = {}

    def generate(self, report: Report) -> Report:
        self._workspace_id = report.workspace_id
        self._filters = report.filters
        return super().generate(report)

    def load_main_data(self) -> DataFrame:
        return self._main_query.list_missions_evaluation_statistics(
            self._workspace_id, Config.KONQUEST_EXPORT_MISSION_EVALUATIONS_LIMIT_DAYS, self._filters
        )

    def _get_data_by_mission_category(self):
        return self._reshape_by_column('mission_category')

    def _get_data_by_user_creator(self):
        return self._reshape_by_column('user_creator')

    def _get_data_by_mission_model(self):
        return self._reshape_by_column('mission_model')

    def _reshape_by_column(self, column: str) -> DataFrame:
        is_empty_column = self._is_empty_series(self._main_data[column])
        if is_empty_column:
            return DataFrame()

        sum_columns = ['evaluations_total', 'promoters_total', 'detractors_total', 'passives_total']
        evaluations_sum = self.pivot_table(
            self._main_data,
            index=[column],
            values=sum_columns,
            aggfunc='sum',
            fill_value=None
        ).reindex(sum_columns, axis=1)
        avg_columns = [
            'question_1_rating_avg',
            'question_2_rating_avg',
            'question_3_rating_avg',
            'question_4_rating_avg',
            'question_5_rating_avg',
            'question_6_rating_avg',
            'questions_rating_avg',
            'nps'
        ]
        evaluations_avg = self.pivot_table(
            self._main_data,
            index=[column],
            values=avg_columns,
            aggfunc=pd.Series.mean,
            fill_value=None
        ).round(decimals=2).reindex(avg_columns, axis=1)

        return evaluations_sum.merge(evaluations_avg, how='inner', on=column)

    @staticmethod
    def _get_questions() -> DataFrame:
        questions = [
            {
                "question_number": 1,
                "question_text": "mission_evaluation_relevant_content"
            },
            {
                "question_number": 2,
                "question_text": "mission_evaluation_basic_concepts"
            },
            {
                "question_number": 3,
                "question_text": "mission_evaluation_practical_applicability"
            },
            {
                "question_number": 4,
                "question_text": "mission_evaluation_content_format"
            },
            {
                "question_number": 5,
                "question_text": "mission_evaluation_awareness"
            },
            {
                "question_number": 6,
                "question_text": "mission_evaluation_content_time"
            }
        ]
        return DataFrame(questions)

    def _load_parts(self) -> None:
        self._parts = [
            ExportPart(
                name="raw",
                data_lambda=self._get_main_data,
                index=False
            ),
            ExportPart(
                name="questions",
                data_lambda=self._get_questions,
                index=False
            ),
            ExportPart(
                name="by_mission_category",
                data_lambda=self._get_data_by_mission_category,
            ),
            ExportPart(
                name="by_user_creator",
                data_lambda=self._get_data_by_user_creator,
            ),
            ExportPart(
                name="by_mission_model",
                data_lambda=self._get_data_by_mission_model,
            ),
        ]
