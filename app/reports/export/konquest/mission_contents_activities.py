import pandas as pd
from domain.report.models import Report
from pandas import DataFrame
from reports.export import Export<PERSON>uilder, ExportCsvCreator, ExportPart, ExportXlsxCreator
from reports.formatters.date_formatter import DateFormatter
from reports.formatters.date_time_HMS_formatter import DateTimeHMSFormatter
from reports.formatters.formatter_service import FormatterService
from reports.query.konquest.export import KonquestExportQuery
from reports.services import KontentService


class KonquestMissionContentsActivitiesExport(ExportBuilder):
    formatters_by_column = {
        "enrollment_start_date": DateFormatter,
        "enrollment_end_date": DateFormatter,
        "time_start": DateTimeHMSFormatter,
        "time_stop": DateTimeHMSFormatter,
    }

    def __init__(
        self,
        xlsx_creator: ExportXlsxCreator,
        csv_creator: ExportCsvCreator,
        query_selector: KonquestExportQuery,
        kontent_service: KontentService,
        formatter_service: FormatterService,
        report_name: str = None
    ):
        super().__init__(xlsx_creator, csv_creator, query_selector, formatter_service, report_name)
        self._main_query = query_selector
        self._workspace_id = None
        self._filters = {}
        self._kontent_service = kontent_service

    def generate(self, report: Report) -> Report:
        self._workspace_id = report.workspace_id
        self._filters = report.filters
        return super().generate(report)

    def load_main_data(self) -> DataFrame:
        activities = self._main_query.list_mission_contents_activities(self._workspace_id, filters=self._filters)
        if activities.empty:
            return activities
        activities['learn_content_uuid'] = activities['learn_content_uuid'].map(str)
        activities['tracking_in_calc'] = activities.apply(
                lambda x: x["content_type"] == 'CONTENT', axis=1
        )
        activities = activities.drop('content_type', axis=1)

        learn_content_ids = list(set(activities['learn_content_uuid'].to_list()))
        learn_contents = self._kontent_service.get_contents(learn_content_ids)

        df_learn_contents = DataFrame(learn_contents)
        df_learn_contents.rename(columns={'id': 'learn_content_uuid'}, inplace=True)
        df_learn_contents.rename(columns={'name': 'content_name'}, inplace=True)
        df_learn_contents.rename(columns={'duration': 'content_duration(seconds)'}, inplace=True)

        activities = activities.merge(
            pd.DataFrame(df_learn_contents),
            on='learn_content_uuid',
            how='left'
        )

        return activities.drop('learn_content_uuid', axis=1)

    def _get_by_enrollment_data(self) -> DataFrame:
        only_calc_tracking = self._main_data[self._main_data["tracking_in_calc"]]
        indexes = [
            'enrollment_id',
            'user_email',
            'mission_name',
            'enrollment_performance',
            'content_name',
            'content_duration(seconds)',
        ]
        return self.pivot_table_with_subtotal(
            only_calc_tracking,
            values=['consume_time(seconds)'],
            index=indexes,
        )

    def _load_parts(self) -> None:
        self._parts = [
            ExportPart(
                name="raw",
                data_lambda=self._get_main_data,
                index=False
            ),
            ExportPart(
                name="by enrollment",
                data_lambda=self._get_by_enrollment_data,
            ),
        ]
