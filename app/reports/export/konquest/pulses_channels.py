import pandas as pd
from config.default import Config
from domain.report.models import Report
from pandas import DataFrame
from reports.export import Export<PERSON>uilder, ExportCsvCreator, ExportPart, ExportXlsxCreator
from reports.formatters.date_time_HM_formatter import DateTimeHMFormatter
from reports.formatters.formatter_service import FormatterService
from reports.query.konquest.export import KonquestExportQuery
from reports.utils import convert_timedelta_to_str


class KonquestPulsesChannelsExport(ExportBuilder):
    formatters_by_column = {
        'created_date': DateTimeHMFormatter
    }
    def __init__(
        self,
        xlsx_creator: ExportXlsxCreator,
        csv_creator: ExportCsvCreator,
        query_selector: KonquestExportQuery,
        formatter_service: FormatterService,
        report_name: str = None
    ):
        super().__init__(xlsx_creator, csv_creator, query_selector, formatter_service, report_name)
        self._main_query = query_selector
        self._workspace_id = None
        self._filters = {}

    def generate(self, report: Report) -> Report:
        self._workspace_id = report.workspace_id
        self._filters = report.filters
        return super().generate(report)

    def load_main_data(self) -> DataFrame:
        return self._main_query.list_pulses_channels(
            self._workspace_id, Config.KONQUEST_EXPORT_PULSES_CHANNELS_LIMIT_DAYS, self._filters
        )

    def _load_pulses_by_channel_data(self) -> DataFrame:
        return self.pivot_table_with_subtotal(
            data_frame=self._main_data,
            index=['channel_name', 'pulse_id', 'pulse_name', 'pulse_type'],
            values=['duration'],
            prefix_margin='Total',
            aggfunc='sum',
            fill_value=None
        )

    def _load_pulses_by_creator_data(self) -> DataFrame:
        return self.pivot_table_with_subtotal(
            data_frame=self._main_data,
            index=['user_creator_name', 'pulse_id', 'pulse_name', 'pulse_type'],
            values=['duration'],
            prefix_margin='Total',
            aggfunc=pd.Series.mean,
            fill_value=None
        )

    def _load_stats_data(self) -> DataFrame:
        unique_pulses = self._main_data.drop_duplicates(subset='pulse_id', keep='first')

        stats = {
            "pulses_counters": ['Total Pulses', 'Total Time'],
            "Total": [
                unique_pulses['pulse_id'].count(),
                convert_timedelta_to_str(unique_pulses['duration'].sum())
            ]
        }
        return DataFrame(stats)

    def _load_parts(self) -> None:
        self._parts = [
            ExportPart(
                name="raw",
                data_lambda=self._get_main_data,
                index=False
            ),
            ExportPart(name="pulses by channel", data_lambda=self._load_pulses_by_channel_data),
            ExportPart(name="pulses by creator", data_lambda=self._load_pulses_by_creator_data),
            ExportPart(name="pulses stats", data_lambda=self._load_stats_data, index=False),
        ]
