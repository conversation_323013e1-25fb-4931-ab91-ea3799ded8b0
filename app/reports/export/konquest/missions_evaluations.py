from config.default import Config
from domain.report.models import Report
from pandas import DataFrame
from reports.export import Export<PERSON>uilder, ExportCsvCreator, ExportPart, ExportXlsxCreator
from reports.formatters.date_formatter import DateFormatter
from reports.formatters.formatter_service import FormatterService
from reports.query.konquest.export import KonquestExportQuery


class KonquestMissionsEvaluationsExport(ExportBuilder):
    formatters_by_column = {
        "evaluation_date": DateFormatter
    }
    def __init__(
        self,
        xlsx_creator: ExportXlsxCreator,
        csv_creator: ExportCsvCreator,
        query_selector: KonquestExportQuery,
        formatter_service: FormatterService,
        report_name: str = None
    ):
        super().__init__(xlsx_creator, csv_creator, query_selector, formatter_service, report_name)
        self._main_query = query_selector
        self._workspace_id = None
        self._filters = {}

    def generate(self, report: Report) -> Report:
        self._workspace_id = report.workspace_id
        self._filters = report.filters
        return super().generate(report)

    def load_main_data(self) -> DataFrame:
        return self._main_query.list_missions_evaluations(
            self._workspace_id, Config.KONQUEST_EXPORT_MISSION_EVALUATIONS_LIMIT_DAYS, self._filters
        )

    @staticmethod
    def _get_questions() -> DataFrame:
        questions = [
            {
                "question_number": 1,
                "question_text": "mission_evaluation_relevant_content"
            },
            {
                "question_number": 2,
                "question_text": "mission_evaluation_basic_concepts"
            },
            {
                "question_number": 3,
                "question_text": "mission_evaluation_practical_applicability"
            },
            {
                "question_number": 4,
                "question_text": "mission_evaluation_content_format"
            },
            {
                "question_number": 5,
                "question_text": "mission_evaluation_awareness"
            },
            {
                "question_number": 6,
                "question_text": "mission_evaluation_content_time"
            }
        ]
        return DataFrame(questions)

    def _load_parts(self) -> None:
        self._parts = [
            ExportPart(
                name="raw",
                data_lambda=self._get_main_data,
                index=False
            ),
            ExportPart(
                name="questions",
                data_lambda=self._get_questions,
                index=False
            )
        ]
