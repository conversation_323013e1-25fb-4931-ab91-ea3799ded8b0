from typing import Optional

from config.default import Config
from domain.report.models import Report
from pandas import DataFrame
from reports.constants import PERFORMANCE_AVERAGE
from reports.export import ExportBuilder, ExportCsvCreator, ExportPart, ExportXlsxCreator
from reports.formatters.formatter_service import FormatterService
from reports.query.konquest.export import KonquestExportQuery


class KonquestUserGeneralStatisticsExport(ExportBuilder):
    def __init__(
        self,
        xlsx_creator: ExportXlsxCreator,
        csv_creator: ExportCsvCreator,
        query_selector: KonquestExportQuery,
        formatter_service: FormatterService,
        report_name: str
    ):
        super().__init__(xlsx_creator, csv_creator, query_selector, formatter_service, report_name)
        self._main_query = query_selector
        self._workspace_id = None
        self._filters = {}

    def generate(self, report: Report) -> Report:
        self._workspace_id = report.workspace_id
        self._filters = report.filters
        return super().generate(report)

    def _load_parts(self) -> None:
        self._parts = [
            ExportPart(
                name="raw",
                data_lambda=self._get_main_data,
                index=False
            ),
        ]

    def load_main_data(self) -> DataFrame:
        data = self._main_query.list_users_general_statistics(
            self._workspace_id, Config.KONQUEST_EXPORT_USER_GENERAL_STATISTICS_LIMIT_DAYS, self._filters
        )
        if not data.empty:
            data[PERFORMANCE_AVERAGE] = data[PERFORMANCE_AVERAGE].apply(lambda x: self._convert_float_to_percentage(x))
        return data

    @staticmethod
    def _convert_float_to_percentage(number: Optional[float]) -> str:
        return "{:.0%}".format(number) if number else '0%'
