from datetime import datetime, timed<PERSON>ta
from typing import Optional

import pandas as pd
from config.default import Config
from domain.report.models import Report
from pandas import DataFrame
from reports.export import ExportBuilder, ExportCsvCreator, ExportPart, ExportXlsxCreator
from reports.export.konquest.mission_enrollment import USER_PROFILE_WORKSPACE
from reports.formatters.date_formatter import DateFormatter
from reports.formatters.formatter_service import FormatterService
from reports.query.konquest.export import KonquestExportQuery

LAST_ACTIVITY_DATE = 'last_activity_date'


class KonquestUsersExport(ExportBuilder):
    profile_filter_fields = ["director__in", "manager__in", "area_of_activity__in"]
    formatters_by_column = {
        "last_access_date": DateFormatter,
        LAST_ACTIVITY_DATE: DateFormatter,
        "user_registration_date": DateFormatter,
        "user_admission_date": DateFormatter,
        "birthday": DateFormatter
    }
    def __init__(
        self,
        xlsx_creator: ExportXlsxCreator,
        csv_creator: ExportCsvCreator,
        konquest_query: KonquestExportQuery,
        formatter_service: FormatterService,
        max_days_of_inactivity: int,
        report_name: str = None,
    ):
        super().__init__(xlsx_creator, csv_creator, konquest_query, formatter_service, report_name)
        self._main_query = konquest_query
        self._workspace_id = None
        self._max_days_of_inactivity = max_days_of_inactivity
        self._filters = {}

    def generate(self, report: Report) -> Report:
        self._workspace_id = report.workspace_id
        self._filters = report.filters if report.filters else {}
        return super().generate(report)

    def load_main_data(self) -> DataFrame:
        self._filters.update({"role__application_id": Config.KONQUEST_APPLICATION_ID})
        user_profile_workspace_filter = self._get_profile_filter() if self._filters else None
        if user_profile_workspace_filter:
            self._filters.update(user_profile_workspace_filter)

        if 'activity_gte_date' in self._filters or 'activity_lte_date' in self._filters:
            activity_gte_date = self._filters.pop('activity_gte_date', None)
            activity_lte_date = self._filters.pop('activity_lte_date', None)

            users_with_activity = self._main_query.list_user_with_activity(
                self._workspace_id,
                activity_gte_date,
                activity_lte_date
            )

            if users_with_activity.empty:
                return DataFrame({})
            user_ids_with_activity = users_with_activity["user_id"].tolist()
            users = self._main_query.list_users(self._workspace_id, filters=self._filters)
            filtered_users = users[users["user_id"].isin(user_ids_with_activity)]

            return filtered_users

        return self._main_query.list_users(self._workspace_id, filters=self._filters)

    def _get_profile_filter(self) -> Optional[dict]:
        if USER_PROFILE_WORKSPACE not in self._filters:
            return
        request_filter = self._filters.pop(USER_PROFILE_WORKSPACE)
        profile_filter = {}
        for field in self.profile_filter_fields:
            profile_filter[f"{USER_PROFILE_WORKSPACE}__{field}"] = request_filter.get(field, [])
        return profile_filter


    def _load_parts(self) -> None:
        self._parts = [
            ExportPart(
                name="raw",
                data_lambda=self._get_main_data,
                index=False
            ),
            ExportPart(
                name="users_by_leader",
                data_lambda=self._load_data_by_leader,
            ),
            ExportPart(
                name="users_by_director",
                data_lambda=self._load_data_by_director,
            ),
            ExportPart(
                name="users_by_manager",
                data_lambda=self._load_data_by_manager,
            ),
            ExportPart(
                name="users_by_area",
                data_lambda=self._load_data_by_area_of_activity,
            ),
            ExportPart(
                name="users_by_country",
                data_lambda=self._load_data_by_country,
            ),
            ExportPart(
                name="users_by_job",
                data_lambda=self._load_data_by_job,
            ),
            ExportPart(
                name="totalizers",
                data_lambda=self._load_data_statics_totalizers,
                index=False,
                by_line=True
            ),
        ]

    def _load_data_by_leader(self) -> DataFrame:
        return self._reshape_data_by_column('user_leader_email')

    def _load_data_by_director(self) -> DataFrame:
        return self._reshape_data_by_column('user_director')

    def _load_data_by_manager(self) -> DataFrame:
        return self._reshape_data_by_column('user_manager')

    def _load_data_by_area_of_activity(self) -> DataFrame:
        return self._reshape_data_by_column('user_area_of_activity')

    def _load_data_by_country(self) -> DataFrame:
        return self._reshape_data_by_column('user_country')

    def _load_data_by_job(self) -> DataFrame:
        return self._reshape_data_by_column('user_job')

    def _load_data_statics_totalizers(self) -> DataFrame:
        return self._reshape_data_by_column("")

    def _reshape_data_by_column(self, column: str) -> DataFrame:
        if column == "":
            self._main_data['__all__'] = ""
            column = "__all__"
        is_empty_column = self._is_empty_series(self._main_data[column])
        
        if is_empty_column:
            return DataFrame()

        value_counts = self._main_data[column].value_counts()
        data = value_counts.to_frame()
        data.columns = ['total_users_enabled']
        data.index.rename(column, inplace=True)

        self._main_data[LAST_ACTIVITY_DATE] = pd.to_datetime(self._main_data[LAST_ACTIVITY_DATE], errors='coerce')
        
        some_days_ago = pd.to_datetime(datetime.now() - timedelta(days=self._max_days_of_inactivity))
        
        if self._main_data[LAST_ACTIVITY_DATE].dt.tz is not None:
            some_days_ago = some_days_ago.tz_localize('UTC')
        
        active_users_statement = (self._main_data[LAST_ACTIVITY_DATE] >= some_days_ago)
        
        active_users = self._main_data[column][active_users_statement].value_counts()
        inactive_users = self._main_data[column][~active_users_statement].value_counts()
        users_who_never_logged_on = self._main_data[column][self._main_data['last_access_date'].isnull()].value_counts()
        data = data.assign(active_users=active_users)
        data = data.assign(inactive_users=inactive_users)
        data = data.assign(users_who_never_logged_on=users_who_never_logged_on)
        data['engagement_rate'] = round(data['active_users'] / data['total_users_enabled'], 2)
        data['inactivity_rate'] = round(data['inactive_users'] / data['total_users_enabled'], 2)
        data['access_rate'] = round((data['total_users_enabled'] - data['users_who_never_logged_on']) / data['total_users_enabled'], 2)

        return data.fillna(0)
