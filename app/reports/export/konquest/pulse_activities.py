from typing import Sequence

import pandas as pd
from config.default import Config
from domain.report.models import Report
from pandas import DataFrame
from reports.export import ExportBuilder, ExportCsvCreator, ExportPart, ExportXlsxCreator
from reports.formatters.date_formatter import DateFormatter
from reports.formatters.date_time_HM_formatter import DateTimeHMFormatter
from reports.formatters.formatter_service import FormatterService
from reports.query.konquest.export import KonquestExportQuery
from reports.utils import calc_rate


class KonquestPulseActivityExport(ExportBuilder):
    formatters_by_column = {
        "start_at": DateTimeHMFormatter,
        "stop_at": DateTimeHMFormatter,
        "report_date": DateFormatter
    }
    def __init__(
        self,
        xlsx_creator: ExportXlsxCreator,
        csv_creator: ExportCsvCreator,
        query_selector: KonquestExportQuery,
        formatter_service: FormatterService,
        report_name: str
    ):
        super().__init__(xlsx_creator, csv_creator, query_selector, formatter_service, report_name)
        self._main_query = query_selector
        self._workspace_id = None
        self._filters = {}

    def generate(self, report: Report) -> Report:
        self._workspace_id = report.workspace_id
        self._filters = report.filters
        return super().generate(report)

    def load_main_data(self) -> DataFrame:
        return self._main_query.list_pulse_activities(
            self._workspace_id, Config.KONQUEST_EXPORT_USERS_PULSES_ACTIVITIES_LIMIT_DAYS, self._filters
        )

    def _load_parts(self) -> None:
        self._parts = [
            ExportPart(name="raw", data_lambda=self._get_main_data, index=False),
            ExportPart(name="activities by user", data_lambda=self._load_data_by_user),
            ExportPart(name="activities by channel", data_lambda=self._load_data_by_channel),
            ExportPart(name="activities by country", data_lambda=self._load_data_by_country),
            ExportPart(name="activities by leader", data_lambda=self._load_data_by_leader)
        ]

    def _get_main_data(self) -> DataFrame:
        return self._main_data

    def _load_data_by_user(self) -> DataFrame:
        return self._generate_pivot_with_duration_data(['user_email', 'user_name', 'pulse_name'])

    def _load_data_by_channel(self) -> DataFrame:
        return self._generate_pivot_with_duration_data(['channel_name', 'pulse_name'])

    def _generate_pivot_with_duration_data(self, indexes: Sequence[str]) -> DataFrame:
        consume_duration_data = self.pivot_table(
            data=self._main_data,
            index=indexes,
            values=['consume_duration'],
            aggfunc='sum'
        )
        content_duration_data = self.pivot_table(
            data=self._main_data,
            index=indexes,
            values=['content_duration'],
            aggfunc=pd.Series.mean,
        )
        activities_data = consume_duration_data.merge(content_duration_data, on=indexes, how='left')
        activities_data['consume_percentage'] = activities_data.apply(
            lambda row: calc_rate(
                row['consume_duration'], row['content_duration']
            ),
            axis=1
        )
        return activities_data

    def _load_data_by_country(self) -> DataFrame:
        return self._reshape_data_by_column("user_country")

    def _load_data_by_leader(self) -> DataFrame:
        return self._reshape_data_by_column("user_leader")

    def _reshape_data_by_column(self, column) -> DataFrame:
        is_empty_column = self._is_empty_series(self._main_data[column])
        if is_empty_column:
            return DataFrame()
        values_count = self._main_data[column].value_counts()
        count_data = values_count.to_frame()
        count_data.columns = ['count_pulses_consumed']
        count_data.index.rename(column, inplace=True)

        df_pulse_consume_sum = self.pivot_table(
            self._main_data,
            index=[column],
            values=['content_duration', 'consume_duration'],
            aggfunc='sum',
            fill_value=None
        )
        df_pulse_consume_sum = df_pulse_consume_sum.add_suffix('_sum')

        df_pulse_consume_avg = self.pivot_table(
            self._main_data,
            index=[column],
            values=['content_duration', 'consume_duration'],
            aggfunc=pd.Series.mean,
            fill_value=None
        )
        df_pulse_rating_avg = self.pivot_table(
            self._main_data,
            index=[column],
            values=['rating'],
            aggfunc='mean'
        ).round(decimals=2)
        df_pulse_consume_avg = df_pulse_consume_avg.merge(df_pulse_rating_avg, on=column, how='left')
        df_pulse_consume_avg = df_pulse_consume_avg.add_suffix('_avg')
        df_pulse_consume = df_pulse_consume_sum.merge(df_pulse_consume_avg, on=column, how='inner')

        return count_data.merge(df_pulse_consume, how='inner', on=column)
