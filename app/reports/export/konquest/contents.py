import boto3
from config.default import Config
from domain.report.models import Report
from pandas import DataFrame
from reports.export import Export<PERSON>uilder, ExportCsvCreator, ExportPart, ExportXlsxCreator
from reports.formatters.formatter_service import FormatterService
from reports.query.konquest.export import KonquestExportQuery
from reports.query.kontent.query import KontentQuery

COLUMNS_TO_DROP = ["id", "analyzed", "transcribe_job", "category_id", "updated_date", "content_type_id"]


class KonquestContentExport(ExportBuilder):
    def __init__(
        self,
        xlsx_creator: ExportXlsxCreator,
        csv_creator: ExportCsvCreator,
        query_selector: KonquestExportQuery,
        kontent_query: KontentQuery,
        formatter_service: FormatterService,
        report_name: str,
    ):
        super().__init__(xlsx_creator, csv_creator, query_selector, formatter_service, report_name)
        self._main_query = query_selector
        self._kontent_query = kontent_query
        self._workspace_id = None
        self._filters = {}
        self.client = boto3.client(
            "s3",
            aws_access_key_id=Config.AWS_S3_ACCESS_KEY_ID,
            aws_secret_access_key=Config.AWS_S3_SECRET_ACCESS_KEY,
            region_name=Config.AWS_S3_REGION_NAME,
        )

    def generate_presigned_url(self, url: str) -> str:
        if not isinstance(url, str) or not url.startswith(Config.AWS_BASE_S3_URL):
            return url
        path_part = url.split(f"{Config.AWS_BASE_S3_URL}/")[1]
        parts = path_part.split("/", 1)
        if len(parts) < 2:
            return url

        bucket, key = parts[0], parts[1]

        presigned_url = self.s3_client.generate_presigned_url(
            "get_object", Params={"Bucket": bucket, "Key": key}, ExpiresIn=2592000
        )
        return presigned_url

    def generate(self, report: Report) -> Report:
        self._workspace_id = report.workspace_id
        self._filters = report.filters
        return super().generate(report)

    def load_main_data(self) -> DataFrame:
        contents = self._main_query.list_contents(self._workspace_id, self._filters)
        if contents.empty:
            return contents
        content_ids = contents["id"].tolist()
        contents = DataFrame(self._kontent_query.list_learn_contents(content_ids))

        try:
            contents = contents.drop(labels=COLUMNS_TO_DROP, axis=1)
        except KeyError:
            pass

        if "url" in contents.columns:
            contents["url"] = contents["url"].apply(self.generate_presigned_url)

        return contents

    def _get_main_data(self) -> DataFrame:
        return self._main_data

    def _load_parts(self) -> None:
        self._parts = [
            ExportPart(name="raw", data_lambda=self._get_main_data, index=False),
        ]
