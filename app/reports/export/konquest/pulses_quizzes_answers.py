from config.default import Config
from domain.report.models import Report
from pandas import DataFrame
from reports.export import Export<PERSON>uilder, ExportCsvCreator, ExportPart, ExportXlsxCreator
from reports.formatters.date_time_HM_formatter import DateTimeHMFormatter
from reports.formatters.formatter_service import FormatterService
from reports.query.konquest.export import KonquestExportQuery


class KonquestPulsesQuizzesAnswers(ExportBuilder):
    formatters_by_column = {
        "answer_date": DateTimeHMFormatter
    }

    def __init__(
        self,
        xlsx_creator: ExportXlsxCreator,
        csv_creator: ExportCsvCreator,
        query_selector: KonquestExportQuery,
        formatter_service: FormatterService,
        report_name: str = None
    ):
        super().__init__(xlsx_creator, csv_creator, query_selector, formatter_service, report_name)
        self._main_query = query_selector
        self._workspace_id = None
        self._filters = {}

    def generate(self, report: Report) -> Report:
        self._workspace_id = report.workspace_id
        self._filters = report.filters
        return super().generate(report)

    def load_main_data(self) -> DataFrame:
        data = self._main_query.list_pulses_quizzes_answers(
            self._workspace_id,
            Config.KONQUEST_EXPORT_PULSES_QUIZZES_ANSWERS_LIMIT_DAYS,
            self._filters
        )
        return data

    def _load_parts(self) -> None:
        self._parts = [
            ExportPart(
                name="raw",
                data_lambda=self._get_main_data,
                index=False
            ),
        ]
