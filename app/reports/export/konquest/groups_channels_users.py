import pandas as pd
from config.default import Config
from domain.report.models import Report
from pandas import DataFrame
from reports.export import ExportBuilder, ExportCsvCreator, ExportPart, ExportXlsxCreator
from reports.formatters.formatter_service import FormatterService
from reports.query.konquest.export import KonquestExportQuery


class KonquestGroupsChannelsUsersExport(ExportBuilder):
    def __init__(
        self,
        xlsx_creator: ExportXlsxCreator,
        csv_creator: ExportCsvCreator,
        query_selector: KonquestExportQuery,
        formatter_service: FormatterService,
        report_name: str
    ):
        super().__init__(xlsx_creator, csv_creator, query_selector, formatter_service, report_name)
        self._main_query = query_selector
        self._workspace_id = None
        self._filters = {}
        self._channels_data = None
        self._users_data = None

    def generate(self, report: Report) -> Report:
        self._workspace_id = report.workspace_id
        self._filters = report.filters
        return super().generate(report)

    def load_main_data(self) -> DataFrame:
        data = self._main_query.list_groups_channels_users(
            self._workspace_id, Config.KONQUEST_EXPORT_GROUPS_CHANNELS_USERS_LIMIT_DAYS, self._filters
        )
        return data

    def _load_channels_by_group_data(self) -> DataFrame:
        indexes = ['group_name']
        group_frequency = self._main_data[indexes[0]].value_counts().to_dict()
        data = DataFrame()

        for group_name in group_frequency:
            filter_by_group = self._main_data[indexes[0]] == group_name
            filtered_data = self._main_data[filter_by_group].drop_duplicates('channel_name')
            data = pd.concat([data, filtered_data.drop(columns=['user_name', 'user_email'])])

        self._channels_data = data.set_index(['group_name', 'channel_name'])
        return self._channels_data

    def _load_users_by_group_data(self) -> DataFrame:
        indexes = ['group_name']
        group_frequency = self._main_data[indexes[0]].value_counts().to_dict()
        data = DataFrame()

        for group_name in group_frequency:
            filter_by_group = self._main_data[indexes[0]] == group_name
            filtered_data = self._main_data[filter_by_group].drop_duplicates('user_email')
            data = pd.concat([data, filtered_data.drop(columns=['channel_name', 'channel_type'])])

        self._users_data = data.set_index(['group_name', 'user_email'])
        return self._users_data

    def _load_stats_data(self) -> DataFrame:
        group_frequency = self._main_data['group_name'].value_counts().to_dict()
        data = []

        for group in group_frequency:
            count_users = len(
                (self._users_data[self._users_data.index.isin([group], level=0)]).value_counts().to_dict()
            )
            count_channels = len(
                (self._channels_data[self._channels_data.index.isin([group], level=0)]).value_counts().to_dict()
            )
            data_stats = {
                "group_name": group,
                "users": count_users,
                "channels": count_channels
            }
            data.append(data_stats)

        return DataFrame(data)

    def _load_parts(self) -> None:
        self._parts = [
            ExportPart(
                name="raw",
                data_lambda=self._get_main_data,
                index=False
            ),
            ExportPart(
                name="channels by group",
                data_lambda=self._load_channels_by_group_data,
                index=True
            ),
            ExportPart(
                name="users by group",
                data_lambda=self._load_users_by_group_data,
                index=True
            ),
            ExportPart(
                name="stats",
                data_lambda=self._load_stats_data,
                index=False
            )
        ]
