from config.default import Config
from domain.report.models import Report
from pandas import <PERSON>Frame, Timedel<PERSON>, notnull, to_numeric
from reports.export import ExportBuilder, ExportCsvCreator, ExportPart, ExportXlsxCreator
from reports.formatters.date_formatter import DateFormatter
from reports.formatters.formatter_service import FormatterService
from reports.query.konquest.user_consumptions import KonquestUserConsumptionsQuery


class KonquestUserConsumptionExport(ExportBuilder):
    formatters_by_column = {
        "date_end": DateFormatter,
        "date_start": DateFormatter
    }
    def __init__(
        self,
        xlsx_creator: ExportXlsxCreator,
        csv_creator: ExportCsvCreator,
        query_selector: KonquestUserConsumptionsQuery,
        formatter_service: FormatterService,
        report_name: str = None
    ):
        super().__init__(xlsx_creator, csv_creator, query_selector, formatter_service, report_name)
        self._main_query = query_selector
        self._workspace_id = None
        self._filters = {}

    def generate(self, report: Report) -> Report:
        self._workspace_id = report.workspace_id
        self._filters = report.filters
        return super().generate(report)

    def _load_parts(self) -> None:
        self._parts = [
            ExportPart(
                name="raw",
                data_lambda=self._get_main_data,
                index=False
            ),
        ]

    def load_main_data(self) -> DataFrame:
        data = self._main_query.execute(self._workspace_id, Config.KONQUEST_EXPORT_USER_CONSUMPTION_LIMIT_DAYS, self._filters)
        if not data.empty:
            data = data.drop(columns="object_id")
            data["performance"] = to_numeric(data["performance"], errors='coerce').fillna(0)
            data["duration"] = data["duration"].fillna(Timedelta(seconds=0))
            data["consume_duration"] = (
            data["duration"].dt.total_seconds() * data["performance"]
                ).apply(lambda x: Timedelta(seconds=round(x)))
        return data

    def get_raw_data(self, report: Report) -> dict:
        self._workspace_id = report.workspace_id
        self._filters = report.filters
        data = self.load_main_data()
        data = data.where(notnull(data), None)
        if not data.empty:
            data["date_start"] = data["date_start"].astype(str)
            data["date_end"] = data["date_end"].astype(str)
            data["duration"] = data["duration"].astype(str)
            data["consume_duration"] = data["consume_duration"].astype(str)
        return data.to_dict("records")
