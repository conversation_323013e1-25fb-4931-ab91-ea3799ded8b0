from typing import Optional

import pandas as pd
from config.default import Config
from domain.report.models import Report
from pandas import DataFrame, Series
from reports.export import ExportBuilder, ExportCsvCreator, ExportPart, ExportXlsxCreator
from reports.formatters.date_formatter import DateFormatter
from reports.formatters.formatter_service import FormatterService
from reports.query.account.export import AccountExportQuery
from reports.query.konquest.export import KonquestExportQuery
from reports.utils import calc_rate

USER_PROFILE_WORKSPACE = "user_profile_workspace"


class KonquestMissionEnrollmentExport(ExportBuilder):
    profile_filter_fields = ["director__in", "manager__in", "area_of_activity__in"]
    formatters_by_column = {
        "date_enroll": DateFormatter,
        "date_start": DateFormatter,
        "date_end": DateFormatter,
        "report_date": DateFormatter,
    }

    def __init__(
        self,
        xlsx_creator: ExportXlsxCreator,
        csv_creator: ExportCsvCreator,
        query_selector: KonquestExportQuery,
        account_selector: AccountExportQuery,
        formatter_service: FormatterService,
        report_name: str = None
    ):
        super().__init__(xlsx_creator, csv_creator, query_selector, formatter_service, report_name)
        self._main_query = query_selector
        self._account_query = account_selector
        self._workspace_id = None
        self._filters = {}

    def generate(self, report: Report) -> Report:
        self._workspace_id = report.workspace_id
        self._filters = report.filters
        return super().generate(report)

    def _load_parts(self) -> None:
        self._parts = [
            ExportPart(
                name="raw",
                data_lambda=self._get_main_data,
                index=False
            ),
            ExportPart(
                name="enrollments finished by user",
                data_lambda=self._load_data_by_user,
            ),
            ExportPart(
                name="enrollments by country",
                data_lambda=self._load_data_by_country
            ),
            ExportPart(
                name="enrollments by provider",
                data_lambda=self._load_data_by_mission_provider
            ),
            ExportPart(
                name="enrollments by user",
                data_lambda=self._load_data_statics_by_user
            ),
            ExportPart(
                name="enrollments by leader",
                data_lambda=self._load_data_statics_by_leader
            ),
            ExportPart(
                name="enrollments by director",
                data_lambda=self._load_data_statics_by_director
            ),
            ExportPart(
                name="enrollments by manager",
                data_lambda=self._load_data_statics_by_manager
            ),
            ExportPart(
                name="enrollments by area",
                data_lambda=self._load_data_statics_by_area_of_activity
            ),
            ExportPart(
                name="enrollments by internal code",
                data_lambda=self._load_data_statics_by_internal_code
            ),
            ExportPart(
                name="totalizers",
                data_lambda=self._load_data_statics_totalizers,
                index=False,
                by_line=True
            ),
        ]

    def load_main_data(self) -> DataFrame:
        user_profile_workspace_filter = self._get_profile_filter() if self._filters else None
        users = self._account_query.list_users(self._workspace_id, filters=user_profile_workspace_filter)
        if user_profile_workspace_filter:
            if users.empty:
                return DataFrame([])
            self._filters["user_id__in"] = [str(user_id) for user_id in users["user_id"].values.tolist()]
        data = self._main_query.list_mission_enrollments(
            self._workspace_id, Config.KONQUEST_EXPORT_MISSION_ENROLLMENT_LIMIT_DAYS, self._filters
        )
        if not data.empty:
            data["performance"] = pd.to_numeric(data["performance"])
            if not users.empty:
                data = data.merge(users[["user_email", "user_cpf"]], on="user_email", how="left")
        return data

    def _get_profile_filter(self) -> Optional[dict]:
        if USER_PROFILE_WORKSPACE not in self._filters:
            return
        request_filter = self._filters.pop(USER_PROFILE_WORKSPACE)
        profile_filter = {}
        for field in self.profile_filter_fields:
            profile_filter[f"{USER_PROFILE_WORKSPACE}__{field}"] = request_filter.get(field, [])
        return profile_filter

    def _get_main_data(self) -> DataFrame:
        return self._main_data

    def _load_data_by_learning_trail(self) -> DataFrame:
        user_profile_workspace_filter = self._get_profile_filter() if self._filters else None
        if user_profile_workspace_filter:
            users = self._account_query.list_users(self._workspace_id, filters=user_profile_workspace_filter)
            if users.empty:
                return DataFrame([])
            self._filters["user_id__in"] = [str(user_id) for user_id in users["user_id"].values.tolist()]
        data = self._main_query.list_learning_trail_enrollments(
            self._workspace_id, Config.KONQUEST_EXPORT_MISSION_ENROLLMENT_LIMIT_DAYS, self._filters
        )

        if 'mission_completion_rate' in data.columns:
            data['mission_completion_rate'] = data['mission_completion_rate'].apply(lambda x: f"{x:.0%}")
        return data

    def _load_data_by_user(self) -> DataFrame:
        indexes = [
            'user_name',
            'user_country',
            'user_leader',
            'mission_name',
            'date_end'
        ]
        return self.pivot_table(
            self._main_data,
            values=['performance'],
            index=indexes,
            margins=True,
            margins_name='Average'
        )

    def _load_data_by_country(self) -> DataFrame:
        return self._reshape_data_by_column("user_country")

    def _load_data_by_mission_provider(self) -> DataFrame:
        return self._reshape_data_by_column("mission_provider")

    def _load_data_statics_by_user(self) -> DataFrame:
        return self._reshape_data_by_column("user_email")

    def _load_data_statics_by_leader(self) -> DataFrame:
        return self._reshape_data_by_column("user_leader")

    def _load_data_statics_by_director(self) -> DataFrame:
        return self._reshape_data_by_column("user_director")

    def _load_data_statics_by_manager(self) -> DataFrame:
        return self._reshape_data_by_column("user_manager")

    def _load_data_statics_by_area_of_activity(self) -> DataFrame:
        return self._reshape_data_by_column("user_area_of_activity")

    def _load_data_statics_by_internal_code(self) -> DataFrame:
        return self._reshape_data_by_column("internal_code")

    def _load_data_statics_totalizers(self) -> DataFrame:
        return self._reshape_data_by_column("")

    @staticmethod
    def _is_empty_series(series: Series) -> bool:
        return series.isnull().sum() == len(series)

    def _reshape_data_by_column(self, column: str) -> DataFrame:
        main_data = self._main_data.copy()
        if column == "":
            main_data['__all__'] = ""
            column = "__all__"
        is_empty_column = self._is_empty_series(main_data[column])
        if is_empty_column:
            return DataFrame()
        column_names = {
            "ENROLLED": "enrollments_not_started",
            "COMPLETED": "enrollments_completed",
            "STARTED": "enrollments_started",
            "REPROVED": "enrollments_reproved",
            "EXPIRED": "enrollments_expired",
            "REFUSED": "enrollments_refused",
            "PENDING_VALIDATION": "enrollments_pending_validation",
            "GIVE_UP": "enrollments_give_up",
            "GIVE UP": "enrollments_give_up",
            "REQUEST_EXTENSION":"enrollments_request_extension",
            "WAITING_APPROVAL":"enrollments_waiting_approval",
            "NOT GIVE UP": "enrollments_not_give_up",
            "INACTIVATED": "enrollments_inactivated",
        }

        value_counts = main_data[column].value_counts()
        data = value_counts.to_frame()
        data.columns = ['enrollments']
        data.index.rename(column, inplace=True)

        status_count = self.pivot_table(
            data=main_data,
            index=[column],
            columns=['enrollment_status'],
            aggfunc='size',
            fill_value=0
        )
        status_count = status_count.rename(columns=column_names)
        status_count = status_count.merge(data, how='inner', on=column)

        unique_emails = main_data.groupby(column)['user_email'].nunique().to_frame()
        unique_emails.columns = ['unique_user_emails']

        unique_emails_active = main_data[main_data['mission_enrollment_active'] == True].groupby(column)['user_email'].nunique().to_frame()  # noqa: E712

        unique_emails_active.columns = ['unique_emails_with_enrollment_active']

        status_count = status_count.merge(unique_emails, how='inner', on=column)
        status_count = status_count.merge(unique_emails_active, how='left', on=column)

        try:
            status_count['enrollment_tx_done'] = status_count.apply(
                lambda x: calc_rate(x["enrollments_completed"], x['enrollments']), axis=1
            )
        except KeyError:
            status_count['enrollment_tx_done'] = status_count.apply(lambda x: "0%", axis=1)

        try:
            status_count['completed_rate'] = status_count.apply(
                lambda x: (x['enrollments_completed'] + x['enrollments_reproved'])
                / x['enrollments'] if (x['enrollments_completed'] + x['enrollments_reproved']) > 0 else 0, axis=1
            )
        except KeyError:
            status_count['completed_rate'] = status_count.apply(lambda x: "0%", axis=1)

        try:
            status_count['aproved_rate'] = status_count.apply(
                lambda x: (x['enrollments_completed'])
                / x['enrollments'] if (x['enrollments_completed']) > 0 else 0, axis=1
            )
        except KeyError:
            status_count['aproved_rate'] = status_count.apply(lambda x: "0%", axis=1)


        try:
            status_count['reproved_rate'] = status_count.apply(
                lambda x: x['enrollments_reproved']
                / x['enrollments'] if x['enrollments_reproved'] > 0 else 0, axis=1
            )
        except KeyError:
            status_count['reproved_rate'] = status_count.apply(lambda x: "0%", axis=1)

        try:
            status_count['give_up_rate'] = status_count.apply(
                lambda x: x['enrollments_give_up'] / x['enrollments'] if x['enrollments'] > 0 else 0, axis=1
            )
        except KeyError:
            status_count['give_up_rate'] = status_count.apply(lambda x: "0%", axis=1)



        durations_sum = self.pivot_table(
            data=main_data,
            index=[column],
            values=['mission_duration', 'estimated_consume'],
            aggfunc='sum'
        ).round(decimals=2)
        durations_sum = durations_sum.add_suffix('_sum')

        consume_time_avg = self.pivot_table(
            data=main_data,
            index=[column],
            values=['mission_duration', 'estimated_consume'],
            aggfunc=pd.Series.mean
        ).round(decimals=2)

        ratings_mean = self.pivot_table(
            data=main_data.loc[main_data['enrollment_status'] == "COMPLETED"],
            index=[column],
            values=['mission_nps', 'rating'],
            fill_value=0,
            aggfunc=pd.Series.mean
        ).round(decimals=2)

        average_values = consume_time_avg.merge(ratings_mean, on=column, how='inner')
        consume_time_avg = average_values.add_suffix('_avg')
        consume_time = durations_sum.merge(consume_time_avg, on=column, how='inner')


        return status_count.merge(consume_time, how='inner', on=column)
