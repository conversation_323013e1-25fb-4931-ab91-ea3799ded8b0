import pandas as pd
from domain.report.models import Report
from pandas import DataFrame
from reports.export import ExportBuilder, ExportCsvCreator, ExportPart, ExportXlsxCreator
from reports.formatters.date_formatter import DateFormatter
from reports.formatters.formatter_service import FormatterService
from reports.formatters.percent_format import PercentFormatter
from reports.query.konquest.export import KonquestExportQuery
from reports.utils import calc_float_rate, convert_timedelta_to_str

MISSION_ID = 'mission_id'
ENROLLMENTS = 'enrollments'
ENROLLMENTS_ENROLLED = 'mission.enrollments_enrolled'
ENROLLMENTS_STARTED = 'mission.enrollments_started'
ENROLLMENTS_GIVE_UP = 'mission.enrollments_give_up'
ENROLLMENTS_REPROVED = 'mission.enrollments_reproved'
ENROLLMENTS_EXPIRED = 'mission.enrollments_expired'
ENROLLMENTS_DONE = 'mission.enrollments_done'
ENROLLMENTS_INACTIVATED = 'mission.enrollments_inactivated'
ENROLLMENTS_REQUEST_EXTENSION = 'mission.enrollments_request_extension'
ENROLLMENTS_REFUSED = 'mission.enrollments_refused'
ENROLLMENTS_PENDING_VALIDATION = 'mission.enrollments_pending_validation'
ENROLLMENTS_WAITING_APPROVAL = 'mission.enrollments_waiting_approval'
ENROLLMENTS_WAITING_VACANCIES = 'mission.enrollments_waiting_vacancies'
DURATION = 'duration'


class KonquestMissionExport(ExportBuilder):
    formatters_by_column = {
        "mission_updated_date": DateFormatter,
        "mission_created_date": DateFormatter,
        "mission_deleted_date": DateFormatter,
        "enrollments_done_rate": PercentFormatter,
        "enrollments_reproved_rate": PercentFormatter,
        "enrollments_give_up_rate": PercentFormatter,
        "completion_rate": PercentFormatter,
    }
    aggregation_columns = {
        'enrollments_done_rate':
            lambda main_df: calc_float_rate(main_df[ENROLLMENTS_DONE], main_df[ENROLLMENTS]),
        'enrollments_reproved_rate':
            lambda main_df: calc_float_rate(main_df[ENROLLMENTS_REPROVED], main_df[ENROLLMENTS]),
        'enrollments_give_up_rate':
            lambda main_df: calc_float_rate(main_df[ENROLLMENTS_GIVE_UP], main_df[ENROLLMENTS]),
        'completion_rate':
            lambda main_df: calc_float_rate(
                main_df[ENROLLMENTS_DONE] + main_df[ENROLLMENTS_REPROVED], main_df[ENROLLMENTS]
            ),
    }

    def __init__(
        self,
        xlsx_creator: ExportXlsxCreator,
        csv_creator: ExportCsvCreator,
        query_selector: KonquestExportQuery,
        formatter_service: FormatterService,
        report_name: str = None
    ):
        super().__init__(xlsx_creator, csv_creator, query_selector, formatter_service, report_name)
        self._main_query = query_selector
        self._workspace_id = None
        self._filters = {}

    def generate(self, report: Report) -> Report:
        self._workspace_id = report.workspace_id
        self._filters = report.filters
        return super().generate(report)

    def _load_parts(self) -> None:
        self._parts = [
            ExportPart(
                name="raw",
                data_lambda=self.get_main_data,
                index=False
            ),
            ExportPart(
                name="missions by creator",
                data_lambda=self.load_data_by_creator_data
            ),
            ExportPart(
                name="missions stats",
                data_lambda=self.load_stats_data,
                index=False
            ),
        ]

    def load_data_by_creator_data(self) -> DataFrame:
        enrollment_values = [
            'enrollments_done_rate',
            'enrollments_reproved_rate',
            'enrollments_give_up_rate',
            ENROLLMENTS,
            ENROLLMENTS_DONE,
            ENROLLMENTS_ENROLLED,
            ENROLLMENTS_STARTED,
            ENROLLMENTS_REPROVED,
            ENROLLMENTS_EXPIRED,
            ENROLLMENTS_GIVE_UP,
            ENROLLMENTS_INACTIVATED,
            ENROLLMENTS_REQUEST_EXTENSION,
            ENROLLMENTS_REFUSED,
            ENROLLMENTS_PENDING_VALIDATION,
            ENROLLMENTS_WAITING_APPROVAL,
            ENROLLMENTS_WAITING_VACANCIES,
            DURATION
        ]
        duration_values = [DURATION]
        indexes = ['user_creator_name', 'mission_name', 'completion_rate']

        enrollments = pd.pivot_table(self._main_data, values=enrollment_values, index=indexes)
        durations = pd.pivot_table(self._main_data, values=duration_values, index=indexes, aggfunc=pd.Series.mean)
        pivot = enrollments.merge(durations, on=indexes, how='left')
        return pivot

    def load_data_by_group(self) -> DataFrame:
        pivot = self.pivot_table_with_subtotal(
            data_frame=self._main_data,
            index=['group_name', 'mission_name', 'completion_rate'],
            values=[
                'enrollments_done_rate',
                'enrollments_reproved_rate',
                'enrollments_give_up_rate',
                ENROLLMENTS,
                ENROLLMENTS_DONE,
                ENROLLMENTS_ENROLLED,
                ENROLLMENTS_STARTED,
                ENROLLMENTS_REPROVED,
                ENROLLMENTS_EXPIRED,
                ENROLLMENTS_GIVE_UP,
                ENROLLMENTS_INACTIVATED,
                ENROLLMENTS_REQUEST_EXTENSION,
                ENROLLMENTS_REFUSED,
                ENROLLMENTS_PENDING_VALIDATION,
                ENROLLMENTS_WAITING_APPROVAL,
                ENROLLMENTS_WAITING_VACANCIES,
                DURATION
            ],
            prefix_margin='Total'
        )
        return pivot

    def load_main_data(self) -> DataFrame:
        missions = self._main_query.list_missions(self._workspace_id, None, self._filters)
        if not missions.empty:
            for column_name in self.aggregation_columns:
                missions[column_name] = missions.apply(self.aggregation_columns[column_name], axis=1)
        return missions

    def load_stats_data(self) -> DataFrame:
        df_missions = self._main_data.drop_duplicates(subset=MISSION_ID, keep='first')
        total_contents = convert_timedelta_to_str(df_missions[DURATION].sum())
        stats = {
            "mission_counters": [
                'total_missions',
                'total_content',
                'total_enrollments',
                'total_enrollments_done',
                'total_enrollments_enrolled',
                'total_enrollments_started',
                'total_enrollments_give_up',
                'total_enrollments_reproved',
                'total_enrollments_expired',
                'total_enrollments_inactivated',
                'total_enrollments_pending_validation',
                'total_enrollments_refused',
                'total_enrollments_request_extension',
                'total_enrollments_waiting_approval'
            ],
            "Total": [
                df_missions[MISSION_ID].count(),
                total_contents,
                df_missions[ENROLLMENTS].sum(),
                df_missions[ENROLLMENTS_DONE].sum(),
                df_missions[ENROLLMENTS_ENROLLED].sum(),
                df_missions[ENROLLMENTS_STARTED].sum(),
                df_missions[ENROLLMENTS_GIVE_UP].sum(),
                df_missions[ENROLLMENTS_REPROVED].sum(),
                df_missions[ENROLLMENTS_EXPIRED].sum(),
                df_missions[ENROLLMENTS_INACTIVATED].sum(),
                df_missions[ENROLLMENTS_PENDING_VALIDATION].sum(),
                df_missions[ENROLLMENTS_REFUSED].sum(),
                df_missions[ENROLLMENTS_REQUEST_EXTENSION].sum(),
                df_missions[ENROLLMENTS_WAITING_APPROVAL].sum()
            ]
        }

        stats_data = DataFrame(stats)
        return stats_data

    def get_main_data(self) -> DataFrame:
        return self._main_data
