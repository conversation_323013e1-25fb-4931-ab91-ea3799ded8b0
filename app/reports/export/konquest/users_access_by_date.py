from config.default import Config
from domain.report.models import Report
from pandas import Data<PERSON>rame
from reports.export import Export<PERSON>uilder, ExportCsvCreator, ExportPart, ExportXlsxCreator
from reports.formatters.date_formatter import DateFormatter
from reports.formatters.date_time_HM_formatter import DateTimeHMFormatter
from reports.formatters.formatter_service import FormatterService
from reports.query.konquest.export import KonquestExportQuery

ACTIVE_USERS = "users_access_by_date.active_users"
ENABLED_USERS = "enabled_users"


class KonquestUsersAccessByDateExport(ExportBuilder):
    formatters_by_column = {
        "date_activity": DateTimeHMFormatter,
        "last_access": DateTimeHMFormatter,
        "last_activity": DateTimeHMFormatter
    }

    def __init__(
        self,
        xlsx_creator: ExportXlsxCreator,
        csv_creator: ExportCsvCreator,
        query_selector: KonquestExportQuery,
        formatter_service: FormatterService,
        report_name: str = None
    ):
        super().__init__(xlsx_creator, csv_creator, query_selector, formatter_service, report_name)
        self._main_query = query_selector
        self._workspace_id = None
        self._filters = {}
        self._date_formatter = DateFormatter()

    def generate(self, report: Report) -> Report:
        self._workspace_id = report.workspace_id
        self._filters = report.filters
        return super().generate(report)

    def load_main_data(self) -> DataFrame:
        users_access = self._main_query.list_users_access_by_date(
            self._workspace_id,
            limit_filter_date_range=Config.KONQUEST_EXPORT_USER_ACCESS_BY_DATE_LIMIT_DAYS,
            filters=self._filters
        )
        if users_access.empty:
            return users_access
        return users_access

    def _load_by_month_data(self) -> DataFrame:
        self._main_data['date_activity__only_date'] = self._main_data['date_activity'].apply(
            lambda row: row.date()
        )

        data = self.pivot_table(
            self._main_data,
            index=['date_activity_year', 'date_activity_month', 'user_email', 'user_name'],
            values=['date_activity__only_date'],
            aggfunc=lambda x: len(x.unique())
        )

        self._main_data.drop(['date_activity__only_date'], inplace=True, axis=1)
        data = data.rename({'date_activity__only_date': 'active_days'}, axis=1)
        return data

    def _load_data_by_director(self) -> DataFrame:
        return self._reshape_data_by_column('user_director')

    def _load_data_by_manager(self) -> DataFrame:
        return self._reshape_data_by_column('user_manager')

    def _load_data_by_area_of_activity(self) -> DataFrame:
        return self._reshape_data_by_column('user_area_of_activity')

    def _load_parts(self) -> None:
        self._parts = [
            ExportPart(
                name="raw",
                data_lambda=self._get_main_data,
                index=False
            ),
            ExportPart(name="user_active_user_by_month", data_lambda=self._load_by_month_data),
            ExportPart(name="users_by_director", data_lambda=self._load_data_by_director),
            ExportPart(name="users_by_manager", data_lambda=self._load_data_by_manager),
            ExportPart(name="users_by_area", data_lambda=self._load_data_by_area_of_activity),
        ]

    def _reshape_data_by_column(self, column: str) -> DataFrame:
        is_empty_column = self._is_empty_series(self._main_data[column])
        if is_empty_column:
            return DataFrame()

        users_enabled_column = f"{column}_users_enabled"

        if users_enabled_column not in self._main_data.columns:
            raise ValueError(f"The column '{users_enabled_column}' does not exist in the dataset.")

        df_grouped_by_users = self._main_data.drop_duplicates(subset=["user_email"])

        active_users_counts = df_grouped_by_users[column].value_counts()
        users_enabled_counts = self._main_data.groupby(column)[users_enabled_column].max()

        data = DataFrame({
            ACTIVE_USERS: active_users_counts,
            ENABLED_USERS: users_enabled_counts
        })

        data.fillna(0, inplace=True)
        data.index.rename(column, inplace=True)

        return data
