import pandas as pd
from config.default import Config
from domain.report.models import Report
from pandas import DataFrame
from reports.export import ExportBuilder, ExportCsvCreator, ExportPart, ExportXlsxCreator
from reports.formatters.date_time_HM_formatter import DateTimeHMFormatter
from reports.formatters.formatter_service import FormatterService
from reports.formatters.percent_format import PercentFormatter
from reports.query.konquest.export import KonquestExportQuery
from reports.utils import calc_float_rate


class KonquestGroupsMissionsUsersExport(ExportBuilder):
    formatters_by_column = {
        "date_start": DateTimeHMFormatter,
        "date_end": DateTimeHMFormatter,
        "enrollments_done_rate": PercentFormatter,
        "enrollments_reproved_rate": PercentFormatter,
        "enrollments_give_up_rate": PercentFormatter,
        "completion_rate": PercentFormatter,
        "performance": PercentFormatter,
        "group_engagement_rate": PercentFormatter,
    }
    sub_aggregation_columns = {
        "enrollments_done_rate":
            lambda main_df: calc_float_rate(main_df["enrollments_completed"], main_df["enrollments"]),
        "enrollments_reproved_rate":
            lambda main_df: calc_float_rate(main_df["enrollments_reproved"], main_df["enrollments"]),
        "enrollments_give_up_rate":
            lambda main_df: calc_float_rate(main_df["enrollments_give_up"], main_df["enrollments"]),
        "completion_rate":
            lambda main_df: calc_float_rate(
                main_df["enrollments_completed"] + main_df["enrollments_reproved"], main_df["enrollments"]
            ),
    }

    def __init__(
        self,
        xlsx_creator: ExportXlsxCreator,
        csv_creator: ExportCsvCreator,
        query_selector: KonquestExportQuery,
        formatter_service: FormatterService,
        report_name: str = None
    ):
        super().__init__(xlsx_creator, csv_creator, query_selector, formatter_service, report_name)
        self._main_query = query_selector
        self._workspace_id = None
        self._filters = {}

    def generate(self, report: Report) -> Report:
        self._workspace_id = report.workspace_id
        self._filters = report.filters
        return super().generate(report)

    def load_main_data(self) -> DataFrame:
        data = self._main_query.list_groups_missions_users(
            self._workspace_id, Config.KONQUEST_EXPORT_GROUPS_MISSIONS_USERS_LIMIT_DAYS, self._filters
        )
        if not data.empty:
            data["group_engagement_rate"] = data.apply(
                lambda row: calc_float_rate(row["group_users_enrolled"], row["users_linked_in_the_group"]), axis=1
            )

            data.drop(["group_id", "mission_id"], axis=1, inplace=True)

        return data

    def _load_missions_by_group(self) -> DataFrame:
        enrollments_by_mission = self._reshape_data_by_column()
        return self.pivot_table(
            enrollments_by_mission,
            values=[
                "completion_rate",
                "enrollments_done_rate",
                "enrollments_reproved_rate",
                "enrollments_give_up_rate",
                "enrollments",
                "enrollments_completed",
                "enrollments_not_started",
                "enrollments_started",
                "enrollments_reproved",
                "enrollments_expired",
                "enrollments_give_up",
                "enrollments_inactivated",
                "enrollments_request_extension",
                "enrollments_refused",
                "enrollments_pending_validation",
                "enrollments_waiting_approval",
                "enrollments_inactivated"
            ],
            index=["group_name", "mission_name"],
            aggfunc="sum",
            fill_value=0
        )

    def _load_enrollments_by_user(self) -> DataFrame:
        return self.pivot_table(
            self._main_data,
            values=["performance"],
            index=["group_name", "user_email", "user_name"],
            fill_value=0
        )

    def _load_parts(self) -> None:
        self._parts = [
            ExportPart(
                name="raw",
                data_lambda=self._get_main_data,
                index=False
            ),
            ExportPart(
                name="mission_by_group",
                data_lambda=self._load_missions_by_group,
                index=True
            ),
            ExportPart(
                name="users performances by group",
                data_lambda=self._load_enrollments_by_user,
                index=True
            ),
        ]

    def _reshape_data_by_column(self) -> DataFrame:
        group_by = ["group_name", "mission_name"]
        enrollment_statuses = {
            "ENROLLED": "enrollments_not_started",
            "COMPLETED": "enrollments_completed",
            "STARTED": "enrollments_started",
            "REPROVED": "enrollments_reproved",
            "EXPIRED": "enrollments_expired",
            "REFUSED": "enrollments_refused",
            "PENDING_VALIDATION": "enrollments_pending_validation",
            "GIVE_UP": "enrollments_give_up",
            "REQUEST_EXTENSION": "enrollments_request_extension",
            "WAITING_APPROVAL": "enrollments_waiting_approval",
            "WAITING_VACANCIES": "mission.enrollments_waiting_vacancies",
            "INACTIVATED": "enrollments_inactivated",
        }
        enrollments_count = (
            self._main_data[self._main_data["enrollment_status"].notna()]
            .groupby(group_by)
            .size()
            .reset_index(name="enrollments")
        )
        result = enrollments_count

        for enrollment_status, column_name in enrollment_statuses.items():
            enrollment_count_by_status = (
                self._main_data[self._main_data["enrollment_status"] == enrollment_status]
                .groupby(group_by)
                .size()
                .reset_index(name=column_name)
            )
            enrollment_count_by_status.fillna(0, inplace=True)

            result = pd.merge(
                result, enrollment_count_by_status, on=group_by, how="left", validate="many_to_many"
            )

        for column_name in self.sub_aggregation_columns:
            result[column_name] = result.apply(self.sub_aggregation_columns[column_name], axis=1)

        return result
