from typing import Optional

import pandas as pd
from config.default import Config
from domain.report.models import Report
from pandas import DataFrame
from reports.export import (
    ExportBuilder,
    ExportCsvCreator,
    ExportPart,
    ExportXlsxCreator,
)
from reports.export.konquest.mission_enrollment import USER_PROFILE_WORKSPACE
from reports.formatters.date_formatter import DateFormatter
from reports.formatters.formatter_service import FormatterService
from reports.formatters.interval_formatter import IntervalFormatter
from reports.formatters.percent_format import PercentFormatter
from reports.query.account.export import AccountExportQuery
from reports.query.konquest.export import KonquestExportQuery
from reports.utils import calc_rate


class KonquestTrailEnrollmentExport(ExportBuilder):
    profile_filter_fields = ["director__in", "manager__in", "area_of_activity__in"]
    formatters_by_column = {
        "date_enroll": DateFormatter,
        "date_start": DateFormatter,
        "goal_date": DateFormatter,
        "date_end": DateFormatter,
        "sum_estimated_consume": IntervalFormatter,
        "performance": PercentFormatter,
        "progress": PercentFormatter,
    }

    def __init__(
        self,
        xlsx_creator: ExportXlsxCreator,
        csv_creator: ExportCsvCreator,
        query_selector: KonquestExportQuery,
        formatter_service: FormatterService,
        account_selector: AccountExportQuery,
        report_name: str = None,
    ):
        super().__init__(
            xlsx_creator, csv_creator, query_selector, formatter_service, report_name
        )
        self._main_query = query_selector
        self._workspace_id = None
        self._filters = {}
        self._account_query = account_selector

    def generate(self, report: Report) -> Report:
        self._workspace_id = report.workspace_id
        self._filters = report.filters
        return super().generate(report)

    def _load_parts(self) -> None:
        self._parts = [
            ExportPart(name="raw", data_lambda=self.get_main_data, index=False),
            ExportPart(
                name="enrollments by director",
                data_lambda=self._load_data_statics_by_director,
            ),
            ExportPart(
                name="enrollments by manager",
                data_lambda=self._load_data_statics_by_manager,
            ),
            ExportPart(
                name="enrollments by leader",
                data_lambda=self._load_data_statics_by_user_leader,
            ),
            ExportPart(
                name="enrollments by area",
                data_lambda=self._load_data_statics_by_area_of_activity,
            ),
            ExportPart(
                name="enrollments by country",
                data_lambda=self._load_data_statics_by_country,
            ),
            ExportPart(
                name="by director and trail",
                data_lambda=self._load_data_statics_by_director_and_and_trail,
            ),
            ExportPart(
                name="by manager and trail",
                data_lambda=self._load_data_statics_by_manager_and_trail,
            ),
            ExportPart(
                name="by area and trail",
                data_lambda=self._load_data_statics_by_area_of_activity_and_trail,
            ),
            ExportPart(
                name="by leader and trail",
                data_lambda=self._load_data_statics_by_user_leader_and_trail,
            ),
            ExportPart(
                name="by country and trail",
                data_lambda=self._load_data_statics_by_user_country_and_trail,
            ),
            ExportPart(
                name="totalizers",
                data_lambda=self._load_data_statics_totalizers,
                index=False,
                by_line=True,
            ),
        ]

    def load_main_data(self) -> DataFrame:
        user_profile_workspace_filter = (
            self._get_profile_filter() if self._filters else None
        )
        users = self._account_query.list_users(
            self._workspace_id, filters=user_profile_workspace_filter
        )
        if user_profile_workspace_filter:
            if users.empty:
                return DataFrame([])
            self._filters["user_id__in"] = [
                str(user_id) for user_id in users["user_id"].values.tolist()
            ]
        trails_enrollments = self._main_query.list_trails_enrollments(
            self._workspace_id,
            Config.KONQUEST_EXPORT_MISSION_ENROLLMENT_LIMIT_DAYS,
            self._filters,
        )

        if not trails_enrollments.empty and not users.empty:
            trails_enrollments = trails_enrollments.merge(
                users[["user_email", "user_cpf"]], on="user_email", how="left"
            )
        return trails_enrollments

    def _get_profile_filter(self) -> Optional[dict]:
        if USER_PROFILE_WORKSPACE not in self._filters:
            return
        request_filter = self._filters.pop(USER_PROFILE_WORKSPACE)
        profile_filter = {}
        for field in self.profile_filter_fields:
            profile_filter[f"{USER_PROFILE_WORKSPACE}__{field}"] = request_filter.get(
                field, []
            )
        return profile_filter

    def _agregate_by_colums(self, data: DataFrame, column: list) -> DataFrame:
        main_data = self._main_data.copy()

        column_names = {
            "ENROLLED": "enrollments_not_started_in_trails",
            "COMPLETED": "enrollments_completed_in_trails",
            "STARTED": "trail_enrollments_started_in_trails",
            "REPROVED": "trail_enrollments_reproved_in_trails",
            "REFUSED": "enrollments_refused_in_trails",
            "PENDING_VALIDATION": "enrollments_pending_validation_in_trails",
            "GIVE UP": "enrollments_give_up_in_trails",
            "GIVE_UP": "enrollments_give_up_in_trails",
            "NOT GIVE UP": "enrollments_not_give_up_in_trails",
            "INACTIVATED": "enrollments_inactivated_in_trails",
            "EXPIRED": "enrollments_expired_in_trails",
        }

        if len(column) == 1 and column[0] == "totalizers":
            main_data["__all__"] = ""
            column = ["__all__"]

        value_counts = main_data.groupby(column).size()
        if value_counts.empty:
            return DataFrame()
        aggregated_data = value_counts.to_frame()
        aggregated_data.columns = ["trail_enrollments"]

        status_count = self.pivot_table(
            data=main_data,
            index=column,
            columns=["enrollment_status"],
            aggfunc="size",
            fill_value=0,
        )

        status_count = status_count.rename(columns=column_names)
        status_count = status_count.merge(aggregated_data, how="inner", on=column)
        try:
            status_count["trail_enrollment_tx_done"] = status_count.apply(
                lambda x: calc_rate(x["enrollments_completed"], x["trail_enrollments"]),
                axis=1,
            )
        except KeyError:
            status_count["trail_enrollment_tx_done"] = status_count.apply(
                lambda x: "0%", axis=1
            )

        main_data["sum_estimated_consume"] = pd.to_timedelta(
            main_data["sum_estimated_consume"]
        )

        values_sum = main_data.groupby(column).agg(
            {
                "duration_trail": "sum",
                "points": "sum",
                "learning_trail_points": "sum",
                "sum_estimated_consume": "sum",
            }
        ).add_suffix("_sum")

        values_mean = main_data.groupby(column).agg(
            {
                "duration_trail": "mean",
                "sum_estimated_consume": "mean",
                "mission_rating_avg": "mean",
                "mission_nps_avg": "mean",
                "trail_mission_duration_time_avg": "mean",
            }
        ).add_suffix("_mean")
        values_mean = values_mean.rename(
            columns={"mission_rating_avg_mean": "trail_mission_rating_avg_mean"}
        )

        status_count = status_count.merge(values_sum, how="inner", on=column)
        status_count = status_count.merge(values_mean, how="inner", on=column)

        return status_count

    def _load_data_statics_by_director(self) -> DataFrame:
        aggregated_data = self._agregate_by_colums(self._main_data, ["user_director"])
        return aggregated_data

    def _load_data_statics_by_manager(self) -> DataFrame:
        aggregated_data = self._agregate_by_colums(self._main_data, ["user_manager"])
        return aggregated_data

    def _load_data_statics_by_user_leader(self) -> DataFrame:
        aggregated_data = self._agregate_by_colums(self._main_data, ["user_leader"])
        return aggregated_data

    def _load_data_statics_by_area_of_activity(self) -> DataFrame:
        aggregated_data = self._agregate_by_colums(
            self._main_data, ["user_area_of_activity"]
        )
        return aggregated_data

    def _load_data_statics_by_country(self) -> DataFrame:
        aggregated_data = self._agregate_by_colums(self._main_data, ["user_country"])
        return aggregated_data

    def _load_data_statics_by_director_and_and_trail(self) -> DataFrame:
        aggregated_data = self._agregate_by_colums(
            self._main_data, ["user_director", "trail_name"]
        )
        return aggregated_data

    def _load_data_statics_by_manager_and_trail(self) -> DataFrame:
        aggregated_data = self._agregate_by_colums(
            self._main_data, ["user_manager", "trail_name"]
        )
        return aggregated_data

    def _load_data_statics_by_area_of_activity_and_trail(self) -> DataFrame:
        aggregated_data = self._agregate_by_colums(
            self._main_data, ["user_area_of_activity", "trail_name"]
        )
        return aggregated_data

    def _load_data_statics_by_user_leader_and_trail(self) -> DataFrame:
        aggregated_data = self._agregate_by_colums(
            self._main_data, ["user_leader", "trail_name"]
        )
        return aggregated_data

    def _load_data_statics_by_user_country_and_trail(self) -> DataFrame:
        aggregated_data = self._agregate_by_colums(
            self._main_data, ["user_country", "trail_name"]
        )
        return aggregated_data

    def _load_data_statics_totalizers(self) -> DataFrame:
        aggregated_data = self._agregate_by_colums(self._main_data, ["totalizers"])
        return aggregated_data
