import unittest
from unittest.mock import MagicMock
from domain.report.models import Report
from pandas import DataFrame
import pandas as pd
from reports.export import ExportXlsxCreator, ExportCsvCreator
from reports.export.konquest.users_access_by_date import KonquestUsersAccessByDateExport
from reports.formatters.formatter_service import FormatterService
from reports.query.konquest.export import KonquestExportQuery


class TestKonquestUsersAccessByDateExport(unittest.TestCase):

    def setUp(self):
        self.xlsx_creator = MagicMock(spec=ExportXlsxCreator)
        self.csv_creator = MagicMock(spec=ExportCsvCreator)
        self.query_selector = MagicMock(spec=KonquestExportQuery)
        self.formatter_service = MagicMock(spec=FormatterService)
        self.report_name = "Test Report"

        self.export_builder = KonquestUsersAccessByDateExport(
            xlsx_creator=self.xlsx_creator,
            csv_creator=self.csv_creator,
            query_selector=self.query_selector,
            formatter_service=self.formatter_service,
            report_name=self.report_name
        )

        self.report = MagicMock(spec=Report)
        self.report.workspace_id = "workspace_123"
        self.report.filters = {"filter_key": "filter_value"}

    def test_load_parts(self):
        self.export_builder._main_data = DataFrame(
            {"user_email": ["<EMAIL>"], "user_name": ["User A"], "date_activity": ["2021-01-01"]}
        )
        self.export_builder._load_parts()

        part_names = [part.name for part in self.export_builder._parts]
        self.assertIn("raw", part_names)
        self.assertIn("user_active_user_by_month", part_names)
        self.assertIn("users_by_director", part_names)
        self.assertIn("users_by_manager", part_names)
        self.assertIn("users_by_area", part_names)

    def test_reshape_data_by_column(self):
        self.export_builder._main_data = DataFrame({
            "user_email": ["<EMAIL>", "<EMAIL>", "<EMAIL>"],
            "user_director": ["Director A", "Director A", "Director B"],
            "user_director_users_enabled": [10, 10, 20]
        })

        result = self.export_builder._reshape_data_by_column("user_director")
        expected_df = DataFrame(
            {"users_access_by_date.active_users": [2, 1], "enabled_users": [10, 20]},
            index=["Director A", "Director B"]
        )
        expected_df.index.name = "user_director"
        self.assertTrue(result.equals(expected_df))

    def test_reshape_data_by_column_with_empty_data(self):
        self.export_builder._main_data = DataFrame({"user_email": [], "user_director": []})
        result = self.export_builder._reshape_data_by_column("user_director")
        self.assertTrue(result.empty)

    def test_load_by_month_data(self):
        self.export_builder._main_data = DataFrame({
            "date_activity": ["2023-07-01 12:00", "2023-07-02 13:00", "2023-07-04 13:00", "2023-07-01 14:00"],
            "user_email": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"],
            "user_name": ["User A", "User B", "User B", "User A"],
            "date_activity_year": ["2023", "2023", "2023", "2023"],
            "date_activity_month": ["7", "7", "7", "7"]
        })
        self.export_builder._main_data["date_activity"] = self.export_builder._main_data["date_activity"].apply(
            pd.to_datetime
        )

        result = self.export_builder._load_by_month_data()

        expected_data = {
            ("2023", "7", "<EMAIL>", "User A"): {"active_days": 1},
            ("2023", "7", "<EMAIL>", "User B"): {"active_days": 2},
        }
        expected_df = DataFrame.from_dict(expected_data, orient="index", columns=["active_days"])
        expected_df.index.set_names(
            ["date_activity_year", "date_activity_month", "user_email", "user_name"],
            inplace=True
        )

        self.assertTrue(result.equals(expected_df))
