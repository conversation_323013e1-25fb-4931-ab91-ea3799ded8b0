import pytest
import pandas as pd
from datetime import datetime
import pytz
from unittest.mock import Mock
from reports.export.info_sheet_service import InfoSheetService

@pytest.fixture
def report():
    report_mock = Mock()
    report_mock.time_zone = "America/Sao_Paulo"
    report_mock.user_creator = {"email": "<EMAIL>"}
    report_mock.language = "pt-BR"
    return report_mock

@pytest.fixture
def writer():
    return pd.ExcelWriter('/tmp/test.xlsx', engine='xlsxwriter')

@pytest.fixture
def service():
    return InfoSheetService()

def test_get_values(service, report):
    values = service._get_values(report)
    timezone = pytz.timezone(report.time_zone)
    expected_date = datetime.now(timezone).strftime("%Y-%m-%d %H:%M:%S")
    assert values["created_date"] == expected_date
    assert values["user_creator_email"] == "<EMAIL>"

def test_get_formats(service, writer):
    workbook = writer.book
    default_format, uppercase_format, subtitle_format = service._get_formats(workbook)
    assert default_format
    assert uppercase_format
    assert subtitle_format

def test_replace_variables(service):
    values = {"created_date": "2023-01-01 12:00:00", "user_creator_email": "<EMAIL>"}
    assert service._replace_variables("{created_date}", values) == "2023-01-01 12:00:00"
    assert service._replace_variables("{user_creator_email}", values) == "<EMAIL>"
    assert service._replace_variables("no_placeholder", values) == "no_placeholder"

def test_get_cell_format(service):
    uppercase_format = Mock()
    subtitle_format = Mock()
    assert service._get_cell_format("HEADER", ["HEADER", "TITLE"], uppercase_format, subtitle_format) == subtitle_format
    assert service._get_cell_format("HEADER", ["header", "Title"], uppercase_format, subtitle_format) == uppercase_format
    assert service._get_cell_format("text", ["text", "subtitle"], uppercase_format, subtitle_format) is None

def test_add_info_sheet(service, writer, report):
    sheet_name = "TestSheet"
    report_name = "konquest-missions-enrollments-export"
    service.add_info_sheet(writer, report, report_name, sheet_name)
    writer.close()

    workbook = pd.ExcelFile('/tmp/test.xlsx')
    assert sheet_name in workbook.sheet_names