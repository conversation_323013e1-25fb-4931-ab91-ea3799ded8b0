from typing import Dict

import pytest
from Locales import Locales
from freezegun import freeze_time
from pandas import DataFrame

from config.default import Config
from custom.discord_webhook_logger import DiscordWebhookLogger
from domain.report.models import Report
from reports.abstracts.report_uploader_client_abc import ReportUploader<PERSON>lientABC
from reports.export import ExportCreator, ExportPart
from reports.formatters.formatter import Formatter

BUCKET = 'bucket'


class ReportUploaderClientStub(ReportUploaderClientABC):
    def upload_report(self, file_path, type_name, client='default', remove_after_upload=True):
        return {"url": f"https://{BUCKET}/{file_path}"}


class MyExportCreator(ExportCreator):
    def create(self, parts: [ExportPart], report_name: str, report: Report) -> str:
        return super().create(parts, report_name, report)

    def _generate_file(self, report:Report) -> str:
        return self._generate_file_path('xlsx')


@pytest.fixture
def export_creator():
    return MyExportCreator('xlsx', ReportUploaderClientStub(), Locales(Config.LOCALE_JSON), DiscordWebhookLogger())


def test_translate():
    locales = Locales(Config.LOCALE_JSON)
    locales.set_default_lang('pt-BR')
    export_creator = MyExportCreator('xlsx', ReportUploaderClientStub(), locales, DiscordWebhookLogger())
    data_frame = DataFrame([{"test": "Any Value"}])
    new_data_frame = export_creator.translate(data_frame)
    assert new_data_frame.columns[0] == "teste"


def test_translate_key_with_language_not_mapped(export_creator):
    data_frame = DataFrame([{"test": "Any Value"}])
    new_data_frame = export_creator.translate(data_frame)
    assert new_data_frame.columns[0] == "test"


@freeze_time("2022-01-20")
def test_report_file_name(export_creator, mocker):
    url = export_creator.create([], "test", Report(language="pt-BR", time_zone="UTC"))
    file_name = url.split('/')[-1]

    assert "teste-2022-01-20 00:00:00" in file_name
