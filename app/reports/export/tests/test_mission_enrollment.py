import unittest
from unittest.mock import MagicMock
from pandas import DataFrame
from reports.export.konquest.mission_enrollment import KonquestMissionEnrollmentExport

class TestKonquestMissionEnrollmentExport(unittest.TestCase):
    def setUp(self):
        self.xlsx_creator_mock = MagicMock()
        self.csv_creator_mock = MagicMock()
        self.query_selector_mock = MagicMock()
        self.account_selector_mock = MagicMock()
        self.formatter_service_mock = MagicMock()
        self.report_name = "Konquest Mission Enrollment Report"

        self.konquest_export = KonquestMissionEnrollmentExport(
            xlsx_creator=self.xlsx_creator_mock,
            csv_creator=self.csv_creator_mock,
            query_selector=self.query_selector_mock,
            account_selector=self.account_selector_mock,
            formatter_service=self.formatter_service_mock,
            report_name=self.report_name
        )

    def test_load_data_by_learning_trail_with_filters(self):
        self.konquest_export._workspace_id = "123"
        self.konquest_export._filters = {"director__in": ["<PERSON>"], "manager__in": ["Alice"]}

        users_mock = DataFrame({"user_id": [1, 2, 3]})
        self.account_selector_mock.list_users.return_value = users_mock

        data_mock = DataFrame({
            "user_id": [1, 2, 3],
            "mission_completion_rate": [0.85, 0.75, 0.90]
        })
        self.query_selector_mock.list_learning_trail_enrollments.return_value = data_mock

        result_data = self.konquest_export._load_data_by_learning_trail()

        self.assertEqual(len(result_data), 3)
        self.assertIn("mission_completion_rate", result_data.columns)
        self.assertEqual(result_data.iloc[0]["mission_completion_rate"], "85%")
        self.assertEqual(result_data.iloc[1]["mission_completion_rate"], "75%")
        self.assertEqual(result_data.iloc[2]["mission_completion_rate"], "90%")

    def test_load_data_by_learning_trail_without_filters(self):
        self.konquest_export._workspace_id = "123"
        self.konquest_export._filters = {}

        data_mock = DataFrame({"user_id": [1, 2, 3]})
        self.query_selector_mock.list_learning_trail_enrollments.return_value = data_mock

        result_data = self.konquest_export._load_data_by_learning_trail()

        self.assertEqual(len(result_data), 3)

    def test_load_data_by_learning_trail_with_empty_data(self):
        self.konquest_export._workspace_id = "123"
        self.konquest_export._filters = {}

        data_mock = DataFrame()
        self.query_selector_mock.list_learning_trail_enrollments.return_value = data_mock

        result_data = self.konquest_export._load_data_by_learning_trail()

        self.assertTrue(result_data.empty)
