import email
import os
import time
import uuid
import zipfile
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Optional, Sequence, Dict, Type

from Locales import Locales
import numpy
import pandas as pd
from dataclasses import dataclass, field

from matplotlib.ticker import Formatter
from pandas import DataFrame, Series
from pandas.errors import DataError
from pandas.core.indexes import multi
from xlsxwriter.format import Format
from xlsxwriter.worksheet import Worksheet

from config.default import Config
from custom.discord_webhook_logger import DiscordWebhookLogger
from domain.report.models import Report
from reports.abstracts.report_uploader_client_abc import ReportUploaderClientABC
from reports.formatters.date_formatter import DateFormatter
from reports.formatters.formatter import Formatter
from reports.formatters.formatter_service import FormatterService
from reports.query import QueryExecutor
from reports.utils import ReportUploaderClient, convert_timedelta_to_str
import pytz
from reports.export.info_sheet_service import InfoSheetService


@dataclass
class ExportPart:
    name: str
    data_lambda: callable
    index: bool = True
    stylized: bool = True
    _data: DataFrame = field(default_factory=DataFrame)
    is_active: bool = True
    by_line: bool = False

    def load_data(self) -> None:
        try:
            self._data = self.data_lambda()
        except Exception as error:
            if Config.DEBUG:
                raise error
            DiscordWebhookLogger().emit_short_message(
                f'Export Part Error. Template: {self.name}', error
            )

    @property
    def data(self) -> Optional[DataFrame]:
        if self._data.empty:
            self.load_data()
        return self._data


class ExportCreator(ABC):
    def __init__(
        self,
        file_type: str,
        uploader: ReportUploaderClientABC,
        locale: Locales,
        temp_dir: str = f'{Config.BASE_DIR}/reports/temp',
    ) -> None:
        self._file_type: str = file_type
        self._uploader = uploader
        self._temp_dir = temp_dir
        self._locale = locale
        self._parts: Sequence[ExportPart] = []
        self.report_name = None
        self.time_zone: str = 'UTC'
        self._formatter_service: Optional[FormatterService] = None

    def set_formatter_service(self, formatter_service: FormatterService):
        """
        Load a formatter service to format all dataframes (timezone, float, NaN...).
        Set the formatter timezone and the formatter before load on this class
        """
        if not formatter_service.time_zone:
            raise ValueError("Invalid service. The time_zone of the service should be set")
        self._formatter_service = formatter_service

    @abstractmethod
    def create(
        self,
        parts: [ExportPart],
        report_name: str,
        report: Report
    ) -> str:
        if report.language in self._locale.languages:
            self._locale.set_default_lang(report.language)

        self._parts = list(filter(lambda page: not page.data.empty, parts))
        self.report_name = report_name if report_name else str(uuid.uuid4())
        report_path = self._generate_file(report)
        return self._uploader.upload_report(report_path, self._file_type).get('url')

    @abstractmethod
    def _generate_file(self, report: Report) -> str:
        pass

    def _generate_file_path(self, extension: str):
        translated_report_name = self._translate_text(self.report_name)
        file_path = f'{self._temp_dir}/{translated_report_name}-{datetime.today()}-{uuid.uuid4()}.{extension}'
        return file_path

    def _format_dataframe(self, data_frame: DataFrame) -> DataFrame:
        if self._formatter_service:
            data_frame = self._formatter_service.format_dataframe(data_frame)
        data_frame = self._format_intervals(data_frame)
        data_frame = self.translate(data_frame)
        return data_frame

    def _translate_text(self, text: str) -> str:
        if text in self._locale.messages:
            try:
                return self._locale.get(text)
            except KeyError:
                return text
        return text

    def _translate_list(self, text_list: Sequence) -> Sequence:
        translated_list = []
        for text in text_list:
            if not isinstance(text, str):
                translated_list.append(text)
            else:
                translated_list.append(self._translate_text(text))
        return translated_list

    @staticmethod
    def _format_intervals(data_frame) -> DataFrame:
        columns_types = data_frame.dtypes
        columns = list(columns_types.index.values)

        for column in columns:
            column_type = columns_types[column]
            if column_type.type != numpy.timedelta64:
                continue
            data_frame[column] = data_frame[column].apply(convert_timedelta_to_str)

        return data_frame

    def translate(self, data_frame: DataFrame) -> DataFrame:
        for column in data_frame:
            data_frame[column] = self._translate_list(list(data_frame[column].values))
            data_frame.rename(columns={column: self._translate_text(column)}, inplace=True)

        if isinstance(data_frame.index, multi.MultiIndex):
            return self._translate_multi_indexes(data_frame)

        return self._translate_indexes(data_frame)

    def _translate_multi_indexes(self, data_frame) -> DataFrame:
        old_data = data_frame.copy()
        translated_indexes = []

        for index in data_frame.index:
            translated_indexes.append(tuple(self._translate_list(list(index))))
        data_frame = data_frame.reindex(translated_indexes)

        data_frame.index.rename(self._translate_list(data_frame.index.names), inplace=True)
        for column in data_frame:
            data_frame[column] = old_data[column].values

        return data_frame

    def _translate_indexes(self, data_frame: DataFrame) -> DataFrame:
        translated = []
        for name in data_frame.index.names:
            translated.append(self._translate_text(name))
        data_frame.index.names = translated
        return data_frame


class ExportCsvCreator(ExportCreator):
    def __init__(self, uploader: ReportUploaderClient, locale: Locales, temp_dir: str = f"{Config.BASE_DIR}/reports/temp") -> None:
        super().__init__("zip", uploader, locale, temp_dir)
        self._report_id = None

    def create(self, parts: [ExportPart], report_name: str, report: Report) -> str:
        self._report_id = str(uuid.uuid4())
        return super().create(parts, report_name, report)

    def _generate_file(self, report: Report) -> str:
        file_zip = self._generate_file_path('zip')

        csv_files = []
        for part in self._parts:
            zip_path = f'{self._temp_dir}/{part.name}_{self._report_id}.csv'
            data = self._format_dataframe(part.data)
            data.to_csv(zip_path, index=part.index, encoding='UTF-8')
            csv_files.append(zip_path)

        return self._compress(csv_files, file_zip)

    @staticmethod
    def _compress(part_paths: list, output_path: str) -> str:
        compress_type = zipfile.ZIP_DEFLATED
        zipper = zipfile.ZipFile(output_path, mode="w")

        for path in part_paths:
            partitioned_file_path = path.rpartition('/')
            file_name = path.replace(partitioned_file_path[0], '').replace('/', '')
            try:
                zipper.write(path, file_name, compress_type=compress_type)
            except FileNotFoundError:
                continue
            os.remove(path)
        zipper.close()

        return output_path


class ExportXlsxCreator(ExportCreator):
    cell_format = {"font_name": "Arial", "font_size": 12, "align": "left"}
    header_format = {
        "bold": True,
        "text_wrap": True,
        "valign": "top",
        "align": "center",
        "fg_color": "#808080",
        "font_color": "#FFFFFF",
        "border": 1
    }

    def __init__(
        self,
        uploader: ReportUploaderClient,
        locale: Locales,
        webhook_logger: DiscordWebhookLogger,
        info_sheet_service: InfoSheetService,
        max_column_width: int = 100,
        temp_dir: str = f"{Config.BASE_DIR}/reports/temp"
    ) -> None:
        super().__init__("xlsx", uploader, locale, temp_dir)
        self._parts: Sequence[ExportPart] = []
        self._webhook_logger = webhook_logger
        self.report_name = None
        self.max_column_width = max_column_width
        self.info_sheet_service = info_sheet_service

    def create(self, parts: [ExportPart], report_name: str, report: Report) -> str:
        return super().create(parts, report_name, report)

    def _generate_file(self, report: Report) -> str:
        file_path = self._generate_file_path('xlsx')
        with pd.ExcelWriter(file_path, engine='xlsxwriter') as writer:
            info_sheet_name = self._translate_text("information")[:31]
            self.info_sheet_service.add_info_sheet(writer, report, self.report_name, info_sheet_name)
            for part in self._parts:
                data = self._format_dataframe(part.data).round(2)
                name = self._translate_text(part.name)[:31]
                index = part.index
                if part.by_line:
                    data = data.transpose()
                    data = data.reset_index()
                    data.columns = [self._translate_text('counters'), self._translate_text('total')]
                if part.stylized:
                    data.to_excel(writer, sheet_name=name, startrow=1, index=index, header=False)
                    worksheet = writer.sheets[name]
                    self._stylize_worksheet(data, worksheet, writer)
                else:
                    data.to_excel(writer, sheet_name=name, index=index)

        writer.close()                           
        return file_path

    def _stylize_worksheet(self, data: DataFrame, worksheet: Worksheet, writer: pd.ExcelWriter) -> None:
        cell_format = writer.book.add_format(self.cell_format)
        header_format = writer.book.add_format(self.header_format)
        worksheet.set_column('A:AZ', None, cell_format)
        has_index = bool(data.index.names[0])

        if has_index:
            self._stylize_indexes(data, header_format, worksheet)

        self._stylize_columns(data, header_format, worksheet)

    def _stylize_columns(self, data: DataFrame, header_format: Format, worksheet: Worksheet) -> None:
        indexes = data.index.names
        has_index = bool(indexes[0])
        count_indexes = len(indexes) - 1 if not has_index else len(indexes)

        for column_position, column_name in enumerate(data.columns.values):
            position_in_worksheet = column_position + count_indexes
            column_width = self._get_column_width(data_frame=data, column_name=column_name)
            column_width = min(column_width, self.max_column_width)

            worksheet.write(0, position_in_worksheet, column_name, header_format)
            worksheet.set_column(position_in_worksheet, position_in_worksheet, column_width)

    def _stylize_indexes(self, data: DataFrame, header_format: Format, worksheet: Worksheet) -> Worksheet:
        for position, value in enumerate(data.index.names):
            index_width = self._get_index_width(data, position)
            index_width = min(index_width, self.max_column_width)
            worksheet.write(0, position, value, header_format)
            worksheet.set_column(position, position, index_width)

        return worksheet

    @staticmethod
    def _get_column_width(data_frame: DataFrame, column_name: str) -> int:
        try:
            max_values_width = len(max([str(value) for value in data_frame[column_name]], key=len))
        except IndexError:
            max_values_width = 20
        column_name_width = len(column_name)
        max_width = max([max_values_width, column_name_width])

        return max_width + 2

    @staticmethod
    def _get_index_width(data_frame: DataFrame, position: int) -> int:
        try:
            max_values_width = len(max([str(value[position]) for value in data_frame.index.values], key=len))
        except IndexError:
            max_values_width = 20
        index_name_width = len(data_frame.index.names[position])
        max_width = max([max_values_width, index_name_width])

        return max_width + 2


class ExportBuilder(ABC):
    formatters_by_column: Dict[str, Type[Formatter]] = {"column_name": DateFormatter}

    def __init__(
        self,
        xlsx_creator: ExportXlsxCreator,
        csv_creator: ExportCsvCreator,
        query_selector: QueryExecutor,
        formatter_service: FormatterService,
        report_name: str = None,
        date_format_by_column = None
    ) -> None:
        self._xlsx_creator = xlsx_creator
        self._csv_creator = csv_creator
        self._main_query = query_selector
        self._report_name = report_name if report_name else str(uuid.uuid4())
        self._parts: Sequence[ExportPart]
        self._main_data: Optional[DataFrame] = DataFrame([])
        self._creators: Dict[str, ExportCreator] = {
            'XLSX': self._xlsx_creator,
            'CSV': self._csv_creator
        }
        self._workspace_id = None
        self.date_format_by_column = date_format_by_column
        self.formatter_service = formatter_service
        if self.date_format_by_column is None:
            self.date_format_by_column = {}

        self._filters = {}

    @abstractmethod
    def generate(self, report: Report) -> Report:
        self._load_parts()
        self.formatter_service.set_time_zone(report.time_zone)
        self.formatter_service.formatters = self.formatters_by_column

        if not self._parts:
            raise AttributeError(
                "exports parts cannot be empty, load the self._parts variable in the abstractmethod _load_parts"
            )
        try:
            creator = self._creators[report.file_format]
        except KeyError as error:
            raise ValueError(f'unknown file format: {report.file_format}') from error

        creator.set_formatter_service(self.formatter_service)
        parts = list(filter(lambda part: part.is_active, self._parts))
        self._main_data = self.load_main_data()

        process_started_at = time.time()
        if self._main_data.empty:
            report.url = creator.create([parts[0]], self._report_name, report)
        else:
            report.url = creator.create(parts, self._report_name, report)
        process_ended_at = time.time()
        report.processing_time = process_ended_at - process_started_at
        report.status = "DONE"
        return report

    @abstractmethod
    def load_main_data(self) -> DataFrame:
        pass

    def get_main_data(self) -> DataFrame:
        return self._main_data

    @abstractmethod
    def _load_parts(self) -> None:
        self._parts = []

    def pivot_table(
        self,
        data,
        values=None,
        index=None,
        columns=None,
        aggfunc="mean",
        fill_value=None,
        margins=False,
        dropna=True,
        margins_name="All",
        observed=False,
    ) -> DataFrame:
        index_formatters = dict(filter(
            lambda formatter_column: formatter_column[0] in index, self.formatters_by_column.items()
        ))
        if index_formatters:
            self.formatter_service.format_dataframe(data, index_formatters)
        try:
            return pd.pivot_table(
                data, values, index, columns, aggfunc, fill_value, margins, dropna, margins_name, observed
            )
        except DataError as data_error:
            if str(data_error) != "No numeric types to aggregate":
                raise data_error
            return DataFrame([])

    @staticmethod
    def _is_empty_series(series: Series) -> bool:
        return series.isnull().sum() == len(series)

    def pivot_table_with_subtotal(
        self,
        data_frame: DataFrame,
        index: Sequence[str],
        values: Sequence[str],
        prefix_margin: str = 'Total',
        fill_value=0,
        aggfunc="sum"
    ) -> DataFrame:
        values_frequency = data_frame[index[0]].value_counts().to_dict()
        main_pivot = DataFrame()

        for value in values_frequency:
            filtered_data = data_frame[data_frame[index[0]] == value]
            pivot = self.pivot_table(
                data=filtered_data,
                index=index,
                values=values,
                margins=True,
                margins_name=f"{prefix_margin} - {value}",
                aggfunc=aggfunc,
                fill_value=fill_value,
                observed=False
            )
            main_pivot = pd.concat([main_pivot, pivot])

        return main_pivot

    def _get_main_data(self) -> DataFrame:
        return self._main_data
