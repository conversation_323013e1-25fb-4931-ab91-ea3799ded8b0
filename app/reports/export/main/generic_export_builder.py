from domain.report.models import Report
from pandas import DataFrame
from reports.export import ExportBuilder, ExportCsvCreator, ExportPart, ExportXlsxCreator
from reports.formatters.date_time_HM_formatter import DateTimeHMFormatter
from reports.formatters.formatter_service import FormatterService
from reports.query.query_executor_with_config_injectable import QueryExecutorWithConfigInjectable


class GenericExportBuilder(ExportBuilder):
    formatters_by_column = {
        "start_at": DateTimeHMFormatter,
        "stop_at": DateTimeHMFormatter,
        "permission_date": DateTimeHMFormatter,
    }
    def __init__(
        self,
        xlsx_creator: ExportXlsxCreator,
        csv_creator: ExportCsvCreator,
        query_selector: QueryExecutorWithConfigInjectable,
        formatter_service: FormatterService,
        report_name: str = None
    ):
        super().__init__(xlsx_creator, csv_creator, query_selector, formatter_service, report_name)
        self._main_query = query_selector
        self._workspace_id = None
        self._filters = {}

    def generate(self, report: Report) -> Report:
        self._workspace_id = report.workspace_id
        self._filters = report.filters
        return super().generate(report)

    def load_main_data(self) -> DataFrame:
        return self._main_query.execute(self._workspace_id, self._filters)

    def _load_parts(self) -> None:
        self._parts = [
            ExportPart(
                name="raw",
                data_lambda=self.get_main_data,
                index=False
            )
        ]
