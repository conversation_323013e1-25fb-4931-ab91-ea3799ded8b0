import pandas as pd
from unittest.mock import MagicMock, call

# Adicione a importação de 'text' do sqlalchemy para a asserção
from sqlalchemy import text
from sqlalchemy.engine import Engine

from reports.filter.filter_sql import FilterSql
from reports.query.query_executor_with_config_injectable import QueryExecutorWithConfigInjectable
from reports.query.query_selector_config import QuerySelectorConfig

TEMPLATES_FOLDER = "reports/tests/resources"


def test_query_executor_with_config_injectable():
    workspace_id = "1234"
    course_id = "4321"
    filters = {"course_id": course_id}
    database_engine = MagicMock(spec=Engine)
    connection = MagicMock()
    database_engine.connect.return_value.__enter__.return_value = connection
    filter_sql = MagicMock(spec=FilterSql)
    filter_sql.builder_sql_filter.return_value = f"AND course_id='{course_id}'"


    
    config = QuerySelectorConfig(
        database_engine=database_engine,
        template_folder=TEMPLATES_FOLDER,
        filter_sql=filter_sql,
        table_name="main_table",
        foreign_tables_names=("foreing_table",),
        query_name="sql_to_test",
        keys_filters_dates=(("created_at", "updated_at"),)
    )
    executor = QueryExecutorWithConfigInjectable(config)

    mock_row_1 = MagicMock()
    mock_row_1._mapping = {"column1": "value1", "column2": "value2"}
    mock_row_2 = MagicMock()
    mock_row_2._mapping = {"column1": "value3", "column2": "value4"}
    connection.execute.return_value = [mock_row_1, mock_row_2]

    data_frame = executor.execute(workspace_id=workspace_id, filters=filters)

    connection.execute.assert_called_once()
    
    called_sql_object = connection.execute.call_args[0][0]
    
    expected_sql_string = (
        "SELECT * FROM main_table "
        f"WHERE workspace_id = :workspace_id AND course_id='{course_id}'"
    )
    
    assert str(called_sql_object) == expected_sql_string
    assert connection.execute.call_args[1] == {'workspace_id': workspace_id}
    assert isinstance(data_frame, pd.DataFrame)
    assert data_frame.columns.to_list() == ["column1", "column2"]
    assert len(data_frame) == 2
    expected_data = [{"column1": "value1", "column2": "value2"}, {"column1": "value3", "column2": "value4"}]
    assert data_frame.to_dict('records') == expected_data