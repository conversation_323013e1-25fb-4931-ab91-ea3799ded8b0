import pytest
from pandas._libs import NaT
import math

from reports.formatters.percent_format import PercentFormatter


@pytest.fixture
def formatter():
    return PercentFormatter()


def test_percent_formatter_valid_numbers(formatter):
    assert formatter.execute(0.1234) == "12.3%"
    assert formatter.execute(0.5) == "50%"
    assert formatter.execute(1) == "100%"
    assert formatter.execute(0) == "0%"


def test_percent_formatter_invalid_inputs(formatter):
    assert formatter.execute(None) is None  # None input
    assert formatter.execute(NaT) is None  # NaT input
    assert formatter.execute("string") == "string"  # String input remains unchanged
    assert formatter.execute(math.nan) is None  # NaN input
    with pytest.raises(TypeError):
        formatter.execute([])
    with pytest.raises(TypeError):
        formatter.execute({})


def test_percent_formatter_large_and_small_values(formatter):
    assert formatter.execute(12345.6789) == "1234567.9%"  # Very large number
    assert formatter.execute(0.00001234) == "0%"  # Very small number near zero


def test_percent_formatter_handles_edge_cases(formatter):
    assert formatter.execute(-0.5) == "-50%"  # Negative percentage
    assert formatter.execute(-0.0001) == "-0%"  # Negative small number near zero
    assert formatter.execute(0.0001) == "0%"  # Tiny positive value
