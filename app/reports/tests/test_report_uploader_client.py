from reports.utils import ReportUploaderClient

class Boto3ClientStub:
    @staticmethod
    def upload_file(*args):
        return True


def test_report_uploader_client(mocker):
    mocker.patch.object(Boto3ClientStub, 'upload_file')
    mocker.patch("boto3.client", return_value=Boto3ClientStub())
    bucket = 'bucket'
    aws_default_url = 'aws_default_url'
    bucket_path = 'bucket_path'
    client = ReportUploaderClient('access_key', 'secret_key', 'region_name', bucket, aws_default_url, bucket_path)
    path = 'reports/tests/spreadsheet.xlsx'

    url = client.upload_report(path, "xlsx", remove_after_upload=False)

    assert url['url'] == f'{aws_default_url}/{bucket_path}/reports/default/spreadsheet.xlsx'
