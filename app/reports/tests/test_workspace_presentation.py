import unittest
from unittest.mock import MagicMock

from app.reports.presentation.konquest.workspace import WorkspacePresentation

class TestWorkspacePresentation(unittest.TestCase):
    def setUp(self):
        self.report_creator_mock = MagicMock()
        self.workspace_selector_mock = MagicMock()
        self.account_selector_mock = MagicMock()
        self.kontent_selector_mock = MagicMock()
        self.report_name = "Workspace Report"

        self.workspace_presentation = WorkspacePresentation(
            report_creator=self.report_creator_mock,
            workspace_selector=self.workspace_selector_mock,
            account_selector=self.account_selector_mock,
            kontent_selector=self.kontent_selector_mock,
            report_name=self.report_name
        )

    def test_get_general_data(self):
        workspace_data_mock = {
            "count_missions_created": 10,
            "count_pulses_created": 20,
            "sum_missions_duration": 36000,
            "sum_pulses_duration": 7200,
            "count_users_active": 5
        }
        self.workspace_selector_mock.get_general_data.return_value = workspace_data_mock
        self.workspace_presentation._workspace_id = "123"
        self.workspace_presentation._users = [{"id": 1}, {"id": 2}, {"id": 3}]

        general_data = self.workspace_presentation.get_general_data()

        self.assertEqual(general_data["count_missions_created"], 10)
        self.assertEqual(general_data["count_pulses_created"], 20)
        self.assertEqual(general_data["count_total_users"], 3)
        self.assertEqual(general_data["count_hours_duration_missions"], 10)
        self.assertEqual(general_data["count_hours_duration_pulses"], 2)
        self.assertEqual(general_data["count_users_active"], 5)

