from datetime import datetime
import unittest
from unittest.mock import Mock
from uuid import UUID

from pandas import DataFrame

from domain.report.models import Report
from Locales import Locales
import pandas as pd

from reports.presentation import PresentationCreator
from reports.presentation.konquest.mission_enrollment_quiz import MissionEnrollmentQuizPresentation
from reports.query.konquest.mission_enrollment_quiz import KonquestMissionEnrollmentQuizQuery


DATE_FORMAT = '%Y-%m-%d %H:%M:%S'
ENROLLMENTS = [
    {
        'id': UUID('b0e44ce6-45f0-4738-88d6-f1369bc948ee'),
        'user_id': UUID('a277acd5-4181-4bb7-a412-8b65463ef871'),
        'mission_id': UUID('0c17f227-d429-445f-a48a-e97e11fe0f03'),
        'mission_name': 'Tivit - Gestão da Qualidade',
        'assessment_type': 'FULL',
        'created_date': '2022-01-12',
        'end_date': datetime.strptime('2022-09-28 12:48:11', DATE_FORMAT),
        'enrollment_status': 'REPROVED',
        'performance': 0.28,
        'progress': 1.0,
        'total_mission_questions': None,
        'total_correct_answers': None,
        'workspace_name': 'Keeps',
        'workspace_logo_url': 'https://media-stage.keepsdev.com/myaccount/workspace-icon/4b5ed54d5c6cb1c676ec4518c7b270.png',
        'workspace_color': '#795548',
        'user_name': 'Eduardo Korb',
        'status': False,
        'user_email': '<EMAIL>',
        'user_phone': '*************',
        'user_address': 'Biguaçuli',
        'user_country': 'Brasil',
        'user_registration_date': '2021-10-19',
        'user_activation_date': '2021-10-19',
        'user_ein': None,
        'user_leader_email': '----',
        'user_job': '----',
        'user_job_function': '----',
        'user_director': '----',
        'user_manager': '----',
        'user_area_of_activity': '----'
    }
]

QUESTION_OPTIONS = [
    {
        'question_id': UUID('c1469b24-afda-4357-9e19-a467bcdba60e'),
        'question_created_date': datetime.strptime('2021-10-11 11:53:14', DATE_FORMAT),
        'exam_question': 'Main Question',
        'option': 'Option 1',
        'correct_answer': False,
        'mission_enrollment_id': UUID('b0e44ce6-45f0-4738-88d6-f1369bc948ee'),
        'checked': False,
        'answer_date': '2021-11-18 20:35:29'
    },
    {
        'question_id': UUID('c1469b24-afda-4357-9e19-a467bcdba60e'),
        'question_created_date': datetime.strptime('2021-10-11 11:53:14', DATE_FORMAT),
        'exam_question': 'Main Question',
        'option': 'Option 2',
        'correct_answer': False,
        'mission_enrollment_id': UUID('b0e44ce6-45f0-4738-88d6-f1369bc948ee'),
        'checked': False,
        'answer_date': '2021-11-18 20:35:29'
    },
    {
        'question_id': UUID('c1469b24-afda-4357-9e19-a467bcdba60e'),
        'question_created_date': datetime.strptime('2021-10-11 11:53:14', DATE_FORMAT),
        'exam_question': 'Main Question',
        'option': 'Option 3',
        'correct_answer': False,
        'mission_enrollment_id': UUID('b0e44ce6-45f0-4738-88d6-f1369bc948ee'),
        'checked': False,
        'answer_date': '2021-11-18 20:35:29'
    },
    {
        'question_id': UUID('9e199b24-afda-4357-9e19-a467bcdb9b24'),
        'question_created_date': datetime.strptime('2021-10-11 11:53:14', DATE_FORMAT),
        'exam_question': 'Another Question',
        'option': 'Option 1',
        'correct_answer': False,
        'mission_enrollment_id': UUID('b0e44ce6-45f0-4738-88d6-f1369bc948ee'),
        'checked': False,
        'answer_date': '2021-11-18 20:35:29'
    },
]


class TestMissionEnrollmentQuizPresentation(unittest.TestCase):

    def setUp(self):
        self.report_creator = Mock(spec=PresentationCreator)
        self.query = Mock(spec=KonquestMissionEnrollmentQuizQuery)
        self.locales = Mock(spec=Locales)
        self.locales.messages = {}

        self.presentation = MissionEnrollmentQuizPresentation(
            report_creator=self.report_creator,
            query=self.query,
            locales=self.locales,
            report_name="TestReport"
        )

        self.mock_report = Report(workspace_id="123")

    def test_generate_empty_enrollments(self):
        self.report_creator.create.return_value = None
        self.query.list_mission_enrollments.return_value = pd.DataFrame()

        result = self.presentation.generate(self.mock_report)

        self.assertIsInstance(result, Report)
        self.assertEqual(result.status, "DONE")
        self.assertTrue(result.processing_time > 0)
        self.assertEqual(result.url, None)
        self.report_creator.create.assert_called()

    def test_generate_non_empty_enrollments(self):
        enrollments = DataFrame(ENROLLMENTS)
        self.query.list_mission_enrollments.return_value = enrollments
        self.report_creator.create.return_value = "https://example.com/report.pdf"

        result = self.presentation.generate(self.mock_report)

        self.assertIsInstance(result, Report)
        self.assertEqual(result.status, "DONE")
        self.report_creator.create.assert_called_once()

    def test_get_main_data(self):
        self.query.list_mission_enrollments.return_value = pd.DataFrame(ENROLLMENTS)
        self.query.list_question_and_answers.return_value = pd.DataFrame(QUESTION_OPTIONS)

        self.presentation.generate(self.mock_report)

        main_data = self.presentation.get_main_data()
        self.assertIsNotNone(main_data)
        self.assertEqual(
            main_data,
            {
                'assessment_type': 'FULL',
                'enrollment_created_date': '2022-01-12',
                'enrollment_end_date': '2022/09/28 12:48:11',
                'enrollment_performance': '28%',
                'enrollment_progress': 1.0,
                'enrollment_quiz_performance': '0% (0/0)',
                'enrollment_status': 'REPROVED',
                'mission_name': 'Tivit - Gestão da Qualidade',
                'primary_color': '#795548',
                'quizzes':
                    [
                        {
                            'answer_date': '2021-11-18 20:35:29',
                            'options': [
                                {
                                    'checked': False,
                                    'is_correct': False,
                                    'text': 'Option 1'
                                },
                                {
                                    'checked': False,
                                    'is_correct': False,
                                    'text': 'Option 2'
                                },
                                {
                                    'checked': False,
                                    'is_correct': False,
                                    'text': 'Option 3'
                                }
                            ],
                            'title': 'Main Question'
                        },
                        {
                            'answer_date': '2021-11-18 20:35:29',
                            'options': [
                                {
                                    'checked': False,
                                    'is_correct': False,
                                    'text': 'Option 1'
                                }
                            ],
                            'title': 'Another Question'
                        },
                    ],
                'secondary_color': '#795548',
                'user_area_of_activity': '----',
                'user_director': '----',
                'user_email': '<EMAIL>',
                'user_job_function': '----',
                'user_job_position': '----',
                'user_manager': '----',
                'user_name': 'Eduardo Korb',
                'workspace_logo_url': 'https://media-stage.keepsdev.com/myaccount/workspace-icon/4b5ed54d5c6cb1c676ec4518c7b270.png',
                'workspace_name': 'Keeps'
            }
        )
