import glob
import os
import shutil
import uuid
from unittest.mock import Mock
import pandas as pd
import pytest

import reports as reports
from config.default import Config
from reports.abstracts.report_uploader_client_abc import ReportUploaderClientABC
from reports.builder import PDFBuilder, ExportBuilder
from reports.utils import ReportLambdaClient


class  ReportUploaderClientStub(ReportUploaderClientABC):
    def upload_report(self, file_path, type_name, client='default', remove_after_upload=True):
        return {"url": 'www.file.com', "name": 'file'}


@pytest.fixture
def konquest_user_pages_data(konquest_user_pages_data):
    return konquest_user_pages_data


@pytest.fixture
def report_uploader_client():
    return ReportUploaderClientStub()


@pytest.fixture
def konquest_generate_empty_pdf():
    file_path = f'{Config.BASE_DIR}/reports/temp/{uuid.uuid4()}.pdf'
    file_origin = os.path.realpath(f'{Config.BASE_DIR}/PDF_to_pytest.pdf')

    shutil.copy(file_origin, file_path)

    return file_path


@pytest.fixture
def report_container():
    return reports.di.ReportContainer()


@pytest.fixture
def report_uploader(report_container):
    return report_container._report_uploader


@pytest.fixture
def report_lambda_client(report_container):
    return report_container._report_lambda_client


@pytest.fixture
def report_export(report_container):
    return report_container._report_export


@pytest.fixture
def pdf_builder(report_container, report_uploader_client):
    return PDFBuilder(report_container._report_lambda_client, report_uploader_client, f'{Config.BASE_DIR}/reports/temp')


@pytest.fixture
def export_builder(report_container, report_uploader_client):
    return ExportBuilder(
            report_container._report_export,
            report_uploader_client,
            f'{Config.BASE_DIR}/reports/temp'
        )


@pytest.fixture(scope="function")
def file_export(export_builder, konquest_company_missions_export_data):
    return export_builder.execute_xlsx()


def test_konquest_user_overview_pdf_generator(pdf_builder, konquest_user_pages_data, konquest_generate_empty_pdf):
    ReportLambdaClient.json_to_pdf = Mock(return_value=konquest_generate_empty_pdf)

    link_to_download = 'konquest-user-pytest-report'
    assert pdf_builder.execute(konquest_user_pages_data, link_to_download, remove_temp_file=False) is not None


def test_konquest_company_overview_pdf_generator(pdf_builder, konquest_company_pages_data, konquest_generate_empty_pdf):
    ReportLambdaClient.json_to_pdf = Mock(return_value=konquest_generate_empty_pdf)

    link_to_download = 'konquest-company-pytest-report'
    assert pdf_builder.execute(konquest_company_pages_data, link_to_download, remove_temp_file=False) is not None


def test_konquest_course_overview_pdf_generator(pdf_builder, konquest_course_pages_data, konquest_generate_empty_pdf):
    ReportLambdaClient.json_to_pdf = Mock(return_value=konquest_generate_empty_pdf)

    link_to_download = 'konquest-course-pytest-report'
    assert pdf_builder.execute(konquest_course_pages_data, link_to_download, remove_temp_file=False) is not None


def test_smartzap_course_overview_pdf_generator(pdf_builder, smartzap_course_pages_data, konquest_generate_empty_pdf):
    ReportLambdaClient.json_to_pdf = Mock(return_value=konquest_generate_empty_pdf)

    link_to_download = 'smartzap-course-pytest-report'
    assert pdf_builder.execute(smartzap_course_pages_data, link_to_download, remove_temp_file=False) is not None


def test_error_page(pdf_builder, konquest_generate_empty_pdf):
    ReportLambdaClient.json_to_pdf = Mock(return_value=konquest_generate_empty_pdf)

    file = pdf_builder.error_page("erro-test", {"template": 'test'})

    name, extension = os.path.splitext(file)

    os.remove(file)
    assert name is not None
    assert extension == '.pdf'


def test_export_execute_xlsx(export_builder, konquest_company_missions_export_data):
    link_to_download = 'test-export'

    df = pd.DataFrame(konquest_company_missions_export_data)
    file = export_builder.execute_xlsx(sheets=[{"name": 'raw', "data_frame": df, "index": True}], report_name=link_to_download)

    assert file is not None


def test_delete_temp_files():
    files = glob.glob('{Config.BASE_DIR}/reports/temp/*')
    for f in files:
        os.remove(f)
