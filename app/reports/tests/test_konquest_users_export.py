import unittest
from datetime import datetime, timedelta
from unittest.mock import MagicMock
from pandas import DataFrame

from config.default import Config
from reports.export import ExportXlsxCreator, ExportCsvCreator
from reports.export.konquest.users import KonquestUsersExport, LAST_ACTIVITY_DATE
from reports.formatters.formatter_service import FormatterService
from reports.query.konquest.export import KonquestExportQuery
from domain.report.models import Report

class TestKonquestUsersExport(unittest.TestCase):
    def setUp(self):
        self.xlsx_creator = MagicMock(spec=ExportXlsxCreator)
        self.csv_creator = MagicMock(spec=ExportCsvCreator)
        self.konquest_query = MagicMock(spec=KonquestExportQuery)
        self.formatter_service = MagicMock(spec=FormatterService)
        self.max_days_of_inactivity = 30
        self.report_name = "Konquest Users Report"

        self.konquest_users_export = KonquestUsersExport(
            xlsx_creator=self.xlsx_creator,
            csv_creator=self.csv_creator,
            konquest_query=self.konquest_query,
            formatter_service=self.formatter_service,
            max_days_of_inactivity=self.max_days_of_inactivity,
            report_name=self.report_name,
        )

    def test_generate(self):
        report = Report(workspace_id="123", filters={"some_filter": "value"}, file_format="XLSX")
        result_report = self.konquest_users_export.generate(report)
        self.assertEqual(result_report.workspace_id, "123")
        self.assertEqual(result_report.filters, {
            'role__application_id': Config.KONQUEST_APPLICATION_ID,
            'some_filter': 'value'
        })

    def test_load_main_data(self):
        self.konquest_users_export._main_query.list_users.return_value = DataFrame(
            {"user_id": [1, 2, 3], "user_name": ["Alice", "Bob", "Charlie"]}
        )
        self.konquest_users_export._workspace_id = 123
        self.konquest_users_export._filters = {"some_filter": "value"}

        main_data = self.konquest_users_export.load_main_data()

        self.assertTrue("user_id" in main_data.columns)
        self.assertTrue("user_name" in main_data.columns)
        self.assertEqual(main_data.shape, (3, 2))

    def test_load_data_by_leader(self):
        self.konquest_users_export._main_data = DataFrame(
            {
                "user_id": [0, 1, 2, 3],
                "user_leader_email": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"],
                LAST_ACTIVITY_DATE: [None, None, datetime.now(), datetime.now() - timedelta(days=40)],
                "last_access_date": [None, datetime.now(), datetime.now(), datetime.now() - timedelta(days=40)],
            }
        )

        data_by_leader = self.konquest_users_export._load_data_by_leader()

        self.assertTrue("total_users_enabled" in data_by_leader.columns)
        self.assertTrue("active_users" in data_by_leader.columns)
        self.assertTrue("inactive_users" in data_by_leader.columns)
        self.assertTrue("users_who_never_logged_on" in data_by_leader.columns)
        self.assertEqual(data_by_leader.shape, (2, 7))
        self.assertEqual(
            data_by_leader.to_dict("records"),
            [{'total_users_enabled': 3, 'active_users': 0.0, 'inactive_users': 3.0, 'users_who_never_logged_on': 1.0, 'engagement_rate': 0.0, 'inactivity_rate': 1.0, 'access_rate': 0.67}, 
             {'total_users_enabled': 1, 'active_users': 1.0, 'inactive_users': 0.0, 'users_who_never_logged_on': 0.0, 'engagement_rate': 1.0, 'inactivity_rate': 0.0, 'access_rate': 0.0}]
        )