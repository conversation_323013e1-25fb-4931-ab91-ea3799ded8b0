import unittest
from typing import Any
from unittest.mock import Mock

import mock
import pandas as pd
from mock.mock import MagicMock
from pandas import DataFrame

from reports.di import SQL_TEMPLATE_FOLDER_KONQUEST
from reports.query.account.export import AccountExportQuery
from reports.query.konquest.mission_enrollment_quiz import KonquestMissionEnrollmentQuizQuery


class EngineConnectionMocked:
    def execute(self, *args, **kwargs):
        return self

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        return


class EngineMocked:
    @staticmethod
    def connect():
        return EngineConnectionMocked()


class TestKonquestMissionEnrollmentQuizQuery(unittest.TestCase):

    def setUp(self):
        self.engine: Any = EngineMocked
        self.account_export_query = Mock(spec=AccountExportQuery)
        self.filter_sql = Mock()

        self.quiz_query = KonquestMissionEnrollmentQuizQuery(
            konquest_database_engine=self.engine,
            account_export_query=self.account_export_query,
            template_folder=SQL_TEMPLATE_FOLDER_KONQUEST,
            filter_sql=self.filter_sql
        )

    @mock.patch.object(KonquestMissionEnrollmentQuizQuery, '_list_results')
    def test_list_question_and_answers(self, list_results_mock: MagicMock):
        mission_ids = ["mission_id1", "mission_id2"]
        question_options = [
            {"question_id": 1, "question_text": "Question 1"},
            {"question_id": 2, "question_text": "Question 2"}
        ]
        list_results_mock.return_value = question_options

        result = self.quiz_query.list_question_and_answers(mission_ids)

        expected_result = pd.DataFrame(question_options)
        pd.testing.assert_frame_equal(result, expected_result)

    @mock.patch.object(KonquestMissionEnrollmentQuizQuery, '_list_results')
    def test_list_mission_enrollments(self, list_results_mock: MagicMock):
        enrollments = [
            {"user_id": 1, "mission_name": "Mission 1"},
            {"user_id": 2, "mission_name": "Mission 2"}
        ]
        workspace_id = '1234'

        self.account_export_query.list_users.return_value = DataFrame([
            {"user_id": 1, "user_name": "User name"},
            {"user_id": 2, "user_name": "Second user name"}
        ])
        list_results_mock.return_value = enrollments

        result = self.quiz_query.list_mission_enrollments(
            workspace_id, {'mission_id__in': ["14151"], 'user_id__in': ['121fa']}
        )

        expected_result = pd.DataFrame([
            {"user_id": 1, "mission_name": "Mission 1", "user_name": "User name"},
            {"user_id": 2, "mission_name": "Mission 2", "user_name": "Second user name"},
        ])
        pd.testing.assert_frame_equal(result, expected_result)
