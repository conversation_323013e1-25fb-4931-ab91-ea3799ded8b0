import pytest
from datetime import datetime
import pytz
from pandas import DataFrame
import pandas as pd
from pandas.testing import assert_frame_equal
from reports.formatters.formatter import Formatter
from reports.formatters.formatter_service import FormatterService


class MockFormatter(Formatter):
    def execute(self, value):
        return f"Formatted-{value}"


@pytest.fixture
def sample_dataframe():
    return DataFrame({
        "datetime_col": [datetime(2024, 1, 1, 12, 0), datetime(2024, 1, 2, 15, 30)],
        "numeric_col": [10, 20],
        "string_col": ["test1", "test2"]
    })


@pytest.fixture
def formatter_service():
    return FormatterService()


def test_set_time_zone(formatter_service):
    formatter_service.set_time_zone("UTC")
    assert formatter_service.time_zone == pytz.timezone("UTC")


def test_format_dataframe_with_datetime(sample_dataframe, formatter_service):
    formatter_service.set_time_zone("UTC")
    expected_df = sample_dataframe.copy()
    expected_df["datetime_col"] = [
        datetime(2024, 1, 1, 12, 0),
        datetime(2024, 1, 2, 15, 30),
    ]

    result = formatter_service.format_dataframe(sample_dataframe)
    assert_frame_equal(result, expected_df)


def test_format_dataframe_with_custom_formatter(sample_dataframe, formatter_service):
    custom_formatters = {"string_col": MockFormatter}

    expected_df = sample_dataframe.copy()
    expected_df["string_col"] = ["Formatted-test1", "Formatted-test2"]

    result = formatter_service.format_dataframe(sample_dataframe, custom_formatters)
    assert_frame_equal(result, expected_df)


def test_change_timezone(formatter_service):
    formatter_service.set_time_zone("UTC")
    date = datetime(2024, 1, 1, 12, 0, tzinfo=pytz.timezone("America/New_York"))
    result = formatter_service._change_timezone(date)
    expected = datetime(2024, 1, 1, 16, 56)
    assert result == expected


def test_format_date_with_datetime(formatter_service):
    formatter_service.set_time_zone("America/Sao_Paulo")

    dt_with_tz = datetime(2024, 4, 15, 15, 20, 17, tzinfo=pytz.UTC)
    result = formatter_service._format_date(dt_with_tz)
    expected = datetime(2024, 4, 15, 12, 20, 17)
    assert result == expected

    dt_without_tz = datetime(2024, 4, 15, 15, 20, 17)
    result = formatter_service._format_date(dt_without_tz)
    expected = datetime(2024, 4, 15, 12, 20, 17)
    assert result == expected


def test_format_date_with_datetimeindex(formatter_service):
    formatter_service.set_time_zone("America/Sao_Paulo")

    dates = ['2024-04-15 15:20:17.250000+00:00', '2024-04-16 18:32:02.953000+00:00']
    dt_index = pd.DatetimeIndex(dates)
    result = formatter_service._format_date(dt_index)

    expected_dates = ['2024-04-15 12:20:17.250000', '2024-04-16 15:32:02.953000']
    expected = pd.DatetimeIndex(expected_dates)
    pd.testing.assert_index_equal(result, expected)


def test_format_date_with_invalid_value(formatter_service):
    result = formatter_service._format_date("invalid_date")
    assert result == "invalid_date"

    result = formatter_service._format_date(None)
    assert result is None

    result = formatter_service._format_date(123)
    assert result == 123


def test_change_timezone_with_timezone(formatter_service):
    formatter_service.set_time_zone("America/Sao_Paulo")
    date = datetime(2024, 4, 15, 15, 20, 17, tzinfo=pytz.UTC)
    result = formatter_service._change_timezone(date)
    expected = datetime(2024, 4, 15, 12, 20, 17)
    assert result == expected


def test_change_timezone_without_timezone(formatter_service):
    formatter_service.set_time_zone("America/Sao_Paulo")
    date = datetime(2024, 4, 15, 15, 20, 17) 
    result = formatter_service._change_timezone(date)
    expected = datetime(2024, 4, 15, 12, 20, 17)
    assert result == expected


def test_change_timezone_index_with_timezone(formatter_service):
    formatter_service.set_time_zone("America/Sao_Paulo")  # UTC-3

    dates = ['2024-04-15 15:20:17.250000+00:00', '2024-04-16 18:32:02.953000+00:00']
    dt_index = pd.DatetimeIndex(dates)

    result = formatter_service._change_timezone_index(dt_index)

    expected_dates = ['2024-04-15 12:20:17.250000', '2024-04-16 15:32:02.953000']
    expected = pd.DatetimeIndex(expected_dates)

    pd.testing.assert_index_equal(result, expected)


def test_change_timezone_index_without_timezone(formatter_service):
    formatter_service.set_time_zone("America/Sao_Paulo") 

    dates = ['2024-04-15 15:20:17.250000', '2024-04-16 18:32:02.953000']
    dt_index = pd.DatetimeIndex(dates)

    result = formatter_service._change_timezone_index(dt_index)

    expected_dates = ['2024-04-15 12:20:17.250000', '2024-04-16 15:32:02.953000']
    expected = pd.DatetimeIndex(expected_dates)

    pd.testing.assert_index_equal(result, expected)
