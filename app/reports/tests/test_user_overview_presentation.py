import unittest
from unittest.mock import MagicMock, patch
from datetime import date, datetime, timedelta
from uuid import UUID

import numpy as np
from pandas import DataFrame, NaT

from reports.presentation.konquest.user import UserPresentation

TRAIL_ENROLLMENTS_DATA = [
    {'trail_name': '<PERSON><PERSON><PERSON> teste 19/05/2023', 'trail_type': 'Open For Workspace', 'progress': 0.0, 'performance': None, 'start_date': datetime(2023, 6, 23, 18, 30, 25, 54729), 'status': 'STARTED', 'end_date': None, 'goal_date': date(2023, 6, 23)},
    {'trail_name': 'trilha 01', 'trail_type': 'Open For Workspace', 'progress': 0.25, 'performance': None, 'start_date': datetime(2023, 6, 12, 13, 23, 0, 892028), 'status': 'STARTED', 'end_date': None, 'goal_date': date(2023, 6, 29)},
    {'trail_name': '<PERSON><PERSON><PERSON>V1418', 'trail_type': 'Open For Workspace', 'progress': 1.0, 'performance': 1.0, 'start_date': datetime(2023, 6, 21, 16, 38, 22, 626434), 'status': 'COMPLETED', 'end_date': datetime(2023, 6, 22, 15, 35, 35, 613069), 'goal_date': date(2023, 6, 21)},
    {'trail_name': 'Teste rota da notificações missão inativa', 'trail_type': 'Open For Workspace', 'progress': 1.0, 'performance': 1.0, 'start_date': datetime(2023, 8, 4, 12, 23, 27, 406168), 'status': 'COMPLETED', 'end_date': datetime(2023, 8, 4, 12, 23, 27, 525529), 'goal_date': date(2023, 8, 31)}
]

MISSION_ENROLLMENTS_DATA = [
    {'id': UUID('01f29e5d-ae5e-4afa-b9b6-9fa7c89377df'), 'mission_name': 'sdfsd f', 'minimum_performance': 0.0, 'progress': 0.0, 'mission_category': 'Atendimento', 'mission_type': 'Open For Workspace', 'performance': None, 'assessment_type': 'FULL', 'start_date': datetime(2023, 6, 21, 16, 38, 48, 479948), 'end_date': None, 'goal_date': date(2023, 6, 21), 'status': 'STARTED', 'mission_model': 'EXTERNAL_PROVIDER', 'total_mission_questions': 0, 'total_correct_answers': 0},
    {'id': UUID('0208e7a8-28a3-4b06-82f7-114e5b671924'), 'mission_name': 'Teste 999999', 'minimum_performance': 0.0, 'progress': 0.0, 'mission_category': 'Engajamento', 'mission_type': 'Open For Workspace', 'performance': None, 'assessment_type': 'FULL', 'start_date': datetime(2023, 4, 3, 18, 41, 24, 168887), 'end_date': None, 'goal_date': date(2022, 12, 17), 'status': 'WAITING_APPROVAL', 'mission_model': 'LIVE', 'total_mission_questions': 0, 'total_correct_answers': 0},
    {'id': UUID('031b39b9-7ae3-4f31-96f6-7865c6e3fb61'), 'mission_name': 'Customer Success', 'minimum_performance': 0.0, 'progress': 100.0, 'mission_category': 'Atendimento', 'mission_type': 'Open For Workspace', 'performance': 0.91, 'assessment_type': 'FULL', 'start_date': None, 'end_date': datetime(2023, 6, 12, 0, 0), 'goal_date': date(2022, 8, 31), 'status': 'COMPLETED', 'mission_model': 'EXTERNAL_PROVIDER', 'total_mission_questions': 0, 'total_correct_answers': 0},
    {'id': UUID('095ef6b2-ac81-40ca-825a-d10cbc60d75b'), 'mission_name': 'strinssssssg', 'minimum_performance': 0.0, 'progress': 1.0, 'mission_category': 'Teste - Categoria', 'mission_type': 'Public', 'performance': 1.0, 'assessment_type': 'QUIZ', 'start_date': datetime(2023, 4, 13, 15, 47, 3, 31648), 'end_date': datetime(2023, 4, 13, 15, 47, 26, 799527), 'goal_date': date(2022, 5, 12), 'status': 'COMPLETED', 'mission_model': 'INTERNAL', 'total_mission_questions': 0, 'total_correct_answers': 0}
]

PULSES_CONSUMED_DATA = [
    {'id': UUID('01f5a37e-846c-4264-8111-a52d050dbc98'), 'name': 'Não Listado', 'type': 'Video', 'duration': 497.0, 'consume_duration': timedelta(seconds=2919, microseconds=460788), 'channel_name': 'Testes do Vimeo', 'category': 'O agro é pop'},
    {'id': UUID('01fe0f0b-da42-46d7-8dd2-33affeaf4d03'), 'name': 'MEU PULSE', 'type': 'Image', 'duration': 10.0, 'consume_duration': timedelta(seconds=2455, microseconds=613000), 'channel_name': 'Canal do Gordinho', 'category': 'Engajamento'},
    {'id': UUID('0268d84a-0ed7-4e97-aaf7-41f1c5b86bd1'), 'name': 'Modelo Certificado', 'type': 'Image', 'duration': 0.0, 'consume_duration': timedelta(seconds=2109, microseconds=788000), 'channel_name': 'new teste', 'category': 'Technology'}
]

GROUPS_DATA = [
    {'id': UUID('19ebc1bc-f3a8-4667-8669-de9d95c5d57e'), 'name': 'Grupo teste DEV-2771', 'created_date': datetime(2022, 9, 27, 11, 28, 3, 978163), 'mission_enrollments_total': 1, 'mission_enrollments_completed_total': 0, 'mission_enrollments_in_progress_total': 0, 'performance_average': 0.0, 'progress_average': 0.0, 'trail_enrollments_total': 0, 'trail_enrollments_completed_total': 0, 'trail_enrollments_in_progress_total': 0},
    {'id': UUID('3ea48fe4-d392-46a8-930a-951feb3fbee5'), 'name': 'Grupo suporte', 'created_date': datetime(2022, 11, 24, 18, 14, 44, 284414), 'mission_enrollments_total': 2, 'mission_enrollments_completed_total': 0, 'mission_enrollments_in_progress_total': 0, 'performance_average': 0.0, 'progress_average': 0.0, 'trail_enrollments_total': 1, 'trail_enrollments_completed_total': 0, 'trail_enrollments_in_progress_total': 0},
    {'id': UUID('6e3c5c8c-1586-4990-9562-d27f8e86922c'), 'name': 'Opa', 'created_date': datetime(2023, 1, 18, 14, 20, 40, 166546), 'mission_enrollments_total': 23, 'mission_enrollments_completed_total': 1, 'mission_enrollments_in_progress_total': 1, 'performance_average': 1.0, 'progress_average': 0.21374999999999994, 'trail_enrollments_total': 22, 'trail_enrollments_completed_total': 5, 'trail_enrollments_in_progress_total': 1},
    {'id': UUID('846efb8e-9865-40dd-bacc-7980603fc24b'), 'name': 'Abc', 'created_date': datetime(2023, 10, 16, 16, 14, 49, 213174), 'mission_enrollments_total': 4, 'mission_enrollments_completed_total': 0, 'mission_enrollments_in_progress_total': 0, 'performance_average': 0.0, 'progress_average': 0.*****************, 'trail_enrollments_total': 6, 'trail_enrollments_completed_total': 3, 'trail_enrollments_in_progress_total': 0}
]

class TestUserPresentation(unittest.TestCase):

    def setUp(self):
        self.report_creator = MagicMock()
        self.user_query = MagicMock()
        self.workspace_query = MagicMock()
        self.account_query = MagicMock()
        self.locales = MagicMock()
        self.today = date(2023, 10, 16)
        self.user_id = 'user_id'
        self.workspace_id = 'workspace_id'

        self.user_presentation = UserPresentation(
            self.report_creator, self.user_query, self.workspace_query, self.account_query, self.locales
        )
        self.user_presentation._today = self.today
        self.user_presentation._user_id = self.user_id
        self.user_presentation._workspace_id = self.workspace_id

    def test_create_answer_performance(self):
        total_correct_answers = 5
        total_mission_questions = 10
        result = self.user_presentation.create_answer_performance(total_correct_answers, total_mission_questions)
        self.assertEqual(result, "50% (5/10)")

    def test_get_second_page_data(self):
        self.user_query.list_mission_enrollments.return_value = MISSION_ENROLLMENTS_DATA

        result = self.user_presentation.get_second_page_data()
        
        expected_result = {'enrollments_completed': [],
 'enrollments_in_progress': [],
 'mission_enrollments_completed': [{'assessment_type': 'FULL',
                                    'end_date': '---',
                                    'goal_date': '31/08/2022',
                                    'id': UUID('031b39b9-7ae3-4f31-96f6-7865c6e3fb61'),
                                    'minimum_performance': '0%',
                                    'mission_category': 'Atendimento',
                                    'mission_model': 'EXTERNAL_PROVIDER',
                                    'mission_name': 'Customer Success',
                                    'mission_type': 'Open For Workspace',
                                    'performance': '91%',
                                    'progress': '10000%',
                                    'quizzes': '0% (0/0)',
                                    'start_date': '---',
                                    'status': 'COMPLETED',
                                    'total_correct_answers': 0,
                                    'total_mission_questions': 0},
                                   {'assessment_type': 'QUIZ',
                                    'end_date': '---',
                                    'goal_date': '12/05/2022',
                                    'id': UUID('095ef6b2-ac81-40ca-825a-d10cbc60d75b'),
                                    'minimum_performance': '0%',
                                    'mission_category': 'Teste - Categoria',
                                    'mission_model': 'INTERNAL',
                                    'mission_name': 'strinssssssg',
                                    'mission_type': 'Public',
                                    'performance': '100%',
                                    'progress': '100%',
                                    'quizzes': '0% (0/0)',
                                    'start_date': '13/04/2023',
                                    'status': 'COMPLETED',
                                    'total_correct_answers': 0,
                                    'total_mission_questions': 0}],
 'mission_enrollments_completed_percentage': '50%',
 'mission_enrollments_completed_total': 2,
 'mission_enrollments_give_up_total': 0,
 'mission_enrollments_in_progress': [{'assessment_type': 'FULL',
                                      'end_date': '---',
                                      'goal_date': '21/06/2023',
                                      'id': UUID('01f29e5d-ae5e-4afa-b9b6-9fa7c89377df'),
                                      'minimum_performance': '0%',
                                      'mission_category': 'Atendimento',
                                      'mission_model': 'EXTERNAL_PROVIDER',
                                      'mission_name': 'sdfsd f',
                                      'mission_type': 'Open For Workspace',
                                      'performance': '---',
                                      'progress': '0%',
                                      'start_date': '21/06/2023',
                                      'status': 'STARTED',
                                      'total_correct_answers': 0,
                                      'total_mission_questions': 0},
                                     {'assessment_type': 'FULL',
                                      'end_date': '---',
                                      'goal_date': '17/12/2022',
                                      'id': UUID('0208e7a8-28a3-4b06-82f7-114e5b671924'),
                                      'minimum_performance': '0%',
                                      'mission_category': 'Engajamento',
                                      'mission_model': 'LIVE',
                                      'mission_name': 'Teste 999999',
                                      'mission_type': 'Open For Workspace',
                                      'performance': '---',
                                      'progress': '0%',
                                      'start_date': '03/04/2023',
                                      'status': 'WAITING_APPROVAL',
                                      'total_correct_answers': 0,
                                      'total_mission_questions': 0}],
 'mission_enrollments_in_progress_total': 2,
 'mission_enrollments_performance_average': '96%',
 'mission_enrollments_reproved_total': 0}

        self.assertEqual(result, expected_result)

    def test_get_first_page_data(self):
        self.user_query.list_trail_enrollments.return_value = TRAIL_ENROLLMENTS_DATA

        result = self.user_presentation.get_first_page_data()

        expected_result = {'trail_enrollments_completed': [{'end_date': '---',
                                  'goal_date': date(2023, 6, 21),
                                  'performance': '100%',
                                  'progress': '100%',
                                  'start_date': '---',
                                  'status': 'COMPLETED',
                                  'trail_name': 'Trilha Douglas - DEV1418',
                                  'trail_type': 'Open For Workspace'},
                                 {'end_date': '---',
                                  'goal_date': date(2023, 8, 31),
                                  'performance': '100%',
                                  'progress': '100%',
                                  'start_date': '---',
                                  'status': 'COMPLETED',
                                  'trail_name': 'Teste rota da notificações '
                                                'missão inativa',
                                  'trail_type': 'Open For Workspace'}],
 'trail_enrollments_completed_percentage': '50%',
 'trail_enrollments_completed_total': 2,
 'trail_enrollments_give_up_total': 0,
 'trail_enrollments_in_progress': [{'delay_in_days': 115,
                                    'end_date': '---',
                                    'goal_date': '23/06/2023',
                                    'performance': '---',
                                    'progress': '0%',
                                    'start_date': '---',
                                    'status': 'STARTED',
                                    'trail_name': 'Trilha teste 19/05/2023',
                                    'trail_type': 'Open For Workspace'},
                                   {'delay_in_days': 109,
                                    'end_date': '---',
                                    'goal_date': '29/06/2023',
                                    'performance': '---',
                                    'progress': '25%',
                                    'start_date': '---',
                                    'status': 'STARTED',
                                    'trail_name': 'trilha 01',
                                    'trail_type': 'Open For Workspace'}],
 'trail_enrollments_in_progress_total': 2,
 'trail_enrollments_performance_average': '100%',
 'trail_enrollments_reproved_total': 0}

        self.assertEqual(result, expected_result)

    def test_get_third_page_data(self):
        self.user_query.get_pulses_general_data.return_value = {'count_pulses_created': 179}
        self.user_query.list_pulses_consumed.return_value = PULSES_CONSUMED_DATA
        
        self.user_query.get_groups_general_data.return_value = {
            'count_groups_linked': 6, 'count_groups_missions': 34, 'enrollments_in_group_conclusion_rate': 0.4
        }
        self.user_query.list_user_groups.return_value = GROUPS_DATA

        result = self.user_presentation.get_third_page_data()

        expected_result = {
            'groups': [{'created_date': '27/09/2022',
             'group_name': 'Grupo teste DEV-2771',
             'mission_enrollment_completed': 0,
             'mission_enrollment_in_progress': 0,
             'mission_enrollments_total': 1,
             'performance_average': '0%',
             'progress_average': '0%',
             'trail_enrollments_completed': 0,
             'trail_enrollments_in_progress': 0,
             'trail_enrollments_total': 0},
            {'created_date': '24/11/2022',
             'group_name': 'Grupo suporte',
             'mission_enrollment_completed': 0,
             'mission_enrollment_in_progress': 0,
             'mission_enrollments_total': 2,
             'performance_average': '0%',
             'progress_average': '0%',
             'trail_enrollments_completed': 0,
             'trail_enrollments_in_progress': 0,
             'trail_enrollments_total': 1},
            {'created_date': '18/01/2023',
             'group_name': 'Opa',
             'mission_enrollment_completed': 1,
             'mission_enrollment_in_progress': 1,
             'mission_enrollments_total': 23,
             'performance_average': '100%',
             'progress_average': '21%',
             'trail_enrollments_completed': 5,
             'trail_enrollments_in_progress': 1,
             'trail_enrollments_total': 22},
            {'created_date': '16/10/2023',
             'group_name': 'Abc',
             'mission_enrollment_completed': 0,
             'mission_enrollment_in_progress': 0,
             'mission_enrollments_total': 4,
             'performance_average': '0%',
             'progress_average': '4%',
             'trail_enrollments_completed': 3,
             'trail_enrollments_in_progress': 0,
             'trail_enrollments_total': 6}],
             'groups_total': 6,
             'pulses_consumed': [{'channel_name': 'Testes do Vimeo',
                                  'consume_progress': '100%',
                                  'pulse_category': 'O agro é pop',
                                  'pulse_name': 'Não Listado',
                                  'pulse_type': 'Video'},
                                 {'channel_name': 'Canal do Gordinho',
                                  'consume_progress': '100%',
                                  'pulse_category': 'Engajamento',
                                  'pulse_name': 'MEU PULSE',
                                  'pulse_type': 'Image'},
                                 {'channel_name': 'new teste',
                                  'consume_progress': '100%',
                                  'pulse_category': 'Technology',
                                  'pulse_name': 'Modelo Certificado',
                                  'pulse_type': 'Image'}],
             'pulses_consumed_total': 3,
             'pulses_created_total': 179}

        self.assertEqual(result, expected_result)
