from datetime import datetime

from reports.filter.filter_sql import FilterSql


def test_filter_sql_date_parser():
    filters = {
        "date__lte": datetime.now(),
        "date__gte": datetime.now()
    }
    filter_sql = FilterSql().builder_sql_filter(
        'table',
        filters=filters,
        keys_filter_dates=(("date__gte", "date__lte"),)
    )

    assert filter_sql == f" AND table.date <=  '{filters['date__lte']}' AND table.date >=  '{filters['date__gte']}'"
