from unittest.mock import MagicMock

from sqlalchemy.engine import Engine

from domain.report.models import Report
from reports.export import ExportXlsxCreator, ExportCsvCreator
from reports.export.main.generic_export_builder import GenericExportBuilder
from reports.formatters.formatter_service import FormatterService
from reports.query.query_executor_with_config_injectable import QueryExecutorWithConfigInjectable


def test_smartzap_enrollments_export_service():
    expected_url = "https://url.com"
    xlsx_creator = MagicMock(ExportXlsxCreator)
    xlsx_creator.create.return_value = "https://url.com"
    csv_creator = MagicMock(ExportCsvCreator)
    query_selector = MagicMock(QueryExecutorWithConfigInjectable)
    formatter_service = MagicMock(FormatterService)
    report = Report(workspace_id='1234', file_format="XLSX")
    export_service = GenericExportBuilder(xlsx_creator, csv_creator, query_selector, formatter_service)

    report = export_service.generate(report)

    assert report.url == expected_url

