import unittest
from unittest.mock import Mock, patch, MagicMock

from Locales import Locales

from reports.presentation import PresentationCreator


class TestPresentationCreator(unittest.TestCase):

    def setUp(self):
        # Mock dependencies
        self.mock_pdf_creator = Mock()
        self.mock_report_uploader = Mock()
        self.mock_locale = MagicMock(spec=Locales)
        self.mock_locale.languages = ["en"]
        self.mock_locale.messages = []
        self.mock_webhook_logger = Mock()

        # Instantiate the PresentationCreator
        self.creator = PresentationCreator(
            pdf_creator=self.mock_pdf_creator,
            report_uploader=self.mock_report_uploader,
            locale=self.mock_locale,
            webhook_logger=self.mock_webhook_logger
        )

    @patch('uuid.uuid4')
    def test_not_call_upload_function_when_pages_are_empty(self, mock_uuid):
        pages = []
        mock_uuid.return_value = 'test_uuid'

        result = self.creator.create(pages, "test_report", "en", 1)

        self.assertIsNone(result)
        self.mock_report_uploader.assert_not_called()
