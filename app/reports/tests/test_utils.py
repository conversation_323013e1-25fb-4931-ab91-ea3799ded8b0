import os

import pytest
from Locales import Locales

import reports as reports
from config.default import Config


@pytest.fixture
def utils_pt():
    locale = Locales(Config.LOCALE_JSON)
    locale.set_default_lang('pt-BR')
    return reports.utils.Utils(locale)


@pytest.fixture
def utils_en():
    locale = Locales(Config.LOCALE_JSON)
    locale.set_default_lang('en')
    return reports.utils.Utils(locale)


@pytest.fixture
def report_export():
    return reports.di.ReportContainer()._report_export


def test_translate(utils_pt, utils_en):
    label = 'test'

    assert utils_pt.translate(label) == 'teste'
    assert utils_en.translate(label) == 'test'


def test_list_to_csv(utils_en, report_export, konquest_company_missions_export_data):
    data = konquest_company_missions_export_data

    file = report_export.list_to_csv(data, 'test-report')

    name, extension = os.path.splitext(file)

    assert name is not None
    assert extension == '.csv'

    os.remove(file)


def test_list_to_xlsx(report_export, konquest_company_missions_export_data):
    data = konquest_company_missions_export_data

    file = report_export.list_to_xlsx(data, 'test-report')

    name, extension = os.path.splitext(file)

    assert name is not None
    assert extension == '.xlsx'

    os.remove(file)


def test_list_to_xlsx_empty(utils_en, report_export):
    data = []

    file = report_export.list_to_xlsx(data, 'test-report')

    name, extension = os.path.splitext(file)

    assert name is not None
    assert extension == '.xlsx'

    os.remove(file)


def test_merge_csv(report_export,
                   konquest_company_missions_export_data,
                   konquest_company_missions_enrollments_export_data):
    data_main = konquest_company_missions_export_data
    data_complementary = konquest_company_missions_enrollments_export_data

    file_main = report_export.list_to_csv(data_main)
    file_complementary = report_export.list_to_csv(data_complementary)

    file_merged = report_export.merge_csv(file_main, file_complementary, 'mission_id', 'test-report')

    name, extension = os.path.splitext(file_merged)

    assert name is not None
    assert extension == '.xlsx'

    os.remove(file_merged)
    os.remove(file_main)
    os.remove(file_complementary)


def test_calc_the_rate(utils_en):
    rate = utils_en.calc_the_rate(300, 100)
    assert rate == 2

    rate = utils_en.calc_the_rate(300, 0)
    assert rate == 0


def test_format_percentage(utils_en):
    percentage = utils_en.format_to_percentage(0.3, 2)

    assert percentage == '30%'


def test_format_to_hours(utils_en):
    hours = utils_en.format_to_hours(7600)

    assert hours == '02:06'


def test_filter_list(utils_en, konquest_company_missions_export_data):
    list_filtered = utils_en.filter_list(konquest_company_missions_export_data,
                                         "Aberto Para Empresa",
                                         "mission_type")
    mission_type = [mission["mission_type"] for mission in list_filtered]

    assert "Aberto Para Empresa" in mission_type
    assert "Fechado Para Empresa" not in mission_type


def test_filter_list_interval(utils_en, konquest_company_missions_export_data):
    list_data = [0.02, 0.03, 0.4, 0.1, 1]
    list_filtered = utils_en.filter_list_interval(list_data, 0.3, 1.1)

    assert list_filtered == [0.4, 1]


def test_is_valid_uuid(utils_en):
    assert utils_en.is_valid_uuid('9543c838-e93c-4955-a185-106796c4eaa8')
    assert not utils_en.is_valid_uuid('9543c838-e93c-4955-a185-106796c4eaa')
