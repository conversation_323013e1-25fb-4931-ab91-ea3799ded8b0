from reports.query import QueryExecutor
from sqlalchemy.engine import Engine


class SmartzapCourseQuery(QueryExecutor):
    def __init__(self, database_engine: Engine, template_folder: str):
        super().__init__(database_engine, template_folder)

    def get_by_id(self, course_id: str) -> dict:
        query_kwargs = {"course_id": str(course_id)}
        return self.get_first("get_course_by_id", **query_kwargs)

    def get_enrollments_data(self, course_id: str) -> dict:
        query_kwargs = {"course_id": str(course_id)}
        return self.get_first("get_course_enrollments_data", **query_kwargs)

    def get_enrollment_performances_range_data(self, course_id: str) -> dict:
        query_kwargs = {"course_id": str(course_id)}
        return self.get_first("get_course_enrollment_performances_range_data", **query_kwargs)

    def list_contents_consume_data(self, course_id: str) -> list:
        query_kwargs = {"course_id": str(course_id)}
        return self.list("list_course_contents_consume_data", **query_kwargs)

    def list_question_contents(self, course_id: str) -> list:
        query_kwargs = {"course_id": str(course_id)}
        return self.list("list_course_question_contents", **query_kwargs)

    def list_enrollment_performance_ranking(self, course_id: str) -> list:
        query_kwargs = {"course_id": str(course_id)}
        return self.list("list_course_enrollment_performances_ranking", **query_kwargs)

    def list_enrollments(self, course_id: str) -> list:
        query_kwargs = {"course_id": str(course_id)}
        return self.list("list_course_enrollments", **query_kwargs)
