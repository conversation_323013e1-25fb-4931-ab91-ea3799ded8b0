SELECT
    COUNT(e.id) as "count_enrollments",
    COALESCE(SUM(CASE WHEN e.performance = 0 THEN 1 ELSE 0 END), 0) as "0",
	COALESCE(SUM(CASE WHEN e.performance BETWEEN 0 AND 0.0999 THEN 1 ELSE 0 END), 0) as "0-10",
	COALESCE(SUM(CASE WHEN e.performance BETWEEN 0.10 AND 0.2999 THEN 1 ELSE 0 END), 0) as "10-30",
	COALESCE(SUM(CASE WHEN e.performance BETWEEN 0.30 AND 0.4999 THEN 1 ELSE 0 END), 0) as "30-50",
	COALESCE(SUM(CASE WHEN e.performance BETWEEN 0.50 AND 0.6999 THEN 1 ELSE 0 END), 0) as "50-70",
	COALESCE(SUM(CASE WHEN e.performance BETWEEN 0.70 AND 1.00 THEN 1 ELSE 0 END), 0) as "70-100"
FROM
    enrollment as e
WHERE
    e.course_id = '$course_id'