SELECT
    activity.user_id as "user_id",
    "user".name as "user_name",
    '+' || "user".phone as "user_phone",
    "user".email as "user_email",
    "user".tags as "user_tags",
    lesson.name as "lesson_name",
    content.name as "content_name",
    activity.action as "action",
    activity.start_at as "start_at",
    activity.stop_at as "stop_at",
    activity.duration as "duration"
FROM
    activity
JOIN
    "user" on activity.user_id = "user".id
JOIN
    content on activity.content_id = content.id
JOIN
    lesson on content.lesson_id = lesson.id
JOIN
    enrollment on "user".id = enrollment.user_id
WHERE
    enrollment.workspace_id = '$workspace_id'
$filters
ORDER BY activity.enrollment_id
