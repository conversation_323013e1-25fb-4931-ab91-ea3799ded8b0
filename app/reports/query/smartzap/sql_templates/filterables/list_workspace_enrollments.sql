SELECT
    enrollment.id as "enrollment_id",
    "user".name as "user_name",
    "user".phone as "user_phone",
    "user".email as "user_email",
    "user".tags as "user_tags",
    lesson.name as "current_lesson",
    content.name as "current_content",
    enrollment.status as "status",
    CONCAT((COALESCE(
     CASE
	    WHEN enrollment.progress = 100 AND enrollment.status = 'STARTED' THEN 99
	    ELSE enrollment.progress 
	 END, 0)), '%%') AS "progress",
    CONCAT((COALESCE(enrollment.performance, 0) * 100), '%%') as "performance",
    enrollment.start_date AS "first_activity",
	enrollment.updated AS "last_activity"
FROM 
    enrollment 
JOIN 
    "user" ON enrollment.user_id = "user".id
LEFT JOIN
    lesson ON enrollment.current_lesson_id = lesson.id
LEFT JOIN
    content ON enrollment.current_content_id = content.id
WHERE 
    enrollment.workspace_id = '$workspace_id' 
$filters
