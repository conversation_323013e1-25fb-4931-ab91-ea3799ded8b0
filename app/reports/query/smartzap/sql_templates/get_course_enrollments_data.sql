SELECT
    COUNT(e.id) as "count_enrollments",
    COUNT(e.id) filter(where e.status = 'COMPLETED') as "count_finished_enrollments",
    COUNT(e.id) filter(where e.status = 'REFUSED') as "count_refused_enrollments",
    COUNT(e.id) filter(where e.status = 'WAITING') as "count_waiting_enrollments",
    COUNT(e.id) filter(where e.status = 'ERROR') as "count_error_enrollments",
    COUNT(e.id) filter(where e.status = 'STARTED') as "count_active_enrollments"
FROM
    enrollment as e
WHERE
    e.course_id = '$course_id'