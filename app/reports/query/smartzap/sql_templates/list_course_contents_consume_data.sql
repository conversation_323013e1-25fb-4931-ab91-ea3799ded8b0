SELECT
    ct.id,
    ct.name as "content_name",
    COALESCE(SUM(ac.duration), 0) as "sum_consume_time",
    COUNT(DISTINCT(ac.user_id)) as "count_user_access"
FROM
	"content" as ct
JOIN
    lesson l on l.id=ct.lesson_id
LEFT JOIN
    activity as ac on ac.content_id = ct.id
JOIN
    course as c on c.id=l.course_id
WHERE
	c.id = '$course_id' and ct.type_id != '7a41a8e0-ee37-4d0b-ad4f-35bada67134d'
GROUP BY
    ct.id, ct.name
ORDER BY
    ct.name;
