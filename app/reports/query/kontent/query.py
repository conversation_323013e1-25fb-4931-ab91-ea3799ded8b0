from typing import Dict, List

from reports.query import QueryExecutor
from sqlalchemy.engine import Engine


class KontentQuery(QueryExecutor):
    def __init__(self, kontent_database_engine: Engine, template_folder: str):
        super().__init__(kontent_database_engine, template_folder)

    def list_learn_contents(self, learn_content_ids: List) -> List:
        query_kwargs = {"learn_content_id_list": learn_content_ids}
        return self.list('get_learn_content_by_id', **query_kwargs)

    def list_quizzes_answers_data(self, learn_content_ids: List) -> List:
        query_kwargs = {"learn_content_ids": learn_content_ids}
        quizzes = self.list('list_quizzes_answers_data', **query_kwargs)
        count_answers_by_user = self.list('list_users_total_answers_by_exam', **query_kwargs)

        for quiz in quizzes:
            count_questions = quiz["count_questions"]
            quiz["count_users_finished"] = len(list(filter(
                lambda row: row['count_questions_answered'] == count_questions and row["quiz_id"] == quiz["quiz_id"],
                count_answers_by_user
            )))

        return quizzes

    def count_content_types(self, learn_content_ids: List) -> Dict:
        contents = self.list_learn_contents(learn_content_ids)
        count_types = {}
        for content in contents:
            content_type_id = str(content.get("content_type_id"))
            if content_type_id in count_types:
                count_types[content_type_id] = count_types[content_type_id] + 1
            else:
                count_types.update({content_type_id: 1})
        return count_types

    def list_answer_hit_ranking_by_enrollment(self, enrollment_ids: List) -> List:
        query_kwargs = {"enrollment_ids": enrollment_ids}
        return self.list("list_answer_hit_ranking_by_enrollments", **query_kwargs)

    def count_questions(self, learn_content_ids: List) -> int:
        query_kwargs = {"learn_content_ids": learn_content_ids}
        return self.get_first("count_exam_questions", **query_kwargs)["count_questions"]
