from dataclasses import dataclass
from typing import Optional, <PERSON><PERSON>

from reports.filter.filter_sql import FilterSql
from sqlalchemy.engine import Engine


@dataclass
class QuerySelectorConfig:
    database_engine: Engine
    template_folder: str
    filter_sql: FilterSql
    table_name: str
    foreign_tables_names: Tuple
    query_name: str
    keys_filters_dates: Tuple[Tuple[str, str]]
    limit_filter_date_range: Optional[int] = None
