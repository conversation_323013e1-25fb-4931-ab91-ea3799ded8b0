from typing import List

from config import default
from reports.query import QueryExecutor
from sqlalchemy.engine import Engine


class AccountQuery(QueryExecutor):
    def __init__(self, account_database_connection: Engine, template_folder: str):
        super().__init__(account_database_connection, template_folder)

    def get_konquest_users_by_workspace(self, workspace_id: str) -> List[dict]:
        query_kwargs = {
            "workspace_id": str(workspace_id),
            "konquest_application_id": default.Config.KONQUEST_APPLICATION_ID
        }
        return self.list("list_konquest_users_by_workspace", **query_kwargs)

    def get_user(self, user_id: str, workspace_id: str) -> dict:
        query_kwargs = {"user_id": user_id, "workspace_id": workspace_id}
        return self.get_first("get_user_by_id", **query_kwargs)

    def count_konquest_users_by_workspace(self, workspace_id: str) -> dict:
        query_kwargs = {
            "workspace_id": str(workspace_id),
            "konquest_application_id": default.Config.KONQUEST_APPLICATION_ID
        }
        return self.get_first("count_konquest_users_by_workspace", **query_kwargs)
