from typing import List, Optional, Sequence, Set, Union

from pandas import DataFrame
from reports.filter.filter_sql import FilterSql
from reports.query import QueryExecutor
from reports.utils import list_to_sql_repr
from sqlalchemy.engine import Engine


class AccountExportQuery(QueryExecutor):
    def __init__(self, account_database_engine: Engine, template_folder: str):
        super().__init__(account_database_engine, template_folder)
        self._filter = FilterSql()

    def list_users(
        self,
        workspace_id: str,
        limit_filter_date_range: Optional[int] = None,
        filters: Optional[dict] = None
    ) -> DataFrame:
        keys_filters_dates = (("created_date__gte", "created_date__lte"),)

        sql_filter = self._filter.builder_sql_filter(
            table_name="user",
            filters=filters,
            keys_filter_dates=keys_filters_dates,
            default_keys_filter_dates=keys_filters_dates[0],
            limit_filter_date_range=limit_filter_date_range,
            foreign_tables_names=("role", "user_profile_workspace", "user_role_workspace")
        )
        users = self.list("list_workspace_users", **{"workspace_id": workspace_id, "filters": sql_filter})
        return DataFrame(users)

    def list_users_by_emails(
        self,
        emails: Sequence[str],
        workspace_id: str,
        columns: List[str] = None
    ) -> DataFrame:
        sql_filter = self._filter.builder_sql_filter(
            table_name="user",
            filters={"email__in": emails},
        )
        users = self.list("list_workspace_users", **{"workspace_id": workspace_id, "filters": sql_filter})
        df = DataFrame(users)
        if columns:
            columns_to_drop = list(filter(lambda column: column not in columns, df.columns))
            df = df.drop(columns=columns_to_drop)
        return df

    def list_users_without_permission_filter(
        self,
        emails: Union[Sequence[str], Set[str]],
        workspace_id: str
    ) -> DataFrame:
        emails_sql_repr = list_to_sql_repr(emails)
        users = self.list(
            "list_workspace_users_without_permission_filter",
            **{"workspace_id": workspace_id, "user_emails": emails_sql_repr},

        )
        return DataFrame(users)

    def list_workspaces_by_companies(self, application_id: str) -> DataFrame:
        workspaces = self.list(
            "list_workspaces_by_companies",
            **{"application_id": application_id},

        )
        return DataFrame(workspaces)
