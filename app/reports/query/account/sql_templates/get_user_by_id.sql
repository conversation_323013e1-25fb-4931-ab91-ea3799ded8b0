SELECT
    u.name,
    u.email,
    upw.director,
    upw.manager,
    upw.area_of_activity,
    u.created_date,
    u.updated_date,
    j.name as job,
    jf.name as job_function,
	rul.name AS related_user_leader
FROM
    public."user" u
LEFT JOIN
    public.user_profile_workspace upw ON u.id = upw.user_id and upw.workspace_id='$workspace_id'
LEFT JOIN
    public.job j ON upw.job_position_id = j.id
LEFT JOIN
    public.job_function jf ON upw.job_function_id = jf.id
LEFT JOIN
    public."user" rul ON u.related_user_leader_id = rul.id
WHERE
    u.id = '$user_id' and u.id in (
        SELECT user_id FROM user_role_workspace WHERE user_role_workspace.workspace_id = '$workspace_id'
    )
