SELECT
    workspace.id as "workspace_id",
    workspace.name as "workspace_name",
    company.id as "company_id",
    company.name as "company_name"
FROM workspace
JOIN company ON company.id = workspace.company_id
WHERE workspace.id IN (
        SELECT service_workspace.workspace_id
        FROM service_workspace
        LEFT JOIN service ON service.id = service_workspace.service_id
        WHERE service.application_id = '$application_id'
    );