WITH DirectorCounts AS (
    SELECT
        upw.director,
        COUNT(DISTINCT upw.user_id) AS director_user_count
    FROM user_profile_workspace upw
    JOIN user_role_workspace urw
        ON urw.workspace_id = '$workspace_id'
       AND urw.user_id = upw.user_id
    WHERE upw.workspace_id = '$workspace_id'
    GROUP BY upw.director
),
ManagerCounts AS (
    SELECT
        upw.manager,
        COUNT(DISTINCT upw.user_id) AS manager_user_count
    FROM user_profile_workspace upw
    JOIN user_role_workspace urw
        ON urw.workspace_id = '$workspace_id'
       AND urw.user_id = upw.user_id
    WHERE upw.workspace_id = '$workspace_id'
    GROUP BY upw.manager
),
AreaOfActivityCounts AS (
    SELECT
        upw.area_of_activity,
        COUNT(DISTINCT upw.user_id) AS area_activity_user_count
    FROM user_profile_workspace upw
    JOIN user_role_workspace urw
        ON urw.workspace_id = '$workspace_id'
       AND urw.user_id = upw.user_id
    WHERE upw.workspace_id = '$workspace_id'
    GROUP BY upw.area_of_activity
)
SELECT
    DISTINCT("user".id) AS user_id,
    "user".created_date AS user_registration_date,
    "user".name AS user_name,
    "user".nickname AS user_nickname,
    "user".status AS user_status,
    "user".email AS user_email,
    "user".phone AS user_phone,
    "user".address AS user_address,
    "user".country AS user_country,
    "user".ein AS user_ein,
    COALESCE(related_user_leader.email, '----') AS user_leader_email,
    COALESCE(job.name, '----') AS user_job,
    COALESCE(job_function.name, '----') AS user_job_function,
    COALESCE(user_profile_workspace.director, '----') AS user_director,
    COALESCE(dc.director_user_count, 0) AS user_director_users_enabled,
    COALESCE(user_profile_workspace.manager, '----') AS user_manager,
    COALESCE(mc.manager_user_count, 0) AS user_manager_users_enabled,
    COALESCE(user_profile_workspace.area_of_activity, '----') AS user_area_of_activity,
    COALESCE(aoc.area_activity_user_count, 0) AS user_area_of_activity_users_enabled,
    "user".cpf AS user_cpf,
    "user".admission_date AS user_admission_date,
    "user".ethnicity AS user_ethnicity,
    "user".marital_status AS user_marital_status,
    "user".education AS user_education,
    "user".hierarchical_level AS user_hierarchical_level,
    "user".contract_type AS user_contract_type,
    "user".gender,
    "user".birthday,
    CASE WHEN (
        SELECT 1
        FROM user_role_workspace
        WHERE user_role_workspace.user_id = "user".id
          AND user_role_workspace.workspace_id = '$workspace_id'
        LIMIT 1
    )::int = 1 THEN True ELSE False END AS user_enabled
FROM
    "user"
LEFT JOIN "user" related_user_leader
    ON related_user_leader.id = "user".related_user_leader_id
LEFT JOIN user_profile_workspace
    ON user_profile_workspace.user_id = "user".id
   AND user_profile_workspace.workspace_id = '$workspace_id'
LEFT JOIN job
    ON user_profile_workspace.job_position_id = job.id
LEFT JOIN job_function
    ON user_profile_workspace.job_function_id = job_function.id
LEFT JOIN DirectorCounts dc
    ON dc.director = user_profile_workspace.director
LEFT JOIN ManagerCounts mc
    ON mc.manager = user_profile_workspace.manager
LEFT JOIN AreaOfActivityCounts aoc
    ON aoc.area_of_activity = user_profile_workspace.area_of_activity
WHERE
    "user".email in ('$user_emails')
