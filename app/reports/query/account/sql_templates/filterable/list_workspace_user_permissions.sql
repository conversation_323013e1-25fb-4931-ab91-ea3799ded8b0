SELECT
    "user".id AS "user_id",
    "user".name AS "user_name",
    "user".email AS "user_email",
    "user".status AS "user_status",
    array_to_string(array_agg(DISTINCT(COALESCE(job.name, ''))), '; ', '') AS "user_job",
    array_to_string(array_agg(DISTINCT(COALESCE(job_function.name, ''))), '; ', '') AS "user_job_function",
    "user".country::varchar(40) AS "user_country",
    "user".ein AS "user_ein",
    related_user.email::varchar(255) AS "user_leader",
    role.name AS "permission",
    application.name as "application",
    user_role_workspace.created_date as "permission_date"
FROM
    "user"
JOIN
    user_role_workspace ON user_role_workspace.user_id = "user".id
JOIN
    workspace ON user_role_workspace.workspace_id = workspace.id
JOIN
    role ON user_role_workspace.role_id = role.id
LEFT JOIN
    user_profile_workspace ON user_profile_workspace.user_id = "user".id AND user_profile_workspace.workspace_id = workspace.id
LEFT JOIN
    job ON user_profile_workspace.job_position_id = job.id
LEFT JOIN
    job_function ON user_profile_workspace.job_function_id = job_function.id
LEFT JOIN
    "user" related_user ON related_user.id = "user".related_user_leader_id
LEFT JOIN
    application ON role.application_id = application.id
WHERE
    workspace.id = '$workspace_id'
$filters
GROUP BY
    "user".id, "user".name, "user".email, "user".status, "user".country, "user".ein, related_user.email, role.name, application.name, user_role_workspace.created_date;
