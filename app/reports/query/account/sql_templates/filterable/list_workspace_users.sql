SELECT
    DISTINCT(user_role_workspace.user_id) as "user_id",
    "user".created_date as "user_registration_date",
    "user".name as "user_name",
    "user".nickname as "user_nickname",
    "user".status as "user_status",
    "user".email as "user_email",
    "user".phone as "user_phone",
    "user".address as "user_address",
    "user".country as "user_country",
    "user".ein as "user_ein",
    COALESCE(related_user_leader.email, '----') as "user_leader_email",
    COALESCE(job.name::varchar(255), '----') as "user_job",
    COALESCE(job_function.name::varchar(255), '----') as "user_job_function",
    COALESCE(user_profile_workspace.director::varchar(255), '----') as "user_director",
    COALESCE(user_profile_workspace.manager::varchar(255), '----') as "user_manager",
    COALESCE(user_profile_workspace.area_of_activity::varchar(255), '----') as "user_area_of_activity",
    "user".cpf as "user_cpf",
    "user".admission_date as "user_admission_date",
    "user".ethnicity as "user_ethnicity",
    "user".marital_status as "user_marital_status",
    "user".education as "user_education",
    "user".hierarchical_level as "user_hierarchical_level",
    "user".contract_type as "user_contract_type",
    "user".gender as "gender",
    "user".birthday as "birthday"
FROM
    "user"
LEFT JOIN
    "user" related_user_leader on related_user_leader.id = "user".related_user_leader_id
JOIN
    user_role_workspace on user_role_workspace.user_id = "user".id
LEFT JOIN
    user_profile_workspace on user_profile_workspace.user_id = "user".id and user_profile_workspace.workspace_id = '$workspace_id'
LEFT JOIN
    job on user_profile_workspace.job_position_id = job.id
LEFT JOIN
    job_function on user_profile_workspace.job_function_id = job_function.id
JOIN
    role on user_role_workspace.role_id = role.id
WHERE
    user_role_workspace.workspace_id = '$workspace_id'
$filters
