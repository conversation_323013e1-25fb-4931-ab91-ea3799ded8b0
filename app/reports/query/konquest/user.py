from datetime import datetime

from reports.query import QueryExecutor
from sqlalchemy.engine import Engine


class KonquestUserQuery(QueryExecutor):
    def __init__(self, konquest_database_engine: Engine, template_folder: str):
        super().__init__(konquest_database_engine, template_folder)

    def get_user_general_data(self, user_id: str, workspace_id: str, interval_days: int, today: datetime) -> dict:
        query_kwargs = {
            "user_id": str(user_id),
            "workspace_id": str(workspace_id),
            "interval_days": interval_days,
            "today_datetime": today,
        }
        return self.get_first("get_user_general_data", **query_kwargs)

    def list_completed_mission_enrollments(self, user_id: str, workspace_id: str) -> list:
        return self.list("list_user_completed_mission_enrollments", **{"user_id": user_id, "workspace_id": workspace_id})

    def list_trail_enrollments(self, user_id: str, workspace_id: str) -> list:
        return self.list("list_user_trail_enrollments", **{"user_id": user_id, "workspace_id": workspace_id})

    def list_mission_enrollments(self, user_id: str, workspace_id: str) -> list:
        return self.list("list_user_mission_enrollments", **{"user_id": user_id, "workspace_id": workspace_id})

    def get_mission_enrollments_general_data(self, user_id: str, workspace_id: str) -> dict:
        return self.get_first("get_user_mission_enrollment_data", **{"user_id": user_id, "workspace_id": workspace_id})

    def get_pulses_general_data(self, user_id: str, workspace_id: str) -> dict:
        return self.get_first("get_user_pulses_general_data", **{"user_id": user_id, "workspace_id": workspace_id})

    def list_pulses_consumed(self, user_id: str, workspace_id: str) -> list:
        return self.list("list_user_pulses_consumed", **{"user_id": user_id, "workspace_id": workspace_id})

    def get_groups_general_data(self, user_id: str, workspace_id: str) -> dict:
        return self.get_first("get_user_groups_general_data", **{"user_id": user_id, "workspace_id": workspace_id})

    def list_user_groups(self, user_id: str, workspace_id: str) -> list:
        return self.list("list_user_groups", **{"user_id": user_id, "workspace_id": workspace_id})

    def list_top_categories_consumed(self, user_id: str, workspace_id: str) -> list:
        return self.list("list_top_consumed_categories", **{"user_id": user_id, "workspace_id": workspace_id})
