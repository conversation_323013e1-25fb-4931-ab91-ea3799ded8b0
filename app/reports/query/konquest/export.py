import datetime
from typing import Optional

from config.default import Config
from pandas import DataFrame
from pytz import timezone as get_timezone
from reports.constants import DEFAULT_TIME_ZONE
from reports.filter.filter_sql import FilterSql
from reports.query import QueryExecutor
from reports.query.account.export import AccountExportQuery
from reports.utils import filter_columns
from sqlalchemy.engine import Engine

USER_EMAIL = "user_email"
USER_PROFILE_WORKSPACE = "user_profile_workspace"


class KonquestExportQuery(QueryExecutor):
    def __init__(
        self,
        konquest_database_engine: Engine,
        account_export_query: AccountExportQuery,
        template_folder: str,
        filter_sql: FilterSql
    ):
        super().__init__(konquest_database_engine, template_folder)
        self._filter = filter_sql
        self.account_export_query = account_export_query
        self.profile_filter_fields = ["director__in", "manager__in", "area_of_activity__in"]

    def _move_columns(self, columns_list, columns_to_move: list, after_column: str) -> list:
        columns_list = [column for column in columns_list if column not in columns_to_move]
        for column in reversed(columns_to_move):
            columns_list.insert(columns_list.index(after_column) + 1, column)
        return columns_list

    def list_missions(
            self,
            workspace_id: str,
            limit_filter_date_range: Optional[int] = None,
            filters: Optional[dict] = None
    ) -> DataFrame:
        keys_filters_dates = (("created_date__gte", "created_date__lte"),)

        sql_filter = self._filter.builder_sql_filter(
            table_name="mission",
            filters=filters,
            keys_filter_dates=keys_filters_dates,
            default_keys_filter_dates=keys_filters_dates[0],
            foreign_tables_names=("group", "mission_provider"),
            limit_filter_date_range=limit_filter_date_range
        )
        missions = self.list("list_workspace_missions", **{"workspace_id": workspace_id, "filters": sql_filter})
        return DataFrame(missions)

    def list_mission_enrollments(
            self,
            workspace_id: str,
            limit_filter_date_range: Optional[int] = None,
            filters: Optional[dict] = None
    ):
        keys_filters_dates = (("start_date__gte", "start_date__lte"), ("end_date__gte", "end_date__lte"))
        sql_filter = self._filter.builder_sql_filter(
            table_name="mission_enrollment",
            foreign_tables_names=("user", "mission", "mission_provider", "learning_trail_step", "job", "job_function"),
            filters=filters,
            keys_filter_dates=keys_filters_dates,
            default_keys_filter_dates=keys_filters_dates[0],
            limit_filter_date_range=limit_filter_date_range
        )
        enrollments = self.list(
            "list_workspace_mission_enrollments", **{"workspace_id": workspace_id, "filters": sql_filter}
        )

        user_emails = [enrollment[USER_EMAIL] for enrollment in enrollments]
        enrollments_frame = DataFrame(enrollments)
        if user_emails:
            users = self.account_export_query.list_users_by_emails(user_emails, workspace_id)
            user_columns = ["user_director", "user_manager", "user_area_of_activity", USER_EMAIL]
            users = filter_columns(users, user_columns)
            enrollments_frame = enrollments_frame.merge(users, how='left', on=USER_EMAIL, validate="many_to_one")
            # Como o merge dos dataframes adiciona as colunas ao final - e elas precisam ficar logo após "internal_code",
            # para manter a ordem de criação das colunas (havendo novas colunas), fazemos o tratamento abaixo
            user_columns.remove(USER_EMAIL)
            columns_list = self._move_columns(enrollments_frame.columns.tolist(), user_columns, "internal_code")
            enrollments_frame = enrollments_frame.reindex(columns=columns_list)

        return enrollments_frame

    def list_learning_trail_enrollments(
            self,
            workspace_id: str,
            limit_filter_date_range: Optional[int] = None,
            filters: Optional[dict] = None
    ):
        keys_filters_dates = (("start_date__gte", "start_date__lte"), ("end_date__gte", "end_date__lte"))
        sql_filter = self._filter.builder_sql_filter(
            table_name="mission_enrollment",
            foreign_tables_names=("user", "mission", "mission_provider", "learning_trail_step"),
            filters=filters,
            keys_filter_dates=keys_filters_dates,
            default_keys_filter_dates=keys_filters_dates[0],
            limit_filter_date_range=limit_filter_date_range
        )
        enrollments = self.list("list_workspace_learning_trail_enrollments", **{"workspace_id": workspace_id, "filters": sql_filter})
        return DataFrame(enrollments)

    def list_learning_trail_completion_rate(
            self,
            workspace_id: str,
            limit_filter_date_range: Optional[int] = None,
            filters: Optional[dict] = None
    ):
        keys_filters_dates = (("start_date__gte", "start_date__lte"), ("end_date__gte", "end_date__lte"))
        sql_filter = self._filter.builder_sql_filter(
            table_name="learning_trail_enrollment",
            foreign_tables_names=("user", "mission", "learning_trail_step"),
            filters=filters,
            keys_filter_dates=keys_filters_dates,
            default_keys_filter_dates=keys_filters_dates[0],
            limit_filter_date_range=limit_filter_date_range
        )
        enrollments = self.list("list_workspace_learning_trail_completion_rate", **{"workspace_id": workspace_id, "filters": sql_filter})
        enrollments_frame = DataFrame(enrollments)

        if not enrollments_frame.empty:
            emails = enrollments_frame[USER_EMAIL].unique().tolist()
            columns = ["user_director", "user_manager", "user_area_of_activity", "user_leader_email", USER_EMAIL]
            users = self.account_export_query.list_users_by_emails(emails, workspace_id, columns)
            enrollments_frame = enrollments_frame.merge(users, how='left', on=USER_EMAIL, validate="many_to_one")

        return enrollments_frame

    def list_trails_enrollments(
            self,
            workspace_id: str,
            limit_filter_date_range: Optional[int] = None,
            filters: Optional[dict] = None
    ):
        keys_filters_dates = (("start_date__gte", "start_date__lte"), ("end_date__gte", "end_date__lte"))
        sql_filter = self._filter.builder_sql_filter(
            table_name="learning_trail_enrollment",
            foreign_tables_names=("user", "learning_trail"),
            filters=filters,
            keys_filter_dates=keys_filters_dates,
            default_keys_filter_dates=keys_filters_dates[0],
            limit_filter_date_range=limit_filter_date_range
        )
        enrollments = self.list("list_workspace_trail_enrollments", **{"workspace_id": workspace_id, "filters": sql_filter})

        user_emails = [enrollment[USER_EMAIL] for enrollment in enrollments]
        enrollments_frame = DataFrame(enrollments)
        if user_emails:
            users = self.account_export_query.list_users_by_emails(user_emails, workspace_id)
            users = filter_columns(users, ["user_director", "user_manager", "user_area_of_activity", USER_EMAIL])
            enrollments_frame = enrollments_frame.merge(users, how='left', on=USER_EMAIL, validate="many_to_one")
        return enrollments_frame

    def list_pulse_activities(
            self,
            workspace_id: str,
            limit_filter_date_range: Optional[int] = None,
            filters: Optional[dict] = None
    ):
        keys_filters_dates = (
            ("time_start__gte", "time_start__lte"),
            ("time_stop__gte", "time_stop__lte"),
        )
        sql_filter = self._filter.builder_sql_filter(
            table_name="learn_content_activity",
            foreign_tables_names=("channel", "user",),
            filters=filters,
            keys_filter_dates=keys_filters_dates,
            default_keys_filter_dates=keys_filters_dates[0],
            limit_filter_date_range=limit_filter_date_range
        )
        enrollments = self.list("list_workspace_pulse_activities", **{"workspace_id": workspace_id, "filters": sql_filter})
        return DataFrame(enrollments)

    def list_contents(self, workspace_id: str, filters: dict) -> DataFrame:
        mission_filters = self._filter.builder_sql_filter(
            table_name="mission_stage_content",
            foreign_tables_names=("mission_stage",),
            filters=filters,
            keys_filter_dates=None,
            default_keys_filter_dates=None,
            limit_filter_date_range=None
        )
        contents = self.list(
            "list_workspace_contents",
            **{"workspace_id": workspace_id, "mission_filters": mission_filters}
        )
        return DataFrame(contents)

    def list_groups_missions_users(
        self,
        workspace_id: str,
        limit_filter_date_range: Optional[int] = None,
        filters: Optional[dict] = None
    ) -> DataFrame:
        keys_filters_dates = (("created_date__gte", "created_date__lte"),)
        sql_filters = self._filter.builder_sql_filter(
            table_name="group_mission",
            filters=filters,
            foreign_tables_names=("user", "mission"),
            keys_filter_dates=keys_filters_dates,
            default_keys_filter_dates=keys_filters_dates[0],
            limit_filter_date_range=limit_filter_date_range
        )
        return DataFrame(self.list("list_groups_missions_users", **{"workspace_id": workspace_id, "filters": sql_filters}))

    def list_groups_channels_users(
        self,
        workspace_id: str,
        limit_filter_date_range: Optional[int] = None,
        filters: Optional[dict] = None
    ) -> DataFrame:
        keys_filters_dates = (("created_date__gte", "created_date__lte"),)

        sql_filters = self._filter.builder_sql_filter(
            table_name="group_channel",
            foreign_tables_names=("user", "channel"),
            filters=filters,
            keys_filter_dates=keys_filters_dates,
            default_keys_filter_dates=keys_filters_dates[0],
            limit_filter_date_range=limit_filter_date_range
        )
        return DataFrame(
            self.list("list_workspace_groups_channels_users", **{"workspace_id": workspace_id, "filters": sql_filters})
        )

    def list_missions_quizzes_answers(
        self,
        workspace_id: str,
        limit_filter_date_range: Optional[int] = None,
        filters: Optional[dict] = None,
        time_zone: Optional[str] = DEFAULT_TIME_ZONE,
    ) -> DataFrame:
        keys_filters_dates = (("created_date__gte", "created_date__lte"),)

        sql_filters = self._filter.builder_sql_filter(
            table_name="answer",
            foreign_tables_names=("mission", "user"),
            filters=filters,
            keys_filter_dates=keys_filters_dates,
            default_keys_filter_dates=keys_filters_dates[0],
            limit_filter_date_range=limit_filter_date_range
        )
        return DataFrame(self.list(
            "list_missions_quizzes_answers",
            **{"workspace_id": workspace_id, "filters": sql_filters, "time_zone": get_timezone(time_zone)}
        ))

    def list_pulses_quizzes_answers(
        self,
        workspace_id: str,
        limit_filter_date_range: Optional[int] = None,
        filters: Optional[dict] = None,
        time_zone: Optional[str] = DEFAULT_TIME_ZONE,
    ) -> DataFrame:
        keys_filters_dates = (("created_date__gte", "created_date__lte"),)

        sql_filters = self._filter.builder_sql_filter(
            table_name="answer",
            foreign_tables_names=("channel", "user",),
            filters=filters,
            keys_filter_dates=keys_filters_dates,
            default_keys_filter_dates=keys_filters_dates[0],
            limit_filter_date_range=limit_filter_date_range
        )
        return DataFrame(
            self.list(
                "list_workspace_pulse_quizzes_answers",
                **{"workspace_id": workspace_id, "filters": sql_filters, "time_zone": get_timezone(time_zone)}
            )
        )

    def list_mission_contents_activities(
        self,
        workspace_id: str,
        limit_filter_date_range: Optional[int] = None,
        filters: Optional[dict] = None
    ) -> DataFrame:
        keys_filters_dates = (("created_date__gte", "created_date__lte"),)

        sql_filters = self._filter.builder_sql_filter(
            table_name="learn_content_activity",
            filters=filters,
            foreign_tables_names=("mission",),
            keys_filter_dates=keys_filters_dates,
            default_keys_filter_dates=keys_filters_dates[0],
            limit_filter_date_range=limit_filter_date_range
        )
        return DataFrame(
            self.list("list_workspace_mission_contents_activities", **{"workspace_id": workspace_id, "filters": sql_filters})
        )

    def list_pulses_channels(
        self,
        workspace_id: str,
        limit_filter_date_range: Optional[int] = None,
        filters: Optional[dict] = None
    ) -> DataFrame:
        keys_filters_dates = (("created_date__gte", "created_date__lte"),)

        sql_filters = self._filter.builder_sql_filter(
            table_name="channel",
            filters=filters,
            keys_filter_dates=keys_filters_dates,
            default_keys_filter_dates=keys_filters_dates[0],
            limit_filter_date_range=limit_filter_date_range
        )
        return DataFrame(
            self.list("list_workspace_pulses_channels", **{"workspace_id": workspace_id, "filters": sql_filters})
        )

    def _get_profile_filter(self, filters) -> dict:
        if USER_PROFILE_WORKSPACE not in filters:
            return {}
        request_filter = filters.pop(USER_PROFILE_WORKSPACE)
        profile_filter = {}
        for field in self.profile_filter_fields:
            profile_filter[f"{USER_PROFILE_WORKSPACE}__{field}"] = request_filter.get(field, [])
        return profile_filter

    def list_users_access_by_date(
        self,
        workspace_id: str,
        limit_filter_date_range: Optional[int] = None,
        filters: Optional[dict] = None
    ) -> DataFrame:
        keys_filters_dates = (("time_start__gte", "time_start__lte"),)
        filters.update(self._get_profile_filter(filters))
        sql_filters = self._filter.builder_sql_filter(
            table_name="learn_content_activity",
            filters=filters,
            foreign_tables_names=("user", "user_profile_workspace"),
            keys_filter_dates=keys_filters_dates,
            default_keys_filter_dates=keys_filters_dates[0],
            limit_filter_date_range=limit_filter_date_range
        )

        activities = DataFrame(
            self.list("list_workspace_user_access_by_date", **{"workspace_id": workspace_id, "filters": sql_filters})
        )
        if activities.empty:
            return activities

        user_emails = set(activities[USER_EMAIL].to_list())
        users_complementary = self.account_export_query.list_users_without_permission_filter(
            user_emails, workspace_id
        )
        if users_complementary.empty:
            return users_complementary

        users_complementary = filter_columns(
            users_complementary, [
                "user_director",
                "user_director_users_enabled",
                "user_manager",
                "user_manager_users_enabled",
                "user_area_of_activity",
                "user_area_of_activity_users_enabled",
                USER_EMAIL,
                "user_enabled"
            ]
        )
        return activities.merge(users_complementary, how="left", on=USER_EMAIL, validate="many_to_one")

    def list_missions_evaluations(
        self,
        workspace_id: str,
        limit_filter_date_range: Optional[int] = None,
        filters: Optional[dict] = None
    ) -> DataFrame:
        keys_filters_dates = (("created_date__gte", "created_date__lte"),)
        if 'user_id__in' in filters:
            filters['mission_enrollment__user_id__in'] = filters.pop('user_id__in')
        if 'mission_id__in' in filters:
            filters['mission_enrollment__mission_id__in'] = filters.pop('mission_id__in')

        sql_filters = self._filter.builder_sql_filter(
            table_name="mission_evaluation",
            foreign_tables_names=("mission_enrollment",),
            filters=filters,
            keys_filter_dates=keys_filters_dates,
            default_keys_filter_dates=keys_filters_dates[0],
            limit_filter_date_range=limit_filter_date_range,
        )
        return DataFrame(
            self.list("list_workspace_mission_evaluations", **{"workspace_id": workspace_id, "filters": sql_filters})
        )

    def list_missions_evaluation_statistics(
        self,
        workspace_id: str,
        limit_filter_date_range: Optional[int] = None,
        filters: Optional[dict] = None
    ) -> DataFrame:
        keys_filters_dates = (("created_date__gte", "created_date__lte"),)
        sql_filters = self._filter.builder_sql_filter(
            table_name="mission_evaluation",
            filters=filters,
            keys_filter_dates=keys_filters_dates,
            default_keys_filter_dates=keys_filters_dates[0],
            limit_filter_date_range=limit_filter_date_range
        )
        return DataFrame(
            self.list(
                "list_workspace_mission_evaluation_statistics",
                **{"workspace_id": workspace_id, "filters": sql_filters, "round_decimal_place": 2}
            )
        )

    def list_users_general_statistics(
        self,
        workspace_id: str,
        limit_filter_date_range: Optional[int] = None,
        filters: Optional[dict] = None
    ) -> DataFrame:
        users = self.account_export_query.list_users(workspace_id, limit_filter_date_range, filters)
        if users.empty:
            return users
        user_ids = users["user_id"].to_list()
        users = filter_columns(users, ["user_director", "user_manager", "user_area_of_activity", USER_EMAIL])
        general_statistics = DataFrame(
            self.list("list_workspace_users_general_statistics", **{"workspace_id": workspace_id, "users_ids": user_ids})
        )
        return general_statistics.merge(users, how='left', on=USER_EMAIL, validate="one_to_one")

    def list_users(
        self,
        workspace_id: str,
        limit_filter_date_range: Optional[int] = None,
        filters: Optional[dict] = None
    ) -> DataFrame:
        users = self.account_export_query.list_users(workspace_id, limit_filter_date_range, filters)
        if users.empty:
            return users
        user_ids = users["user_id"].to_list()
        complementary_data = DataFrame(
            self.list("list_workspace_user_complementary_data", **{"workspace_id": workspace_id, "users_ids": user_ids})
        )
        complementary_data = complementary_data.merge(users, how='left', on="user_id")

        return complementary_data

    def list_trails(
            self,
            workspace_id: str,
            limit_filter_date_range: Optional[int] = None,
            filters: Optional[dict] = None
    ) -> DataFrame:
        keys_filters_dates = (("created_date__gte", "created_date__lte"),)

        sql_filter = self._filter.builder_sql_filter(
            table_name="learning_trail",
            filters=filters,
            keys_filter_dates=keys_filters_dates,
            default_keys_filter_dates=keys_filters_dates[0],
            limit_filter_date_range=limit_filter_date_range
        )
        trails = self.list("list_workspace_trails", **{"workspace_id": workspace_id, "filters": sql_filter})
        return DataFrame(trails)

    def list_user_with_activity(
        self,
        workspace_id: str,
        gte_date: Optional[str],
        lte_date: Optional[str],
    ) -> DataFrame:
        """
        Lists users who have had activities in a given period.
        """
        if not gte_date:
            gte_date = (
                    datetime.datetime.now() - datetime.timedelta(days=Config.KONQUEST_EXPORT_USERS_LIMIT_DAYS)
            ).strftime('%Y-%m-%d')

        if not lte_date:
            lte_date = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime('%Y-%m-%d')

        users_with_activity = self.list(
            "list_workspace_user_with_activity",
            **{"workspace_id": workspace_id, "gte_date": gte_date, "lte_date": lte_date}
        )

        return DataFrame(users_with_activity)
