from typing import Optional, Sequence

from pandas import DataFrame
from reports.constants import COMPLETED, PER<PERSON><PERSON><PERSON>NC<PERSON>, STARTED, STATUS
from reports.filter.filter_sql import FilterSql
from reports.query.account.export import AccountExportQuery
from reports.query.composed import Composed
from reports.utils import filter_columns
from sqlalchemy.engine import Engine

MODULE = "module"
MAX_PERFORMANCE = 1


class KonquestUserConsumptionsQuery(Composed):
    def __init__(self, konquest_database_engine: Engine, account_export_query: AccountExportQuery, template_folder: str):
        super().__init__(konquest_database_engine, template_folder)
        self._filter = FilterSql()
        self.account_export_query = account_export_query
        self.functions_to_join: Sequence = [self.list_pulse_consumes, self.list_mission_enrollments, self.list_trail_enrollments]

    def execute(self, workspace_id: str, limit_filter_date_range: Optional[int] = None, filters: Optional[dict] = None):
        main_df = DataFrame({})
        for query_func in self.functions_to_join:
            main_df = main_df.append(query_func(workspace_id, limit_filter_date_range, filters))
        if main_df.empty:
            return main_df
        user_emails = main_df["user_email"].tolist()
        if user_emails:
            users = self.account_export_query.list_users_by_emails(user_emails, workspace_id)
            users = filter_columns(users, ["user_director", "user_manager", "user_area_of_activity", "user_email"])
            main_df = main_df.merge(users, how='left', on="user_email")
        return main_df

    def list_mission_enrollments(
        self,
        workspace_id: str,
        limit_filter_date_range: Optional[int] = None,
        filters: Optional[dict] = None
    ):
        keys_filters_dates = (("start_date__gte", "start_date__lte"), ("end_date__gte", "end_date__lte"))
        sql_filter = self._filter.builder_sql_filter(
            table_name="mission_enrollment",
            foreign_tables_names=("user", "mission",),
            filters=filters.copy(),
            keys_filter_dates=keys_filters_dates,
            default_keys_filter_dates=keys_filters_dates[0],
            limit_filter_date_range=limit_filter_date_range
        )
        df = DataFrame(self.list(
            "list_workspace_user_consumptions_part_mission_enrollments",
            **{"workspace_id": workspace_id, "filters": sql_filter}
        ))
        df[MODULE] = "MISSION"
        return df

    def list_trail_enrollments(
        self,
        workspace_id: str,
        limit_filter_date_range: Optional[int] = None,
        filters: Optional[dict] = None
    ):
        keys_filters_dates = (("start_date__gte", "start_date__lte"), ("end_date__gte", "end_date__lte"))
        sql_filter = self._filter.builder_sql_filter(
            table_name="learning_trail_enrollment",
            foreign_tables_names=("user", "learning_trail",),
            filters=filters.copy(),
            keys_filter_dates=keys_filters_dates,
            default_keys_filter_dates=keys_filters_dates[0],
            limit_filter_date_range=limit_filter_date_range
        )
        df = DataFrame(self.list(
            "list_workspace_user_consumptions_part_trail_enrollments",
            **{"workspace_id": workspace_id, "filters": sql_filter}
        ))
        df[MODULE] = "LEARNING_TRAIL"
        return df

    def list_pulse_consumes(
            self,
            workspace_id: str,
            limit_filter_date_range: Optional[int] = None,
            filters: Optional[dict] = None
    ):
        keys_filters_dates = (("learn_content_activity__created_date__gte", "learn_content_activity__created_date__lte"),)
        sql_filter = self._filter.builder_sql_filter(
            table_name="pulse",
            foreign_tables_names=("user", "learn_content_activity",),
            filters=filters.copy(),
            keys_filter_dates=keys_filters_dates,
            default_keys_filter_dates=keys_filters_dates[0],
            limit_filter_date_range=limit_filter_date_range
        )
        df = DataFrame(self.list(
            "list_workspace_user_consumptions_part_pulse_consumes",
            **{"workspace_id": workspace_id, "filters": sql_filter}
        ))
        if not df.empty:
            df[STATUS] = df.apply(
                lambda row: STARTED if row[PERFORMANCE] < MAX_PERFORMANCE else COMPLETED, axis=1
            )
        df[MODULE] = "PULSE"
        return df
