from typing import List

import numpy as np
from pandas import DataFrame
from reports.filter.filter_sql import FilterSql
from reports.query import QueryExecutor
from reports.query.account.export import AccountExportQuery
from sqlalchemy.engine import Engine


class KonquestMissionEnrollmentQuizQuery(QueryExecutor):
    def __init__(
        self,
        konquest_database_engine: Engine,
        account_export_query: AccountExportQuery,
        template_folder: str,
        filter_sql: FilterSql
    ):
        super().__init__(konquest_database_engine, template_folder)
        self.account_export_query = account_export_query
        self._filter = filter_sql

    def list_mission_enrollments(self, workspace_id: str, filters: dict) -> DataFrame:
        sql_filters = self._filter.builder_sql_filter(
            table_name="mission_enrollment",
            filters=filters
        )
        enrollments = DataFrame(
            self.list("list_workspace_mission_enrollments_slim", **{"workspace_id": workspace_id, "filters": sql_filters})
        )
        if enrollments.empty:
            return enrollments
        user_ids = enrollments["user_id"].to_list()

        users = self.account_export_query.list_users(workspace_id, None, {"id__in": user_ids})
        if users.empty:
            return users
        return enrollments.merge(users, how='left', on="user_id").replace({np.nan: None})

    def list_question_and_answers(self, mission_ids: List[str]) -> DataFrame:
        mission_ids = list(set([str(mission_id) for mission_id in mission_ids]))
        return DataFrame(self.list("list_questions_and_answers_by_missions", **{"mission_ids": "','".join(mission_ids)}))
