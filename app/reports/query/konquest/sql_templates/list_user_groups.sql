SELECT
    g.id as "id",
    g.name as "name",
    gu.created_date as "created_date",
    COUNT(DISTINCT(gm.mission_id)) as "mission_enrollments_total",
    COUNT(DISTINCT(me.id)) FILTER( where me.end_date is not NULL ) as "mission_enrollments_completed_total",
    COUNT(DISTINCT(me.id)) FILTER( where me.start_date is NULL ) as "mission_enrollments_in_progress_total",
    COALESCE(AVG(me.performance) FILTER( where me.end_date is not NULL ), 0)  as "performance_average",
    COALESCE(AVG(me.progress), 0)  as "progress_average",
    COUNT(DISTINCT(glt.learning_trail_id)) as "trail_enrollments_total",
    COUNT(DISTINCT(lte.id)) FILTER( where lte.end_date is not NULL ) as "trail_enrollments_completed_total",
    COUNT(DISTINCT(lte.id)) FILTER( where lte.end_date is NULL ) as "trail_enrollments_in_progress_total"
FROM
    group_user gu
JOIN
    "group" g on g.id = gu.group_id
LEFT JOIN
    group_mission gm on g.id = gm.group_id
LEFT JOIN
    group_learning_trail glt on g.id = glt.group_id
LEFT JOIN
    mission_enrollment me on gu.user_id = me.user_id
                                 and me.workspace_id = '$workspace_id'
                                 and me.mission_id = gm.mission_id
LEFT JOIN
    learning_trail_enrollment lte on gu.user_id = lte.user_id
                                 and lte.workspace_id = '$workspace_id'
                                 and lte.learning_trail_id = glt.learning_trail_id
WHERE
    gu.user_id = '$user_id'
    AND g.workspace_id = '$workspace_id'
    AND gu.deleted = FALSE
GROUP BY g.id, gu.created_date