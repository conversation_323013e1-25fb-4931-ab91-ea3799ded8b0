WITH myconstants(today_date, lowest_date) as (
   values(TO_TIMESTAMP('$today_datetime', 'YYYY-MM-DD HH:MM'), TO_TIMESTAMP('$today_datetime', 'YYYY-MM-DD HH:MM') - interval '$interval_days days')
)
SELECT
        (
            SELECT COUNT(DISTINCT(me.user_id))
            FROM myconstants,
                mission_enrollment me
            WHERE me.end_date >= lowest_date
             and me.status in ('COMPLETED', 'REPROVED')
             and me.workspace_id = '$workspace_id'
        ) as "count_users_completed_mission",
        (
            SELECT COUNT(DISTINCT(lca.user_id))
            FROM myconstants,
                learn_content_activity lca
            LEFT JOIN
                pulse_channel pc on lca.pulse_id = pc.pulse_id
            LEFT JOIN
                channel c on pc.channel_id = c.id
            LEFT JOIN
                mission_enrollment me on lca.mission_enrollment_id = me.id
            WHERE me.end_date >= lowest_date
                and (me.workspace_id = '$workspace_id' or c.workspace_id='$workspace_id')
        ) as "count_users_active",
        (
            SELECT COUNT(DISTINCT(lca.user_id))
            FROM myconstants,
                learn_content_activity lca
            LEFT JOIN
                pulse_channel pc on lca.pulse_id = pc.pulse_id
            LEFT JOIN
                channel c on pc.channel_id = c.id
            WHERE lca.time_start >= lowest_date and c.workspace_id='$workspace_id'
        ) as "count_users_consumed_pulse",
        (
            SELECT (
                --calculate the growth percentage considering the previous period
                (
                    COUNT(DISTINCT(lca.user_id)) filter(where lca.time_start >= lowest_date) -
                    COUNT(DISTINCT(lca.user_id)) filter(where lca.time_start < lowest_date)
                ) / GREATEST(COUNT(DISTINCT(lca.user_id)) filter(where lca.time_start < lowest_date), 1)::float4
            )
            FROM myconstants,
                learn_content_activity lca
            LEFT JOIN
                pulse_channel pc on lca.pulse_id = pc.pulse_id
            LEFT JOIN
                channel c on pc.channel_id = c.id
            LEFT JOIN
                mission_enrollment me on lca.mission_enrollment_id = me.id
            WHERE me.end_date >= lowest_date - interval '$interval_days days'
                and (me.workspace_id = '$workspace_id' or c.workspace_id='$workspace_id')
        ) as "growth_user_active"
FROM
    workspace
WHERE
    workspace.id = '$workspace_id'
