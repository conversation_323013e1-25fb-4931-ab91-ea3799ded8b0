SELECT
    learning_trail_enrollment.id learning_trail_enrollment_id,
    "user".name user_name,
    "user".email user_email,
    "user".status user_status,
    CASE 
        WHEN urw.user_id IS NOT NULL THEN False 
        ELSE True 
    END as "user_deleted",
    job.name user_job,
    job_function.name user_job_function,
    "user".country user_country,
    learning_trail.name trail_name,
    learning_trail_enrollment.status enrollment_status,
    learning_trail_enrollment.created_date date_enroll,
    min(learn_content_activity.updated_date) date_start,
    learning_trail_enrollment.goal_date,
    CASE
        WHEN learning_trail_enrollment.status = 'COMPLETED' THEN learning_trail_enrollment.updated_date
    END AS date_end,
    ROUND(CAST(learning_trail_enrollment.performance AS numeric), 2) performance,
    ROUND(CAST(learning_trail_enrollment.progress AS numeric), 2) progress,
    learning_trail_enrollment.give_up as "give_up",
    count(mission_enrollment) filter ( where mission_enrollment.status = 'GIVE UP') as enrollments_give_up,
    learning_trail_enrollment.points as points,
    learning_trail.points as learning_trail_points,
    learning_trail.duration_time * interval '1 sec' as duration_trail,
    avg(mission.duration_time) * interval '1 sec' as trail_mission_duration_time_avg,
    sum(mission.duration_time) * interval '1 sec' as mission_duration_time_sum,
    sum(learn_content_activity.time_in) as sum_estimated_consume,
    (SELECT count(*) FROM learning_trail_step WHERE learning_trail_id = learning_trail.id and learning_trail_step.mission_id IS NOT NULL) as missions_count,
        ROUND(AVG(mission_evaluation.nps)::numeric, 2) as mission_nps_avg,
        ROUND(AVG(mission_rating.rating)::numeric, 2) as mission_rating_avg,
    (SELECT leader.name from "user" leader where leader.id = "user".related_user_leader_id) user_leader,
    count(mission_enrollment) filter ( where mission_enrollment.status = 'COMPLETED') as missions_enrollments_completed,
    "user".ein user_ein,
    CASE 
        WHEN (learning_trail_enrollment.status = 'STARTED' or (learning_trail_enrollment.status = 'ENROLLED' and learning_trail_enrollment.progress > 0)) then True else False 
    END AS "trail_enrollment_active",
    learning_trail.deleted as "learning_trail_deleted",
    learning_trail.deleted_date as "learning_trail_deleted_date",
    learning_trail_type.name trail_type
FROM
    learning_trail_enrollment
JOIN "user" ON user_id = "user".id
LEFT JOIN user_profile_workspace
ON user_profile_workspace.user_id = learning_trail_enrollment.user_id
AND user_profile_workspace.workspace_id = '$workspace_id'
LEFT JOIN job_function on user_profile_workspace.job_function_id = job_function.id
LEFT JOIN job on user_profile_workspace.job_position_id = job.id
LEFT JOIN learning_trail on learning_trail_enrollment.learning_trail_id = learning_trail.id
LEFT JOIN learning_trail_step on learning_trail.id = learning_trail_step.learning_trail_id
LEFT JOIN mission_enrollment on learning_trail_step.mission_id = mission_enrollment.mission_id and mission_enrollment.user_id=learning_trail_enrollment.user_id
LEFT JOIN mission_evaluation on mission_enrollment.id = mission_evaluation.enrollment_id
LEFT JOIN mission_rating on mission_rating.mission_id = mission_enrollment.mission_id and mission_rating.user_id = mission_enrollment.user_id
LEFT JOIN learning_trail_type on learning_trail.learning_trail_type_id = learning_trail_type.id
LEFT JOIN mission on mission_enrollment.mission_id = mission.id
LEFT JOIN learn_content_activity
ON (mission_enrollment.id = learn_content_activity.mission_enrollment_id
OR learning_trail_step.pulse_id = learn_content_activity.pulse_id)
AND learning_trail_enrollment.user_id = learn_content_activity.user_id
LEFT JOIN learning_trail_workspace on learning_trail.id = learning_trail_workspace.learning_trail_id
LEFT JOIN
    user_role_workspace urw ON urw.user_id = "user".id 
        AND urw.workspace_id = '$workspace_id'
WHERE learning_trail_workspace.workspace_id = '$workspace_id'
$filters
GROUP BY
    "user".id,
    "user".name,
    "user".email,
    "user".status,
    job_function.name,
    "user".country,
    job.name,
    learning_trail_enrollment.id,
    learning_trail.name,
    learning_trail_enrollment.status,
    learning_trail_enrollment.created_date,
    learning_trail.id,
    learning_trail_type.name, 
    urw.user_id
