SELECT
	"group"."name" as "group_name",
	channel."name" as "channel_name",
	channel_type."name" as "channel_type",
	"user"."name" as "user_name",
	"user".email as "user_email"
FROM
    group_channel
JOIN
    group_user on group_channel.group_id=group_user.group_id
JOIN
    "user" on "user".id=group_user.user_id
JOIN
    "group" on group_channel.group_id="group".id
JOIN
    channel on channel.id=group_channel.channel_id
LEFT JOIN
	channel_type on channel.channel_type_id=channel_type.id
WHERE
    channel.workspace_id='$workspace_id' and group_channel.deleted = false  and channel.deleted = false
$filters