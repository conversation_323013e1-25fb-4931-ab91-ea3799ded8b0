SELECT
    mission.id as "mission_id",
    mission.name as "mission_name",
    mission.mission_model as "mission_model",
    "user".email as "user_creator",
    mission.created_date as "mission_created_date",
    mission_category.name as "mission_category",
    mission_provider.name as "mission_provider",
    count(mission_evaluation) as "evaluations_total",
    ROUND(COALESCE(avg(mission_evaluation.question_1_rating), 0)::numeric, $round_decimal_place) as "question_1_rating_avg",
    ROUND(COALESCE(avg(mission_evaluation.question_2_rating), 0)::numeric, $round_decimal_place) as "question_2_rating_avg",
    ROUND(COALESCE(avg(mission_evaluation.question_3_rating), 0)::numeric, $round_decimal_place) as "question_3_rating_avg",
    ROUND(COALESCE(avg(mission_evaluation.question_4_rating), 0)::numeric, $round_decimal_place) as "question_4_rating_avg",
    ROUND(COALESCE(avg(mission_evaluation.question_5_rating), 0)::numeric, $round_decimal_place) as "question_5_rating_avg",
    ROUND(COALESCE(avg(mission_evaluation.question_6_rating), 0)::numeric, $round_decimal_place) as "question_6_rating_avg",
    ROUND(COALESCE(avg(mission_evaluation.questions_rating_avg), 0)::numeric, $round_decimal_place) as "questions_rating_avg",
    COALESCE(SUM(CASE WHEN nps >= 9 THEN 1 ELSE 0 END), 0) as "promoters_total",
    COALESCE(SUM(CASE WHEN nps <= 8 and nps >= 7 THEN 1 ELSE 0 END), 0) as "passives_total",
    COALESCE(SUM(CASE WHEN nps <= 6 THEN 1 ELSE 0 END), 0) as "detractors_total",
    ROUND(COALESCE(avg(nps), 0)::numeric, 0) as "nps",
    MAX(mission_evaluation.created_date) AS "last_evaluation_date"
FROM
    mission
JOIN
    mission_workspace on mission.id = mission_workspace.mission_id
JOIN
    "user" on mission.user_creator_id = "user".id
JOIN
    mission_category on mission.mission_category_id = mission_category.id
LEFT JOIN
    external_mission on external_mission.mission_id = mission.id
LEFT JOIN
    mission_provider on external_mission.provider_id = mission_provider.id
LEFT JOIN 
    mission_enrollment on mission.id = mission_enrollment.mission_id
JOIN
    mission_evaluation on mission_enrollment.id = mission_evaluation.enrollment_id AND mission_enrollment.mission_id = mission.id
WHERE
    mission_workspace.workspace_id = '$workspace_id' and mission_workspace.relationship_type = 'OWNER' and mission.deleted = false
$filters
group by mission.id, mission.name, mission.mission_model, "user".email, mission.created_date, mission_category.name, mission_provider.name
