SELECT
    pulse.id as "object_id",
    "user".id as "user_id",
	"user"."name" as "user_name",
	"user".email as "user_email",
	COALESCE(ul.email::varchar(255), '----') as "user_leader",
	pulse."name" as "name",
    'STARTED' as "status",
    LEAST((
        CASE
            WHEN
                COALESCE(pulse.duration_time, 0) > 0
                THEN ROUND(((EXTRACT(epoch FROM SUM(learn_content_activity.time_in)))/pulse.duration_time)::numeric, 2)
            ELSE '0'
        END
    ), 1.0) as "performance",
	pulse.points as "points",
	COALESCE(pulse.duration_time, 0) * interval '1 sec' as "duration",
    SUM(learn_content_activity.time_in) as "consume_duration",
    (SELECT time_start FROM learn_content_activity WHERE learn_content_activity.pulse_id = pulse.id and learn_content_activity.user_id = "user".id ORDER BY learn_content_activity.created_date LIMIT 1) as "date_start",
    (SELECT time_stop FROM learn_content_activity WHERE learn_content_activity.pulse_id = pulse.id and learn_content_activity.user_id = "user".id ORDER BY learn_content_activity.created_date DESC LIMIT 1) as "date_end"
FROM
    pulse
JOIN
    pulse_channel on pulse_channel.pulse_id =pulse.id
JOIN
    channel on pulse_channel.channel_id = channel.id
JOIN
    learn_content_activity on learn_content_activity.pulse_id = pulse.id
JOIN
    "user" on learn_content_activity.user_id = "user".id
LEFT JOIN
    "user" AS ul on ul.id = "user".related_user_leader_id
WHERE
    channel.workspace_id='$workspace_id' and pulse.deleted = false
$filters
GROUP BY pulse.id, pulse.name, pulse.points, pulse.duration_time, "user".email, "user".name, "user".id, ul.email
