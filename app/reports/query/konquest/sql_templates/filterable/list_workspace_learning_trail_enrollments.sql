SELECT 
	coalesce(learning_trail.name, '-----') AS "learning_trail",
	missions_on_trail AS "count_missions_on_learning_trail",
	trail_enrollments AS "user_enrollments_on_learning_trail",
	mission_enrollments AS "count_mission_enrollments_on_learning_trail",
	enrolled AS "trail_enrollments_not_started",
	started AS "enrollments_started",
	completed AS "enrollments_completed",
	inactivated AS "enrollments_inactivated",
	reproved AS "enrollments_reproved",
	estimated_consume,
	mission_duration,
	round((CASE 
            WHEN mission_enrollments > 0
            THEN (completed::numeric / mission_enrollments::numeric)
            ELSE 0 END)::NUMERIC,2) AS "mission_completion_rate",
    round(mission_nps,2) as "avg_nps",
    round(mission_rating) as "avg_rating"
FROM
	(SELECT
		learning_trail_id,
		(SELECT count(*) FROM learning_trail_step lts
		 WHERE lts.learning_trail_id = enrollments.learning_trail_id
		 ) AS "missions_on_trail",
		(SELECT count(*) FROM learning_trail_enrollment lte
		 WHERE lte.learning_trail_id = enrollments.learning_trail_id AND lte.deleted = FALSE
		) AS "trail_enrollments",
		count(*) AS "mission_enrollments",
		sum(CASE WHEN enrollment_status = 'ENROLLED' THEN 1 ELSE 0 END) AS "enrolled",
		sum(CASE WHEN enrollment_status = 'STARTED' THEN 1 ELSE 0 END) AS "started",
		sum(CASE WHEN enrollment_status = 'COMPLETED' THEN 1 ELSE 0 END) AS "completed",
		sum(CASE WHEN enrollment_status = 'INACTIVATED' THEN 1 ELSE 0 END) AS "inactivated",
		sum(CASE WHEN enrollment_status = 'REPROVED' THEN 1 ELSE 0 END) AS "reproved",
		sum(estimated_consume) AS "estimated_consume",
		sum(mission_duration) AS "mission_duration",
		avg(mission_nps) AS "mission_nps",
		avg(mission_rating) AS "mission_rating"
	FROM
		(SELECT
			mission_enrollment.id,
			learning_trail.id AS "learning_trail_id",
			mission_enrollment.status AS "enrollment_status",
			mission_enrollment.performance AS "performance",
			mission_enrollment.progress AS "progress",
			CASE WHEN mission_enrollment.give_up = TRUE THEN 'GIVE UP' ELSE 'NOT GIVE UP' END AS "give_up",
			ROUND(COALESCE((mission.duration_time::NUMERIC * mission_enrollment.performance), 0)::NUMERIC, 0) * INTERVAL '1 sec' AS "estimated_consume",
			COALESCE(mission.duration_time, 0) * INTERVAL '1 sec' AS "mission_duration",
			mission_evaluation.nps as "mission_nps",
		    mission_rating.rating as "mission_rating"
		FROM
			mission_enrollment
		LEFT JOIN 
			learning_trail_step ON learning_trail_step.mission_id = mission_enrollment.mission_id
		LEFT JOIN 
			learning_trail ON learning_trail.id = learning_trail_step.learning_trail_id
		JOIN
			mission ON mission.id = mission_enrollment.mission_id
		JOIN
		    "user" ON "user".id = mission_enrollment.user_id
		LEFT JOIN
		    user_profile_workspace ON user_profile_workspace.user_id = "user".id
			AND user_profile_workspace.workspace_id = '$workspace_id'
		LEFT JOIN
		    mission_evaluation ON mission_enrollment.id = mission_evaluation.enrollment_id
		LEFT JOIN
		    mission_rating ON mission.id = mission_rating.mission_id
			AND "user".id = mission_rating.user_id
		WHERE
			mission_enrollment.workspace_id = '$workspace_id'
			AND mission_enrollment.deleted = FALSE
			$filters
		) enrollments
		WHERE exists(SELECT 1 FROM learning_trail_enrollment lte 
					  WHERE lte.learning_trail_id=enrollments.learning_trail_id
					    AND lte.workspace_id='$workspace_id'
					    AND lte.deleted=FALSE LIMIT 1)		
		GROUP BY enrollments.learning_trail_id
	) q
LEFT JOIN learning_trail ON learning_trail.id=q.learning_trail_id
ORDER BY learning_trail.name
