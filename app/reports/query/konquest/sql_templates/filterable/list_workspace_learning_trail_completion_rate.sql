SELECT 
	distinct(learning_trail_enrollment.id),
	learning_trail.id, 
	learning_trail.name as "learning_trail_name",
	"user".email as "user_email",
	1 as "enrollment_count",
	learning_trail_enrollment.status,
	learning_trail_enrollment.points,
	learning_trail_enrollment.performance
FROM learning_trail
LEFT JOIN 
	learning_trail_enrollment ON learning_trail.id = learning_trail_enrollment.learning_trail_id 
JOIN 
	learning_trail_step ON learning_trail.id = learning_trail_step.learning_trail_id 
JOIN
    "user" on "user".id=learning_trail_enrollment.user_id
LEFT JOIN
    user_profile_workspace on user_profile_workspace.user_id = "user".id and user_profile_workspace.workspace_id = '$workspace_id'
WHERE learning_trail_enrollment.workspace_id = '$workspace_id'
  AND learning_trail_enrollment.deleted = False
  AND learning_trail_enrollment.status in ('ENROLLED', 'STARTED', 'COMPLETED', 'REPROVED', 'INACTIVATED')
$filters
ORDER BY learning_trail.name