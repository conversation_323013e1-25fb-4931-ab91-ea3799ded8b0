SELECT
    mission.id as "object_id",
    "user".id as "user_id",
	"user"."name" as "user_name",
	"user".email as "user_email",
	COALESCE(ul.email::varchar(255), '----') as "user_leader",
	mission."name" as "name",
    mission_enrollment.status as "status",
    mission_enrollment.performance as "performance",
	mission_enrollment.points as "points",
	COALESCE(mission.duration_time, 0) * interval '1 sec' as "duration",
	ROUND(COALESCE((mission.duration_time::numeric * performance), 0)::numeric, 0) * interval '1 sec' as "consume_duration",
    mission_enrollment.start_date as "date_start",
	mission_enrollment.end_date as "date_end",
	(CASE WHEN mission.minimum_performance > 0 THEN mission.minimum_performance::varchar(255) ELSE '-' END) as "minimum_performance"
FROM
    mission_enrollment
JOIN
    mission on mission.id=mission_enrollment.mission_id
JOIN
    "user" on "user".id=mission_enrollment.user_id
LEFT JOIN
    "user" AS ul  on ul.id = "user".related_user_leader_id
WHERE
    mission_enrollment.workspace_id='$workspace_id' and mission_enrollment.deleted = false
$filters