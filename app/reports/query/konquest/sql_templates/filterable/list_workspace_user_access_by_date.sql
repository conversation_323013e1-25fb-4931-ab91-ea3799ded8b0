SELECT
    "user".name as "user_name",
    "user".email as "user_email",
    TRIM(TO_CHAR(time_start, 'YYYY')) as "date_activity_year",
    TRIM(TO_CHAR(time_start, 'Month')) as "date_activity_month",
    learn_content_activity.time_start as "date_activity",
    "user".last_access_date as "last_access",
    (
        CASE WHEN pulse.name IS NOT NULL
            THEN 'PULSE'
            WHEN mission_stage_content.name IS NOT NULL
            THEN 'MISSION'
            ELSE 'UNKNOWN'
        END
    ) AS "content_type",
    COALESCE(pulse.name, mission_stage_content.name) AS "content_name"
FROM
    learn_content_activity
JOIN
    "user" on learn_content_activity.user_id = "user".id
LEFT JOIN
    "user_profile_workspace" on "user_profile_workspace".user_id = "user".id and "user_profile_workspace".workspace_id = '$workspace_id'
LEFT JOIN
    pulse on pulse.id = learn_content_activity.pulse_id
LEFT JOIN
    pulse_channel on learn_content_activity.pulse_id = pulse_channel.pulse_id
LEFT JOIN
    channel on pulse_channel.channel_id = channel.id
LEFT JOIN
    mission_stage_content on learn_content_activity.mission_stage_content_id = mission_stage_content.id
LEFT JOIN
    mission_stage on mission_stage_content.stage_id = mission_stage.id
LEFT JOIN
    mission_enrollment on learn_content_activity.mission_enrollment_id = mission_enrollment.id
WHERE
    (mission_enrollment.workspace_id = '$workspace_id' or channel.workspace_id = '$workspace_id') and learn_content_activity.deleted = false
$filters
GROUP BY
    time_start,
    learn_content_activity.user_id,
    "user".email,
    "user".name,
    "user".last_access_date,
    pulse.name,
    mission_stage_content.name
