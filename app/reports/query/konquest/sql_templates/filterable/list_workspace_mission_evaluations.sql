SELECT
	"user".email as "user_email",
	mission.name as "mission_name",
	question_1_rating as "question_1_rating",
	question_2_rating as "question_2_rating",
	question_3_rating as "question_3_rating",
	question_4_rating as "question_4_rating",
	question_5_rating as "question_5_rating",
	question_6_rating as "question_6_rating",
	questions_rating_avg  as "questions_rating_avg",
	nps as "nps",
	mission_evaluation.comment as "comment",
	sentiment_analysis as "sentiment_analysis",
	mission_evaluation.created_date as "evaluation_date"
FROM
	mission_evaluation
LEFT JOIN 
    mission_enrollment on mission_evaluation.enrollment_id = mission_enrollment.id
LEFT JOIN
	"user" on "user".id = mission_enrollment.user_id
LEFT JOIN
	mission on mission.id = mission_enrollment.mission_id
LEFT JOIN
	mission_workspace on mission_workspace.mission_id = mission_enrollment.mission_id and relationship_type = 'OWNER'
WHERE
    mission_workspace.workspace_id='$workspace_id' and mission_evaluation.deleted = false
$filters