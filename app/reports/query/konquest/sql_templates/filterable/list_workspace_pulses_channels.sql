SELECT
    DISTINCT(pulse.id) as "pulse_id",
	pulse.name AS "pulse_name",
	pulse_type."name" as "pulse_type",
	COALESCE(pulse.duration_time, 0) * interval '1 sec' as "duration",
	channel.name as "channel_name",
	channel_type."name" as "channel_type",
	"user"."name" as "user_creator_name",
	pulse.created_date as created_date
FROM
	channel
LEFT JOIN
	pulse_channel on pulse_channel.channel_id=channel.id
LEFT JOIN
	pulse on pulse_channel.pulse_id=pulse.id
LEFT JOIN
	channel_type on channel.channel_type_id=channel_type.id
JOIN
	pulse_type on pulse_type.id=pulse.pulse_type_id
JOIN
	"user" on "user".id=channel.user_creator_id
WHERE
	workspace_id='$workspace_id' and 
	"pulse"."deleted" = false
$filters