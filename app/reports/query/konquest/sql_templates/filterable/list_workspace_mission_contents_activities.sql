SELECT
    learn_content_activity.mission_enrollment_id as "enrollment_id",
	learn_content_activity.user_id as "user_id",
	learn_content_activity.mission_stage_content_id as "content_id",
	mission_enrollment.start_date as "enrollment_start_date",
	mission_enrollment.end_date as "enrollment_end_date",
	mission_enrollment.performance as "enrollment_performance",
	mission_stage_content.content_type as "content_type",
	"user".name as "user_name",
	"user".email as "user_email",
	mission.name as "mission_name",
	mission_stage_content.learn_content_uuid as "learn_content_uuid",
	learn_content_activity.time_start as "time_start",
	learn_content_activity.time_stop as "time_stop",
	extract(epoch from learn_content_activity.time_in) as "consume_time(seconds)"
FROM
	learn_content_activity
JOIN
	"user" on "user".id = learn_content_activity.user_id
JOIN
	mission_enrollment on mission_enrollment.id = learn_content_activity.mission_enrollment_id
JOIN
	mission_stage_content on mission_stage_content.id = learn_content_activity.mission_stage_content_id and mission_stage_content.content_type in ('CONTENT', 'SCORM', 'HTML')
JOIN
	mission_stage on mission_stage.id = mission_stage_content.stage_id
JOIN
	mission_workspace on mission_workspace.mission_id = mission_stage.mission_id
JOIN
	mission on mission.id = mission_stage.mission_id
WHERE
	mission_workspace.workspace_id = '$workspace_id'
	AND mission_enrollment.end_date >= learn_content_activity.time_stop
	AND learn_content_activity.deleted = False
$filters
ORDER BY
    learn_content_activity.mission_enrollment_id,
    learn_content_activity.created_date

