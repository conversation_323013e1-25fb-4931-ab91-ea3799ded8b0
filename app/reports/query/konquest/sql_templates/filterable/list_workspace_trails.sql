SELECT 
    learning_trail.id AS trail_id,
    learning_trail.name,
    learning_trail.description,
    "user".name AS user_creator_name,
    learning_trail.is_active,
    learning_trail.created_date,
    INTERVAL '1 second' * learning_trail.duration_time as duration_trail,
    COALESCE(learning_trail_enrollment.enrollments, 0) AS enrollments,
    COALESCE(learning_trail_enrollment.enrollments_not_started, 0) AS enrollments_not_started,
    COALESCE(learning_trail_enrollment.enrollments_started, 0) AS enrollments_started,
    COALESCE(learning_trail_enrollment.enrollments_completed, 0) AS enrollments_completed,
    COALESCE(learning_trail_enrollment.enrollments_reproved, 0) AS enrollments_reproved,
    COALESCE(learning_trail_enrollment.enrollments_inactivated, 0) AS enrollments_inactivated,
    COALESCE(learning_trail_enrollment.enrollments_giveup, 0) AS enrollments_give_up,
    COALESCE(mission_evaluation.missions_nps_avg, 0) AS missions_nps_avg,
    COALESCE(mission_evaluation.avg_rating_mission, 0) AS avg_rating_mission,
    COALESCE(learning_trail_step.pulses_count, 0) AS pulses_count,
    COALESCE(learning_trail_step.missions_count, 0) AS missions_count,
    COALESCE(mission_enrollment.missions_enrollments_completed, 0) AS missions_enrollments_completed,
    COALESCE(mission_enrollment.mission_enrollments_in_progress, 0) AS mission_enrollments_in_progress
FROM 
    learning_trail
INNER JOIN 
    "user"
    ON learning_trail.user_creator_id = "user".id
LEFT JOIN 
    (
        SELECT 
            learning_trail_id,
            COUNT(1) AS enrollments,
            COUNT(CASE WHEN status = 'ENROLLED' THEN 1 END) AS enrollments_not_started,
            COUNT(CASE WHEN status = 'STARTED' THEN 1 END) AS enrollments_started,
            COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) AS enrollments_completed,
            COUNT(CASE WHEN status = 'REPROVED' THEN 1 END) AS enrollments_reproved,
            COUNT(CASE WHEN status = 'INACTIVATED' THEN 1 END) AS enrollments_inactivated,
            COUNT(CASE WHEN status = 'GIVE_UP' THEN 1 END) AS enrollments_giveup
        FROM 
            learning_trail_enrollment
        GROUP BY 
            learning_trail_id
    ) learning_trail_enrollment 
    ON learning_trail.id = learning_trail_enrollment.learning_trail_id
LEFT JOIN 
    (
        SELECT 
            learning_trail_id,
            COUNT(CASE WHEN pulse_id IS NOT NULL THEN 1 END) AS pulses_count,
            COUNT(CASE WHEN mission_id IS NOT NULL THEN 1 END) AS missions_count
        FROM 
            learning_trail_step
        GROUP BY 
            learning_trail_id
    ) learning_trail_step 
    ON learning_trail.id = learning_trail_step.learning_trail_id
LEFT JOIN 
    (
        SELECT
            learning_trail_step.learning_trail_id,
            COUNT(CASE WHEN mission_enrollment.status = 'COMPLETED' THEN 1 END) AS missions_enrollments_completed,
            COUNT(CASE WHEN mission_enrollment.status IN ('STARTED', 'ENROLLED') THEN 1 END) AS mission_enrollments_in_progress
        FROM 
            learning_trail_step
        INNER JOIN 
            mission_enrollment 
            ON learning_trail_step.mission_id = mission_enrollment.mission_id AND mission_enrollment.workspace_id = '$workspace_id'
        GROUP BY 
            learning_trail_step.learning_trail_id
    ) mission_enrollment 
    ON learning_trail.id = mission_enrollment.learning_trail_id
LEFT JOIN 
    (
        SELECT 
			learning_trail_step.learning_trail_id,
			ROUND(AVG(mission_evaluation.questions_rating_avg)::numeric, 2) AS avg_rating_mission,
		    ROUND(AVG(mission_evaluation.nps)::numeric, 2) AS missions_nps_avg
		FROM 
			mission_evaluation mission_evaluation 
		INNER JOIN 
			mission_enrollment 
				ON mission_evaluation.enrollment_id = mission_enrollment.id AND mission_enrollment.workspace_id = '$workspace_id'
		INNER JOIN 
			learning_trail_step 
				ON learning_trail_step.mission_id = mission_enrollment.mission_id
		GROUP BY
			learning_trail_step.learning_trail_id
    ) mission_evaluation 
    ON learning_trail.id = mission_evaluation.learning_trail_id
WHERE 
    EXISTS (
        SELECT 1
        FROM learning_trail_workspace
        WHERE learning_trail_workspace.learning_trail_id = learning_trail.id AND learning_trail_workspace.workspace_id = '$workspace_id'
    )
    $filters
ORDER BY 
    learning_trail.name;
