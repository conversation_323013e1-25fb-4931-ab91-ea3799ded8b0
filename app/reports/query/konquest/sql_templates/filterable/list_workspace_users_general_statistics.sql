SELECT
    "user".id as "user_id",
	"user"."name" as "user_name",
	"user".email as "user_email",
	COALESCE(ul.email::varchar(255), '----') as "user_leader",
    (
        SELECT
            COUNT(me.id)
        FROM
            mission_enrollment me
        WHERE
            me.user_id = "user".id
            AND me.workspace_id = '$workspace_id'
    ) as "count_mission_enrollments",
    (
        SELECT
            COUNT(me.id)
        FROM
            mission_enrollment me
        WHERE
            me.user_id = "user".id
            AND me.workspace_id = '$workspace_id'
            AND me.status in ('COMPLETED', 'REPROVED')
    ) as "count_completed_missions",
    (
        SELECT
            COUNT(me.id)
        FROM
            mission_enrollment me
        WHERE
            me.user_id = "user".id
            AND me.workspace_id = '$workspace_id'
            AND me.status not in ('COMPLETED', 'REPROVED')
    ) as "count_missions_in_progress",
    (
        SELECT
            COALESCE(AVG(me.performance)::numeric(10, 2), 0)::float
        FROM
            mission_enrollment as me
        WHERE me.user_id = "user".id
          and me.workspace_id = '$workspace_id'
          and me.status in ('COMPLETED', 'REPROVED')
    ) as "performance_average",
    (
        SELECT
	        COUNT(DISTINCT(pulse.id))  as "total_consume_days"
        FROM
            learn_content_activity
        JOIN
            pulse on pulse.id=learn_content_activity.pulse_id
        JOIN
            pulse_channel on pulse_channel.pulse_id=pulse.id
        JOIN
            channel on channel.id=pulse_channel.channel_id
        WHERE
            learn_content_activity.user_id="user".id
        AND
            channel.workspace_id = '$workspace_id'
    ) as "count_pulses_consumed",
    (
        SELECT
	        SUM(learn_content_activity.time_in)
        FROM
            learn_content_activity
        JOIN
            mission_enrollment m on learn_content_activity.mission_enrollment_id = m.id
        WHERE
            learn_content_activity.user_id="user".id
        AND
            m.workspace_id = '$workspace_id'
    ) as "sum_mission_consume_time",
    (
        SELECT
	        SUM(learn_content_activity.time_in)
        FROM
            learn_content_activity
        JOIN
            pulse on pulse.id=learn_content_activity.pulse_id
        JOIN
            pulse_channel on pulse_channel.pulse_id=pulse.id
        JOIN
            channel on channel.id=pulse_channel.channel_id
        WHERE
            learn_content_activity.user_id="user".id
        AND
            channel.workspace_id = '$workspace_id'
    ) as "sum_pulse_consume_time",
    (
        SELECT
	        SUM(learn_content_activity.time_in)
        FROM
            learn_content_activity
        LEFT JOIN
            pulse on pulse.id=learn_content_activity.pulse_id
        LEFT JOIN
            pulse_channel on pulse_channel.pulse_id=pulse.id
        LEFT JOIN
            channel on channel.id=pulse_channel.channel_id
        LEFT JOIN
            mission_enrollment m on learn_content_activity.mission_enrollment_id = m.id
        WHERE
            learn_content_activity.user_id="user".id
        AND
            (channel.workspace_id = '$workspace_id' or m.workspace_id = '$workspace_id')
    ) as "sum_content_consume_time"
FROM
    "user"
LEFT JOIN
    "user" AS ul  on ul.id = "user".related_user_leader_id
WHERE
    "user".id in ($users_ids)