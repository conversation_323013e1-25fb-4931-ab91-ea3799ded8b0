SELECT
	channel.name as "channel_name",
	pulse."name" as "pulse_name",
	"user"."name" as "user_name",
	"user"."email" as "user_email",
	job.name as "user_job",
	COALESCE("user".country::varchar(40), '----') as "user_country",
	"user".ein as "user_ein",
	COALESCE(ul.email::varchar(255), '----') as "user_leader",
	"action" as "action",
	time_start as "start_at",
	time_stop as "stop_at",
	COALESCE(pulse.duration_time, 0) * interval '1 sec' as "content_duration",
    time_in as "consume_duration",
    pulse_rating.rating as "rating",
    now() as "report_date"
FROM
	learn_content_activity
JOIN
	pulse on pulse.id=pulse_id
LEFT JOIN
	pulse_channel on pulse_channel.pulse_id=pulse.id
LEFT JOIN
	channel on pulse_channel.channel_id=channel.id
JOIN
	"user" on "user".id=user_id
LEFT JOIN
    user_profile_workspace on user_profile_workspace.user_id = "user".id AND user_profile_workspace.workspace_id = '$workspace_id'
LEFT JOIN
    job on user_profile_workspace.job_position_id = job.id
LEFT JOIN
    "user" AS ul  on ul.id = "user".related_user_leader_id
LEFT JOIN
	pulse_rating on pulse_rating.pulse_id=pulse.id and pulse_rating.user_id="user".id
WHERE
	channel.workspace_id='$workspace_id' and learn_content_activity.deleted = false
$filters
