SELECT
    DISTINCT(mission.id) as "mission_id",
    mission.name as "mission_name",
    mission.is_active as "mission_is_active",
    "user"."name" as "user_creator_name",
    REPLACE(TO_CHAR(mission.created_date, 'Month'), ' ', '') as "mission_month_created",
    mission.updated_date as "mission_updated_date",
    mission.created_date as "mission_created_date",
    mission.development_status as "development_status",
    mission_type."name" as "mission_type",
    mission_category."name" as "mission_category",
    (
        SELECT
            STRING_AGG(DISTINCT(learning_trail.name), '; ')
        FROM
            learning_trail
        LEFT JOIN
            learning_trail_step on learning_trail_step.learning_trail_id = learning_trail.id
        WHERE
            learning_trail_step.mission_id = mission.id
    ) as "trails_linked",
    COALESCE(mission.duration_time, 0) * interval '1 sec' as "duration",
    mission.mission_model as "mission_model",
    mission_provider.name as "mission_provider",
    external_mission.course_url as "course_url",
    UPPER(mission.language) as "mission_language",
    (CASE WHEN mission.minimum_performance > 0 THEN mission.minimum_performance::varchar(255) ELSE '-' END) as "minimum_performance",
    mission.allow_self_enrollment_renewal as "allow_self_enrollment_renewal",
    mission.allow_self_reproved_enrollment_renewal as "allow_self_reproved_enrollment_renewal",
    mission.required_evaluation as "required_evaluation",
    mission.deleted as "mission_deleted",
    mission.deleted_date as "mission_deleted_date",
    COUNT(mission_enrollment.id) as "enrollments",
    SUM(case when mission_enrollment.status = 'COMPLETED' then 1 else 0 end) as "mission.enrollments_done",
    SUM(case when mission_enrollment.status = 'ENROLLED' then 1 else 0 end) as "mission.enrollments_enrolled",
    SUM(case when mission_enrollment.status = 'STARTED' then 1 else 0 end) as "mission.enrollments_started",
    SUM(case when mission_enrollment.status = 'REPROVED' then 1 else 0 end) as "mission.enrollments_reproved",
    SUM(case when mission_enrollment.status = 'EXPIRED' then 1 else 0 end) as "mission.enrollments_expired",
    SUM(case when mission_enrollment.status = 'GIVE_UP' then 1 else 0 end) as "mission.enrollments_give_up",
    SUM(case when mission_enrollment.status = 'INACTIVATED' then 1 else 0 end) as "mission.enrollments_inactivated",
    SUM(case when mission_enrollment.status = 'REQUEST_EXTENSION' then 1 else 0 end) as "mission.enrollments_request_extension",
    SUM(case when mission_enrollment.status = 'REFUSED' then 1 else 0 end) as "mission.enrollments_refused",
    SUM(case when mission_enrollment.status = 'WAITING_APPROVAL' then 1 else 0 end) as "mission.enrollments_waiting_approval",
    SUM(case when mission_enrollment.status = 'PENDING_VALIDATION' then 1 else 0 end) as "mission.enrollments_pending_validation",
    SUM(case when mission_enrollment.status = 'WAITING_VACANCIES' then 1 else 0 end) as "mission.enrollments_waiting_vacancies"
FROM
    mission
JOIN
	mission_workspace on mission_workspace.mission_id=mission.id
JOIN
	"user" on mission.user_creator_id="user".id
JOIN
	mission_type on mission.mission_type_id=mission_type.id
LEFT JOIN
    group_mission on mission.id = group_mission.mission_id
LEFT JOIN
    mission_enrollment on mission.id = mission_enrollment.mission_id and mission_enrollment.deleted is False
LEFT JOIN
    mission_category on mission.mission_category_id = mission_category.id
LEFT JOIN
    external_mission on external_mission.mission_id = mission.id
LEFT JOIN
    mission_provider on external_mission.provider_id = mission_provider.id
WHERE
    mission_workspace.workspace_id = '$workspace_id'
AND
    mission_workspace.relationship_type='OWNER'
$filters
GROUP BY
    mission.id,
    mission.name,
    "user"."name",
    mission.updated_date,
    mission.created_date,
    mission.development_status,
    mission_type."name",
    mission.duration_time,
    mission_category."name",
    mission_provider.name,
    external_mission.course_url