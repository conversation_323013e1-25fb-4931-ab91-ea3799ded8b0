SELECT
    learn_content_uuid as "id"
FROM
    mission_stage_content
JOIN
    mission_stage mission_stage on mission_stage_content.stage_id = mission_stage.id
JOIN
    mission_workspace on mission_stage.mission_id = mission_workspace.mission_id
WHERE
    mission_workspace.workspace_id = '$workspace_id' and mission_workspace.deleted = false 
$mission_filters
UNION
SELECT
    learn_content_uuid as "id"
FROM
    pulse
JOIN
    pulse_channel pulse_channel on pulse.id = pulse_channel.pulse_id
JOIN
    channel on pulse_channel.channel_id = channel.id
WHERE
    workspace_id = '$workspace_id' and pulse.deleted = false 