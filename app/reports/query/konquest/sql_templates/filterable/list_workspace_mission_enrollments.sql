WITH learning_trail_data AS (
    SELECT
        lts.mission_id,
        array_to_string(
            array_agg(DISTINCT COALESCE(lt.name, '')),
            '; ', ''
        ) AS learning_trail_names
    FROM learning_trail lt
    INNER JOIN learning_trail_step lts ON lts.learning_trail_id = lt.id
    WHERE EXISTS (
        SELECT 1
        FROM learning_trail_enrollment lte
        WHERE lte.learning_trail_id = lt.id
          AND lte.workspace_id = '$workspace_id'
          AND lte.deleted = FALSE
    )
    GROUP BY lts.mission_id
)
SELECT
    mission_enrollment.id AS enrolment_id,
    "user".name AS user_name,
    "user".email AS user_email,
    "user".status AS user_status,
    CASE
        WHEN urw.user_id IS NOT NULL THEN False
        ELSE True
    END AS user_deleted,
    array_to_string(array_agg(DISTINCT COALESCE(job.name, '')), '; ', '') AS user_job,
    array_to_string(array_agg(DISTINCT COALESCE(job_function.name, '')), '; ', '') AS user_job_function,
    COALESCE("user".country::VARCHAR(40), '----') AS user_country,
    mission.name AS mission_name,
    mission_enrollment.status AS enrollment_status,
    mission_enrollment.created_date AS date_enroll,
    mission_enrollment.start_date AS date_start,
    mission_enrollment.goal_date AS goal_date,
    mission_enrollment.end_date AS date_end,
    COALESCE(mission_enrollment.performance, 0) AS performance,
    mission_enrollment.progress AS progress,
    mission_enrollment.points AS points,
    CASE
        WHEN mission_enrollment.give_up THEN 'GIVE UP'
        ELSE 'NOT GIVE UP'
    END AS give_up,
    COALESCE(mission.duration_time, 0) * INTERVAL '1 second' AS mission_duration,
    ROUND(
        COALESCE((mission.duration_time::NUMERIC * mission_enrollment.performance), 0)::NUMERIC,
        0
    ) * INTERVAL '1 second' AS estimated_consume,
    mission.mission_model AS mission_model,
    mission_provider.name AS mission_provider,
    mission.language AS mission_language,
    COALESCE(mission_evaluation.nps, 0) AS mission_nps,
    COALESCE(mission.minimum_performance, 0) AS minimum_performance,
    mission.internal_code AS internal_code,
    COALESCE(ul.email::VARCHAR(255), '----') AS user_leader,
    "user".ein AS user_ein,
    mission.deleted AS mission_deleted,
    mission.deleted_date AS mission_deleted_date,
    mission.expiration_date AS mission_expiration_date,
    COALESCE(learning_trail_data.learning_trail_names, '----') AS learning_trail_linked,
    mission_enrollment.total_correct_answers AS quiz_hits,
    mission_enrollment.total_mission_questions AS total_quizzes,
    CASE
        WHEN mission_enrollment.status = 'STARTED'
        THEN True
        ELSE False
    END AS mission_enrollment_active,
    COALESCE(mission_rating.rating, 0) AS rating,
    COALESCE(mission_enrollment.certificate_url, mission_enrollment.certificate_provider_url) AS certificate_url
FROM
    mission_enrollment
JOIN
    mission ON mission.id = mission_enrollment.mission_id
JOIN
    "user" ON "user".id = mission_enrollment.user_id
LEFT JOIN
    user_profile_workspace ON user_profile_workspace.user_id = "user".id
        AND user_profile_workspace.workspace_id = '$workspace_id'
LEFT JOIN
    job ON user_profile_workspace.job_position_id = job.id
LEFT JOIN
    job_function ON user_profile_workspace.job_function_id = job_function.id
LEFT JOIN
    "user" AS ul ON ul.id = "user".related_user_leader_id
LEFT JOIN
    external_mission ON external_mission.mission_id = mission.id
LEFT JOIN
    mission_provider ON external_mission.provider_id = mission_provider.id
LEFT JOIN
    mission_evaluation ON mission_enrollment.id = mission_evaluation.enrollment_id
LEFT JOIN
    mission_rating ON mission.id = mission_rating.mission_id
        AND "user".id = mission_rating.user_id
LEFT JOIN
    user_role_workspace urw ON urw.user_id = "user".id
        AND urw.workspace_id = '$workspace_id'
LEFT JOIN
    learning_trail_data ON learning_trail_data.mission_id = mission_enrollment.mission_id
WHERE
    mission_enrollment.workspace_id = '$workspace_id'
    AND mission_enrollment.deleted = FALSE
    $filters
GROUP BY
    mission_enrollment.id, "user".name, "user".email, "user".status, "user".country,
    mission.name, mission_enrollment.status, mission_enrollment.created_date,
    mission_enrollment.start_date, mission_enrollment.goal_date, mission_enrollment.end_date,
    mission_enrollment.performance, mission_enrollment.progress, mission_enrollment.points,
    mission_enrollment.give_up, mission.duration_time, mission.mission_model,
    mission_provider.name, mission.language, mission_evaluation.nps, mission_rating.rating,
    mission.minimum_performance, mission.internal_code, "user".ein, ul.email,
    mission.deleted, mission.deleted_date, mission.expiration_date,
    learning_trail_data.learning_trail_names, urw.user_id, mission_enrollment.certificate_url, mission_enrollment.certificate_provider_url;
