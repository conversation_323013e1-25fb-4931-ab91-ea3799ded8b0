SELECT
    learning_trail.id as "object_id",
    "user".id as "user_id",
	"user"."name" as "user_name",
	"user".email as "user_email",
	COALESCE(ul.email::varchar(255), '----') as "user_leader",
	learning_trail."name" as "name",
    learning_trail_enrollment.status as "status",
    learning_trail_enrollment.performance as "performance",
	learning_trail_enrollment.points as "points",
	COALESCE(learning_trail.duration_time, 0) * interval '1 sec' as "duration",
    learning_trail_enrollment.start_date as "date_start",
	learning_trail_enrollment.end_date as "date_end"
FROM
    learning_trail_enrollment
JOIN
    learning_trail on learning_trail.id=learning_trail_enrollment.learning_trail_id
JOIN
    "user" on "user".id=learning_trail_enrollment.user_id
LEFT JOIN
    "user" AS ul  on ul.id = "user".related_user_leader_id
WHERE
    learning_trail_enrollment.workspace_id='$workspace_id' and learning_trail_enrollment.deleted = false
$filters