SELECT
    DISTINCT(answer.id) as "answer_id",
	mission."name" as "mission_name",
	"user"."name" as "user_name",
	"user"."email" as "user_email",
	mission_enrollment.id as "enrollment_id",
	mission_enrollment.status as "enrollment_status",
	mission_enrollment.end_date as "enrolment_end_date",
	mission_enrollment.performance as "enrollment_performance",
	job.name as "user_job",
	COALESCE("user".country::varchar(40), '----') as "user_country",
	"user".ein as "user_ein",
	COALESCE(related_user_leader.email::varchar(255), '----') as "user_leader",
    question.exam_question as "exam_question",
	answer.is_ok as "hit",
	answer.created_date as "answer_date"
FROM
    answer  
JOIN
    "user" on answer.user_id="user".id
LEFT JOIN
    user_profile_workspace on user_profile_workspace.user_id = "user".id and user_profile_workspace.workspace_id = '$workspace_id'
LEFT JOIN
    job on user_profile_workspace.job_position_id = job.id
LEFT JOIN
    "user" related_user_leader on related_user_leader.id = "user".related_user_leader_id
JOIN
    question on answer.exam_has_question_id=question.id
JOIN
    exam on question.exam_id=exam.id
JOIN
    mission_stage on exam.stage_id=mission_stage.id
JOIN
    mission on mission_stage.mission_id=mission.id
JOIN
    mission_enrollment on mission_enrollment.id=answer.enrollment_id
WHERE
    mission_enrollment.workspace_id='$workspace_id' and answer.deleted = false and mission_enrollment.deleted = false
$filters
