WITH group_user_counts AS (
    SELECT
        gu_2.group_id,
        COUNT(DISTINCT gu_2.user_id) AS users_linked_in_the_group
    FROM group_user gu_2
    WHERE gu_2.deleted = False
    GROUP BY gu_2.group_id
),
group_users_enrollment_counts AS (
    SELECT
        gu_2.group_id,
        COUNT(DISTINCT gu_2.user_id) AS group_users_enrolled
    FROM group_user gu_2
    JOIN group_mission gm2 ON gm2.group_id = gu_2.group_id
    JOIN mission_enrollment me2 ON me2.user_id = gu_2.user_id
        AND me2.mission_id = gm2.mission_id
        AND me2.workspace_id = '$workspace_id'
        AND me2.deleted = False
    WHERE gu_2.deleted = False
    GROUP BY gu_2.group_id
)
SELECT
    "group".id AS "group_id",
    "group"."name" AS "group_name",
    mission.id AS "mission_id",
    mission."name" AS "mission_name",
    COALESCE(mission.duration_time, 0) * interval '1 sec' AS "duration",
    "user"."name" AS "user_name",
    "user".email AS "user_email",
    mission_enrollment.status AS "enrollment_status",
    mission_enrollment.performance AS "performance",
    mission_enrollment.start_date AS "date_start",
    mission_enrollment.end_date AS "date_end",
    COALESCE(group_user_counts.users_linked_in_the_group, 0) AS "users_linked_in_the_group",
    COALESCE(group_users_enrollment_counts.group_users_enrolled, 0) AS "group_users_enrolled"
FROM
    group_mission
JOIN
    group_user ON group_mission.group_id = group_user.group_id AND group_user.deleted = False
JOIN
    "user" ON "user".id = group_user.user_id
JOIN
    "group" ON group_mission.group_id = "group".id
JOIN
    mission ON mission.id = group_mission.mission_id
JOIN
    mission_workspace ON mission_workspace.mission_id = mission.id
LEFT JOIN
    mission_enrollment ON "user".id = mission_enrollment.user_id
    AND mission.id = mission_enrollment.mission_id
    AND mission_enrollment.workspace_id = '$workspace_id'
    AND mission_enrollment.deleted = False
LEFT JOIN
    group_user_counts ON group_user_counts.group_id = group_mission.group_id
LEFT JOIN
    group_users_enrollment_counts ON group_users_enrollment_counts.group_id = group_mission.group_id
WHERE
    mission_workspace.workspace_id = '$workspace_id'
    AND mission_workspace.relationship_type = 'OWNER'
    AND group_mission.deleted = False
$filters
GROUP BY
    "group".id,
    "group".name,
    group_mission.group_id,
    mission.id,
    mission."name",
    mission.duration_time,
    "user"."name",
    "user".email,
    mission_enrollment.status,
    mission_enrollment.performance,
    mission_enrollment.start_date,
    mission_enrollment.end_date,
    group_user_counts.users_linked_in_the_group,
    group_users_enrollment_counts.group_users_enrolled
ORDER BY
    "group"."name"
