SELECT
    "user".id as "user_id",
    "user".last_access_date as "last_access_date",
	(
        SELECT lca.time_stop
        FROM learn_content_activity lca
        LEFT JOIN pulse_channel pc on pc.pulse_id = lca.pulse_id
        LEFT JOIN channel c on c.id = pc.channel_id
        LEFT JOIN mission_enrollment me on lca.mission_enrollment_id = me.id
        WHERE lca.user_id = "user".id and (
            me.workspace_id = '$workspace_id'
            or c.workspace_id = '$workspace_id'
            or lca.workspace_id = '$workspace_id'
        )
        ORDER BY lca.created_date DESC LIMIT 1
    ) as "last_activity_date"
FROM
    "user"
WHERE
    "user".id in ($users_ids)