SELECT
    "user".id as "user_id"
FROM
    learn_content_activity
JOIN
    "user" on learn_content_activity.user_id = "user".id
LEFT JOIN
    mission_enrollment on learn_content_activity.mission_enrollment_id = mission_enrollment.id
LEFT JOIN
    pulse_channel on pulse_channel.pulse_id = learn_content_activity.pulse_id
LEFT JOIN
    channel on pulse_channel.channel_id = channel.id
WHERE
    (mission_enrollment.workspace_id = '$workspace_id' or channel.workspace_id = '$workspace_id') 
    AND learn_content_activity.deleted = false
    AND learn_content_activity.time_start >= '$gte_date'
    AND learn_content_activity.time_start <= '$lte_date'
GROUP BY
    "user".id;
