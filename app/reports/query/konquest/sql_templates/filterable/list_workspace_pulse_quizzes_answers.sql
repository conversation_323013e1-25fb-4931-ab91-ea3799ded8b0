SELECT
    DISTINCT(answer.id) as "answer_id",
	pulse."name" as "pulse_name",
	"user"."name" as "user_name",
	"user"."email" as "user_email",
    question.exam_question as "exam_question",
	answer.is_ok as "hit",
	answer.created_date as "answer_date"
FROM
    answer
JOIN
    "user" on answer.user_id="user".id
JOIN
    question on answer.exam_has_question_id=question.id
JOIN
    exam on question.exam_id=exam.id
JOIN
    pulse on exam.pulse_id = pulse.id
JOIN
    pulse_channel on pulse.id = pulse_channel.pulse_id
JOIN
    channel on pulse_channel.channel_id = channel.id
WHERE
    channel.workspace_id='$workspace_id' and answer.deleted = false
$filters
