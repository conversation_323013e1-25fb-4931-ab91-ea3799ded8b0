SELECT
    DISTINCT("user".email) as "user_email",
    TO_CHAR("user".last_access_date, 'YYYY-MM-DD HH24:MI') as "last_access",
    MAX(TO_CHAR(learn_content_activity.time_start, 'YYYY-MM-DD HH24:MI')) as "last_activity"
FROM
    "user"
LEFT JOIN
    learn_content_activity on "user".id = learn_content_activity.user_id
WHERE
    "user".email in ($user_email_list)
GROUP BY
    "user".email,
    "user".last_access_date

