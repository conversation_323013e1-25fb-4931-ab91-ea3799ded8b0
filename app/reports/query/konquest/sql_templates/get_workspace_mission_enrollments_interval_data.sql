WITH myconstants(today_date, lowest_date) as (
   values(TO_TIMESTAMP('$today_datetime', 'YYYY-MM-DD HH:MM'),
          TO_TIMESTAMP('$today_datetime', 'YYYY-MM-DD HH:MM') - interval '$interval_days days')
)

SELECT
    (
        SELECT count(me.id)
        FROM myconstants,
             mission_enrollment me
        WHERE me.workspace_id = '$workspace_id'
          and me.status = 'COMPLETED' and me.end_date >= lowest_date
    ) as "count_enrollments_completed",
    (
        SELECT (
            --calculate the growth percentage considering the previous period
            (
                COUNT(me.id) filter(where me.end_date >= lowest_date) -
                COUNT(me.id) filter(where me.end_date < lowest_date)
            ) / greatest(COUNT(me.id) filter(where me.end_date < lowest_date), 1)::float4
        )
        FROM myconstants,
             mission_enrollment me
        WHERE me.workspace_id = '$workspace_id'
          and me.status = 'COMPLETED'
          and me.end_date >= lowest_date - interval '$interval_days days'
    ) as "growth_enrollments_completed",
    (
        SELECT COUNT(DISTINCT(me.user_id))
        FROM myconstants,
             mission_enrollment me
        WHERE me.workspace_id = '$workspace_id'
          and me.status = 'COMPLETED' and me.end_date >= lowest_date
    ) as "count_enrollments_completed_users",
    (
        SELECT count(me.id)
        FROM myconstants,
             mission_enrollment me
        WHERE me.workspace_id = '$workspace_id'
          and me.status != 'COMPLETED' and me.created_date >= lowest_date
    ) as "count_enrollments_in_progress",
    (
        SELECT (
            --calculate the growth percentage considering the previous period
            (
                COUNT(me.id) filter(where me.created_date >= lowest_date) -
                COUNT(me.id) filter(where me.created_date < lowest_date)
            ) / greatest(COUNT(me.id) filter(where me.created_date < lowest_date), 1)::float4
        )
        FROM myconstants,
             mission_enrollment me
        WHERE me.workspace_id = '$workspace_id'
          and me.status != 'COMPLETED'
          and me.created_date >= lowest_date - interval '$interval_days days'
    ) as "growth_enrollments_in_progress",
    (
        SELECT count(distinct(me.user_id))
        FROM myconstants,
             mission_enrollment me
        WHERE me.workspace_id = '$workspace_id'
          and me.status != 'COMPLETED' and me.created_date >= lowest_date
    ) as "count_enrollments_in_progress_users"
FROM
    workspace
WHERE
    workspace.id = '$workspace_id'