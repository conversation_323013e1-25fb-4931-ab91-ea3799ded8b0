SELECT
    US.name as "user_name",
    US.avatar as "user_avatar",
    MS.created_date as "created_date",
    MS.duration_time as "duration_time",
    MS.points as "points",
    ROUND(AVG(MR.rating), 2) as "rating_avg"
FROM
    mission MS
JOIN
    "user" US on US.id=MS.user_creator_id
LEFT JOIN
    mission_rating MR on MR.mission_id=MS.id
WHERE
    MS.id = '$mission_id'
GROUP BY
    US.name, US.avatar, MS.created_date, MS.duration_time, MS.points