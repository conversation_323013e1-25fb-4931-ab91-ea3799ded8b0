SELECT
	COALESCE(SUM(CASE WHEN mission_enrollment.performance BETWEEN 0 AND 0.0999 THEN 1 ELSE 0 END), 0) as "count_performances_0_10",
	COALESCE(SUM(CASE WHEN mission_enrollment.performance BETWEEN 0.10 AND 0.2999 THEN 1 ELSE 0 END), 0) as "count_performances_10_30",
	COALESCE(SUM(CASE WHEN mission_enrollment.performance BETWEEN 0.30 AND 0.4999 THEN 1 ELSE 0 END), 0) as "count_performances_30_50",
	COALESCE(SUM(CASE WHEN mission_enrollment.performance BETWEEN 0.50 AND 0.7499 THEN 1 ELSE 0 END), 0) as "count_performances_50_75",
	COALESCE(SUM(CASE WHEN mission_enrollment.performance BETWEEN 0.75 AND 1.00 THEN 1 ELSE 0 END), 0) as "count_performances_75_100"
FROM
    mission_enrollment
WHERE
    mission_enrollment.workspace_id='$workspace_id'
AND mission_enrollment.status IN ('COMPLETED', 'REPROVED')