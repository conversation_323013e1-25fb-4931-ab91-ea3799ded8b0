SELECT
    DISTINCT("group".id) as "group_id",
    "group".name as "group_name",
     COUNT(group_user.id) as "count_users",
     COALESCE(SUM(CASE WHEN mission_enrollment.status IN ('COMPLETED', 'REPROVED') THEN 1 ELSE 0 END), 0) as "finished_mission_enrollments",
     COALESCE(SUM(CASE WHEN mission_enrollment.status NOT IN ('COMPLETED', 'REPROVED', 'ENROLLED') THEN 1 ELSE 0 END), 0) as "started_mission_enrollments",
     COALESCE(SUM(CASE WHEN mission_enrollment.status NOT IN ('COMPLETED', 'REPROVED', 'ENROLLED') THEN 1 ELSE 0 END), 0) as "enrolled_mission_enrollments"
FROM
    group_mission
JOIN
    "group" on group_mission.group_id="group".id
JOIN
    mission on mission.id=group_mission.mission_id
JOIN
    group_user on group_mission.group_id=group_user.group_id
JOIN
    "user" on group_user.user_id="user".id
JOIN
    mission_enrollment on mission_enrollment.user_id="user".id and mission_enrollment.mission_id=mission.id
WHERE
    mission.id='$mission_id'
GROUP by
	 "group".id