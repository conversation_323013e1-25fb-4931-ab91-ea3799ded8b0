SELECT
    COUNT(mission_enrollment.id) as "count_enrollments",
    COALESCE(SUM(case when mission_enrollment.status IN ('COMPLETED', 'REPROVED') then 1 else 0 end), 0) as "count_enrollments_done",
    COALESCE(SUM(case when mission_enrollment.status NOT IN ('COMPLETED', 'REPROVED') then 1 else 0 end), 0) as "count_enrollments_in_progress"
FROM
    mission
LEFT JOIN
    mission_enrollment on mission.id = mission_enrollment.mission_id
WHERE
    mission.id = '$mission_id'
$filters