SELECT
	DISTINCT(exam.id) as "exam_id",
	mission_stage_content.name as "exam_name",
    COUNT(DISTINCT(question.id)) as "count_questions",
	COUNT(DISTINCT(answer.id)) as "count_answers",
	COALESCE(SUM(CASE WHEN answer.is_ok THEN 1 ELSE 0 END), 0) as "count_hits",
	COALESCE(SUM(CASE WHEN NOT answer.is_ok THEN 1 ELSE 0 END), 0) as "count_mistakes"
FROM
    mission
JOIN mission_stage on mission.id = mission_stage.mission_id
JOIN mission_stage_content on mission_stage_content.stage_id = mission_stage.id
JOIN exam on mission_stage_content.learn_content_uuid = exam.id
JOIN question on question.exam_id=exam.id
LEFT JOIN answer on answer.exam_has_question_id=question.id
WHERE
    mission.id='$mission_id'
GROUP BY
	exam.id,
    "exam_name"