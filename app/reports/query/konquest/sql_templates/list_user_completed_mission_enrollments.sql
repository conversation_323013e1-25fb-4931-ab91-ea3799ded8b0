SELECT
    me.*,
    m.name as "mission_name",
    COALESCE(COUNT(distinct q.id), 0) as "count_questions",
    COALESCE(COUNT(distinct a.id) filter ( where a.is_ok is true), 0) as "count_hits"
FROM
    mission_enrollment me
JOIN
    mission m on m.id = me.mission_id
LEFT JOIN
    mission_stage ms on m.id = ms.mission_id
LEFT JOIN
    exam e on ms.id = e.stage_id
LEFT JOIN
    question q on e.id = q.exam_id
LEFT JOIN
    answer a on me.id = a.enrollment_id
WHERE
    me.user_id = '$user_id' and me.workspace_id = '$workspace_id' and me.status in ('COMPLETED', 'REPROVED')
GROUP BY me.id, m.name, me.points, start_date, end_date, goal_date, give_up, give_up_comment, me.mission_id, me.user_id, me.created_date, me.updated_date, status, performance, certificate_url, approve_msg, certificate_provider_url, workspace_id, progress