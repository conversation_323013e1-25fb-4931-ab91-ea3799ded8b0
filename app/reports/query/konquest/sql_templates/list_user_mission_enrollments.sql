SELECT
    DISTINCT(me.id),
    m.name as "mission_name",
    m.minimum_performance as "minimum_performance",
    me.progress as "progress",
    mc.name as "mission_category",
    mt.name as "mission_type",
    me.performance as "performance",
    COALESCE(me.assessment_type, 'FULL') as "assessment_type",
    COALESCE(m.minimum_performance, w.min_performance_certificate) as "minimum_performance",
    me.start_date as "start_date",
    me.end_date as "end_date",
    me.goal_date as "goal_date",
    me.status as "status",
    m.mission_model as "mission_model",
    COALESCE(me.total_mission_questions, 0) as "total_mission_questions",
    COALESCE(me.total_correct_answers, 0) as "total_correct_answers"
FROM
    mission_enrollment me
JOIN
    mission m ON me.mission_id = m.id
LEFT JOIN
    mission_category mc ON m.mission_category_id = mc.id
JOIN
    mission_type mt ON m.mission_type_id = mt.id
JOIN
    workspace w ON me.workspace_id = w.id
WHERE
    me.user_id='$user_id'
    AND me.workspace_id='$workspace_id'
    AND me.deleted=FALSE