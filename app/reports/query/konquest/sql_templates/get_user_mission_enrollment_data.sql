SELECT
    (
        SELECT(
                (
                    COUNT(me.id) filter (where me.end_date is not NULL)
                )/ GREATEST(COUNT(me.id), 1)::float4
        )
        FROM
           mission_enrollment as me
        WHERE me.user_id = '$user_id'
          and me.workspace_id = '$workspace_id'
    ) as "conclusion_rate",
    (
        SELECT
            COUNT(me.id)
        FROM
            mission_enrollment me
        WHERE me.user_id = '$user_id'
          and me.workspace_id = '$workspace_id'
    ) as "count_enrollments",
    (
        SELECT
            COUNT(me.id)
        FROM
            mission_enrollment me
        WHERE me.user_id = '$user_id'
          and me.workspace_id = '$workspace_id'
          and me.end_date is not NULL
    ) as "count_completed_enrollments"
FROM
    "user" u
WHERE
    u.id = '$user_id'