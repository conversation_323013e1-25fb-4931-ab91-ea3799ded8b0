SELECT
  lt.name as "trail_name",
  ltt.name as "trail_type",
  le.progress as "progress",
  le.performance as "performance",
  le.start_date as "start_date",
  le.status as "status",
  le.end_date as "end_date",
  le.goal_date as "goal_date"
FROM
    learning_trail_enrollment le
JOIN
    learning_trail lt ON le.learning_trail_id = lt.id
JOIN
    learning_trail_type ltt ON lt.learning_trail_type_id = ltt.id
WHERE le.user_id='$user_id'
    AND le.workspace_id='$workspace_id'
    AND le.deleted=FALSE
