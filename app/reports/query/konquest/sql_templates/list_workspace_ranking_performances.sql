SELECT
    us.id as "user_id",
    us.name as "user_name",
    coalesce(AVG(me.performance)::numeric(10,2), 0) as "performance",
    coalesce(AVG(me.points)::numeric(10,2), 0) as "points"
FROM
    "user" as us
LEFT JOIN
    mission_enrollment me on us.id = me.user_id
WHERE
    me.workspace_id = '$workspace_id'
AND
    me.status='COMPLETED'
AND
    me.performance IS NOT NULL
GROUP BY
    us.id
ORDER BY
    AVG(me.performance) DESC, AVG(me.points) DESC
LIMIT
    10;