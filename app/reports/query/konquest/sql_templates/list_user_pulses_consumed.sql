SELECT
    p.id as "id",
    p.name as "name",
    pt.name as "type",
    p.duration_time as "duration",
    sum(lca.time_in) as "consume_duration",
    c.name as "channel_name",
    cc.name as "category"
FROM
    pulse p
JOIN
    pulse_type pt on pt.id = p.pulse_type_id
JOIN
    learn_content_activity lca on p.id = lca.pulse_id
JOIN
    pulse_channel pc on p.id = pc.pulse_id
JOIN
    channel c on pc.channel_id = c.id
JOIN
    channel_category cc on cc.id = c.channel_category_id
WHERE
    c.workspace_id = '$workspace_id'
    AND lca.user_id = '$user_id'
    AND lca.deleted = FALSE
group by p.id, pt.name, c.name, cc.name