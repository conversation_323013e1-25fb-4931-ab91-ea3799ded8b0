SELECT
    "user".name as "user_name",
    mission_enrollment.performance as "performance",
    mission_enrollment.points as "points"
FROM
    mission_enrollment
JOIN
    "user" on "user".id=mission_enrollment.user_id
WHERE
    mission_enrollment.mission_id = '$mission_id'
AND
    (mission_enrollment.status='COMPLETED' OR mission_enrollment.status='REPROVED')
AND
    mission_enrollment.performance IS NOT NULL
ORDER BY
    mission_enrollment.performance DESC,
    mission_enrollment.points DESC
LIMIT
    10;