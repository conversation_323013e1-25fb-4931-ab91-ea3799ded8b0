SELECT
    (
        SELECT count(me.id)
        FROM mission_enrollment me
        WHERE me.workspace_id = '$workspace_id'
    ) as "count_enrollments",
    (
        SELECT count(me.id)
        FROM mission_enrollment me
        WHERE me.workspace_id = '$workspace_id' and me.status = 'COMPLETED'
    ) as "count_enrollments_completed",
    (
        SELECT count(me.id)
        FROM mission_enrollment me
        WHERE me.workspace_id = '$workspace_id' and me.status not in('COMPLETED', 'REPROVED')
    ) as "count_enrollments_in_progress"
FROM
    workspace
WHERE
    workspace.id = '$workspace_id'