SELECT
    "user".id as "user_id",
    COUNT(answer.is_ok) AS "count_hits"
FROM
    answer
JOIN
    "user" on answer.user_id="user".id
JOIN
    question on answer.exam_has_question_id=question.id
JOIN
    "exam" on question.exam_id=exam.id
JOIN
    mission_stage on exam.stage_id=mission_stage.id
JOIN
    mission on mission_stage.mission_id=mission.id
WHERE
    mission.id = '$mission_id'
AND
    answer.is_ok is True
GROUP BY
    ("user".id, "user".name)
