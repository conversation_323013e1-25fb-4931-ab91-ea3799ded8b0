WITH myconstants(today_date, lowest_date) as (
   values(TO_TIMESTAMP('$today_datetime', 'YYYY-MM-DD HH:MM'),
          TO_TIMESTAMP('$today_datetime', 'YYYY-MM-DD HH:MM') - interval '$interval_days days')
)

SELECT
    (
        SELECT COUNT(distinct(pc.pulse_id))
        FROM myconstants, pulse_channel as pc
        LEFT JOIN pulse p on pc.pulse_id = p.id
        LEFT JOIN channel c on pc.channel_id = c.id
        WHERE c.workspace_id = '$workspace_id' AND p.created_date >= lowest_date
    ) as "count_pulses",
    (
        SELECT
            (COUNT(DISTINCT(pc.pulse_id)) filter(where p.created_date >= lowest_date) -
             COUNT(DISTINCT(pc.pulse_id)) filter(where p.created_date < lowest_date)) /
            greatest(COUNT(DISTINCT(pc.pulse_id)) filter(where p.created_date < lowest_date), 1)::float4
        FROM myconstants, pulse_channel as pc
        LEFT JOIN pulse p on pc.pulse_id = p.id
        LEFT JOIN channel c on pc.channel_id = c.id
        WHERE c.workspace_id = '$workspace_id' AND p.created_date >= lowest_date - interval '$interval_days days'
    ) as "growth_pulses",
    (
        SELECT COUNT(distinct(lca.pulse_id))
        FROM myconstants, learn_content_activity as lca
        LEFT JOIN pulse_channel pc on lca.pulse_id = pc.pulse_id
        LEFT JOIN channel c on c.id = pc.channel_id
        WHERE c.workspace_id = '$workspace_id' AND lca.created_date >= lowest_date
    ) as "count_pulses_consumed",
    (
        SELECT
            (COUNT(DISTINCT(lca.pulse_id)) filter(where lca.created_date >= lowest_date) -
             COUNT(DISTINCT(lca.pulse_id)) filter(where lca.created_date < lowest_date)) /
            greatest(COUNT(DISTINCT(lca.pulse_id)) filter(where lca.created_date < lowest_date), 1)::float4
        FROM myconstants, learn_content_activity as lca
        LEFT JOIN pulse_channel pc on lca.pulse_id = pc.pulse_id
        LEFT JOIN channel c on c.id = pc.channel_id
        WHERE c.workspace_id = '$workspace_id' AND lca.created_date >= lowest_date - interval '$interval_days days'
    ) as "growth_pulses_consumed"
FROM
    workspace
WHERE
    workspace.id = '$workspace_id'
