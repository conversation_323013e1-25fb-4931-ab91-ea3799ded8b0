SELECT
    (
        SELECT COUNT(distinct(pc.pulse_id))
        FROM pulse_channel as pc
        LEFT JOIN channel c on pc.channel_id = c.id
        WHERE c.workspace_id = '$workspace_id'
    ) as "count_pulses",
    (
        SELECT COUNT(distinct(lca.pulse_id))
        FROM learn_content_activity as lca
        LEFT JOIN pulse_channel pc on lca.pulse_id = pc.pulse_id
        LEFT JOIN channel c on c.id = pc.channel_id
        WHERE c.workspace_id = '$workspace_id'
    ) as "count_pulses_consumed",
    (
        SELECT
            "channel".name as "channel_name"
        FROM
            learn_content_activity
        JOIN
            pulse on pulse.id=pulse_id
        JOIN
            pulse_channel on pulse_channel.pulse_id=pulse.id
        JOIN
            channel on pulse_channel.channel_id=channel.id
        WHERE
            channel.workspace_id='$workspace_id'
        GROUP BY
            channel.id
        ORDER BY
            COUNT(learn_content_activity.id) DESC
        LIMIT
            1
    ) as "popular_channel"
FROM
    workspace
WHERE
    workspace.id = '$workspace_id'