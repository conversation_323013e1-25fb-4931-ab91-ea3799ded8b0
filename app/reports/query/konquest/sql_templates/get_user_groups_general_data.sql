SELECT
    (
        SELECT
            COUNT(g.id)
        FROM
            "group" g
        JOIN
            group_user gu on g.id = gu.group_id
        WHERE
            gu.user_id = '$user_id' AND g.workspace_id = '$workspace_id' AND gu.deleted = FALSE
    ) as "count_groups_linked",
    (
        SELECT
            COUNT(gm.id)
        FROM
            "group" g
        JOIN
            group_user gu on g.id = gu.group_id
        JOIN
            group_mission gm on g.id = gm.group_id
        WHERE
            gu.user_id = '$user_id' AND g.workspace_id = '$workspace_id' AND gu.deleted = FALSE
    ) as "count_groups_missions",
    (
        SELECT
            (
                (COUNT(me.id) filter (where me.end_date is not NULL) / greatest(COUNT(me.id), 1))::float4
            )
        FROM
            "group" g
        JOIN
            group_user gu on g.id = gu.group_id
        JOIN
            group_mission gm on g.id = gm.group_id
        JOIN
            mission_enrollment me on gm.mission_id = me.mission_id
        WHERE
            gu.user_id = '$user_id'
            AND g.workspace_id = '$workspace_id'
            AND me.workspace_id = '$workspace_id'
            AND me.user_id = '$user_id'
            AND me.deleted = FALSE
            AND gm.deleted = FALSE
            AND gu.deleted = FALSE
    ) as "enrollments_in_group_conclusion_rate"
FROM
    "user" u
WHERE
     u.id = '$user_id'
