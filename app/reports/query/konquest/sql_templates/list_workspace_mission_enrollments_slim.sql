SELECT
    mission_enrollment.id,
    user_id as "user_id",
    mission_id as "mission_id",
    mission.name as "mission_name",
    mission_enrollment.assessment_type as "assessment_type",
    TO_CHAR(mission_enrollment.created_date, 'YYYY-MM-DD') as "created_date",
    mission_enrollment.end_date as "end_date",
    mission_enrollment.status as "enrollment_status",
    COALESCE(mission_enrollment.performance, 0) as "performance",
    mission_enrollment.progress,
    mission_enrollment.total_mission_questions,
    mission_enrollment.total_correct_answers,
    w.name as "workspace_name",
    w.icon_url as "workspace_logo_url",
    w.custom_color as "workspace_color"
FROM
    mission_enrollment
JOIN
    "user" on mission_enrollment.user_id = "user".id
JOIN
    mission on mission_enrollment.mission_id = mission.id
JOIN
    workspace w on mission_enrollment.workspace_id = w.id
WHERE
    workspace_id = '$workspace_id'
    AND mission_enrollment.deleted = false
    AND mission_enrollment.status IN ('COMPLETED', 'REPROVED')
    $filters
LIMIT 25
