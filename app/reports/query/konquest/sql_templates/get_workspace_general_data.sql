SELECT
    (
        SELECT
            COUNT(mc.mission_id)
        FROM
            mission_workspace AS mc
        JOIN
            mission ON mission.id = mc.mission_id
        WHERE
            mc.workspace_id = '$workspace_id'
            AND mc.relationship_type = 'OWNER'
            AND mission.deleted = false
    ) as "count_missions_created",
    (
        SELECT
            COUNT(pc.pulse_id)
        FROM
            pulse_channel pc
        JOIN
            channel c on pc.channel_id = c.id
        WHERE
             c.workspace_id = '$workspace_id'
    ) as "count_pulses_created",
    (
        SELECT
            COALESCE(SUM(m.duration_time), 0)
        FROM
             mission_workspace as mc
        JOIN
             mission m on mc.mission_id = m.id
        WHERE mc.workspace_id = '$workspace_id'
          and mc.relationship_type = 'OWNER'
    ) as "sum_missions_duration",
    (
        SELECT
            COALESCE(SUM(p.duration_time), 0)
        FROM
            pulse_channel pc
        JOIN
            channel c on pc.channel_id = c.id
        JOIN
            pulse p on pc.pulse_id = p.id
        WHERE
             c.workspace_id = '$workspace_id'
    ) as "sum_pulses_duration",
    (
        SELECT
            COUNT(DISTINCT(lca.user_id))
        FROM
            learn_content_activity lca
        LEFT JOIN
            pulse p ON lca.pulse_id = p.id 
        LEFT JOIN 
            pulse_channel pc ON p.id = pc.pulse_id 
        LEFT JOIN 
            channel c ON pc.channel_id = c.id 
        LEFT JOIN 
            mission_stage_content msc ON lca.mission_stage_content_id = msc.id 
        LEFT JOIN 
            mission_stage ms ON msc.stage_id = ms.id 
        LEFT JOIN 
            mission_workspace mw ON ms.mission_id = mw.mission_id
        WHERE
            lca.user_id in ($user_ids)
            AND (
                c.workspace_id = '$workspace_id'
                OR (
                    mw.relationship_type = 'OWNER'
                )
            )
    ) as "count_users_active"
FROM
    workspace
WHERE
    workspace.id = '$workspace_id';
