SELECT
    (
        SELECT
	        COUNT(DISTINCT(pulse.id))
        FROM
            pulse
        JOIN
            pulse_channel on pulse_channel.pulse_id=pulse.id
        JOIN
            channel on channel.id=pulse_channel.channel_id
        WHERE
            pulse.user_creator_id='$user_id'
        AND
            channel.workspace_id = '$workspace_id'
        AND
            pulse.deleted = FALSE
        AND
            channel.deleted = FALSE
    ) as "count_pulses_created"
FROM
    "user" u
WHERE u.id = '$user_id'