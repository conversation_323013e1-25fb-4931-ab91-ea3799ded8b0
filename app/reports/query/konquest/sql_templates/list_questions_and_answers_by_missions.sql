SELECT
    question.id as "question_id",
    question.created_date as "question_created_date",
    question.exam_question,
    question_option.option,
    question_option.correct_answer,
    me.id as "mission_enrollment_id",
    (CASE WHEN a.options LIKE '%%'  || question_option.id || '%%' THEN true ELSE false END) as "checked",
    COALESCE(TO_CHAR(a.created_date, 'YYYY-MM-DD HH24:MI:SS'), '----') as "answer_date"
FROM
    question_option
JOIN
    question on question_option.question_id = question.id
JOIN
    exam e on question.exam_id = e.id
JOIN
    mission_stage ms on e.stage_id = ms.id
LEFT JOIN
    mission_enrollment me on ms.mission_id = me.mission_id
LEFT JOIN
    answer a ON question.id = a.exam_has_question_id and me.id = a.enrollment_id
WHERE
    ms.mission_id in ('$mission_ids')
    AND a.deleted = FALSE
    AND me.deleted = FALSE
    AND ms.deleted = FALSE
    AND question.deleted = FALSE
ORDER BY
    me.id, ms."order"