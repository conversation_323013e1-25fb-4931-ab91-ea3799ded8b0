SELECT
    g.id as "group_id",
    g.name as "group_name",
    COUNT(gm.mission_id) as "count_group_missions",
    COUNT(me.id) filter(where me.status in ('COMPLETED', 'REPROVED')) as "count_completed_enrollments",
    COUNT(me.id) filter(where me.status not in ('COMPLETED', 'REPROVED', 'STARTED')) as "count_enrollments_in_progress",
    COUNT(me.id) filter(where me.status = 'STARTED') as "count_enrolled_enrollments",
    COUNT(me.id) as "count_enrollments"
FROM
    "group" as g
LEFT JOIN
    group_mission gm on g.id = gm.group_id
LEFT JOIN
    group_user gu on g.id = gu.group_id
LEFT JOIN
    mission_enrollment me on g.workspace_id = me.workspace_id and gm.mission_id = me.mission_id and gu.user_id = me.user_id
WHERE
    g.workspace_id = '$workspace_id'
GROUP BY
    g.id
ORDER BY
    g.name