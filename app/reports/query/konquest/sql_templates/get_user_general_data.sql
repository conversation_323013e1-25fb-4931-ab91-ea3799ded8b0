WITH myconstants(today_date, lowest_date) as (
   values(TO_TIMESTAMP('$today_datetime', 'YYYY-MM-DD HH:MM'),
          TO_TIMESTAMP('$today_datetime', 'YYYY-MM-DD HH:MM') - interval '$interval_days days')
)

SELECT
    us.name as "user_name",
    us.last_access_date as "user_last_access",
    us.avatar as "user_avatar",
    (
        SELECT
            COALESCE(AVG(me.performance)::numeric(10, 2), 0)::float
        FROM
            myconstants, mission_enrollment as me
        WHERE me.user_id = us.id
          and me.workspace_id = '$workspace_id'
          and me.status in ('COMPLETED', 'REPROVED')
          and me.end_date >= lowest_date
    ) as "performance_average_on_last_days",
    (
        SELECT
	        COUNT(DISTINCT(TO_CHAR(learn_content_activity.time_start, 'YYYY-MM-DD')))  as "total_consume_days"
        FROM
            myconstants, learn_content_activity
        JOIN
            pulse on pulse.id=learn_content_activity.pulse_id
        JOIN
            pulse_channel on pulse_channel.pulse_id=pulse.id
        JOIN
            channel on channel.id=pulse_channel.channel_id
        WHERE
            learn_content_activity.user_id=us.id
        AND
            learn_content_activity.time_start >= lowest_date
        AND
            channel.workspace_id = '$workspace_id'
    ) as "count_pulse_consume_days_on_last_days",
    (
        SELECT
            COUNT(DISTINCT(TO_CHAR(lca.time_start, 'YYYY-MM-DD')))  as "total_consume_days"
        FROM
            myconstants, learn_content_activity lca
        JOIN
            mission_enrollment me on lca.mission_enrollment_id = me.id
        WHERE
            lca.user_id=us.id
        AND
            me.workspace_id = '$workspace_id'
        AND
            lca.time_start >= lowest_date
    ) as "count_days_consuming_mission_on_last_days",
    (
        SELECT
	        COUNT(DISTINCT(pulse.id))  as "total_consume_days"
        FROM
            myconstants, learn_content_activity
        JOIN
            pulse on pulse.id=learn_content_activity.pulse_id
        JOIN
            pulse_channel on pulse_channel.pulse_id=pulse.id
        JOIN
            channel on channel.id=pulse_channel.channel_id
        WHERE
            learn_content_activity.user_id=us.id
        AND
            channel.workspace_id = '$workspace_id'
        AND
            learn_content_activity.time_start >= lowest_date
    ) as "count_days_consuming_pulses_on_last_days",
    (
        SELECT
            COUNT(me.id)
        FROM
            myconstants, mission_enrollment me
        WHERE
            me.user_id = us.id
            AND me.workspace_id = '$workspace_id'
            AND me.status in ('COMPLETED', 'REPROVED')
            AND me.end_date >= lowest_date
    ) as "mission_enrollments_completed_on_last_days",
    (
        SELECT
            COALESCE(SUM(m.duration_time), 0)
        FROM
            mission_enrollment as me
        JOIN
            mission m on me.mission_id = m.id
        WHERE
            me.user_id = us.id
            AND me.workspace_id = '$workspace_id'
            AND me.status in ('COMPLETED', 'REPROVED')
    ) as "consumption_time_in_missions",
    (
        SELECT
	        COALESCE(SUM(pulse.duration_time), 0)  as "total_consume_days"
        FROM
            pulse
        JOIN
            pulse_channel on pulse_channel.pulse_id=pulse.id
        JOIN
            channel on channel.id=pulse_channel.channel_id
        WHERE
            channel.workspace_id = '$workspace_id'
            AND pulse.id in (
                SELECT
                    pulse_id
                FROM
                    learn_content_activity lca
                WHERE
                    lca.user_id = us.id
            )
    ) as "consumption_time_in_pulses",
    (
        SELECT
            COUNT(me.id)
        FROM
            mission_enrollment as me
        JOIN
            mission m on me.mission_id = m.id
        WHERE
            me.user_id = us.id
            AND me.workspace_id = '$workspace_id'
            AND me.status in ('COMPLETED', 'REPROVED')
    ) as "missions_consumed_total",
    (
        SELECT
            COUNT(me.id)
        FROM
            mission_enrollment as me
        JOIN
            mission m on me.mission_id = m.id
        WHERE
            me.user_id = us.id
            AND me.workspace_id = '$workspace_id'
    ) as "mission_enrollment_total",
    (
        SELECT
            COALESCE(AVG(me.performance)::numeric(10, 2), 0)::float
        FROM
            mission_enrollment as me
        WHERE
            me.user_id = us.id
            AND me.workspace_id = '$workspace_id'
            AND me.status in ('COMPLETED', 'REPROVED')
    ) as "performance_average",
    (
        SELECT
	        COUNT(DISTINCT(pulse.id))
        FROM
            learn_content_activity
        JOIN
            pulse on pulse.id=learn_content_activity.pulse_id
        JOIN
            pulse_channel on pulse_channel.pulse_id=pulse.id
        JOIN
            channel on channel.id=pulse_channel.channel_id
        WHERE
            learn_content_activity.user_id=us.id
        AND
            channel.workspace_id = '$workspace_id'
    ) as "pulses_consumed_total"
FROM
    "user" as us
WHERE
    us.id = '$user_id'