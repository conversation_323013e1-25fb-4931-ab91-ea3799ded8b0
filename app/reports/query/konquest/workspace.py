import uuid
from datetime import datetime
from typing import List

from reports.query import QueryExecutor
from sqlalchemy.engine import Engine


class KonquestWorkspaceQuery(QueryExecutor):
    def __init__(self, konquest_database_engine: Engine, template_folder: str):
        super().__init__(konquest_database_engine, template_folder)

    def get_by_id(self, workspace_id: str) -> dict:
        query_kwargs = {"workspace_id": str(workspace_id)}
        return self.get_first("get_workspace_by_id", **query_kwargs)

    def get_general_data(self, workspace_id: str, workspace_user_ids: List[str]) -> dict:
        workspace_user_ids = workspace_user_ids if workspace_user_ids else str(uuid.uuid4())
        query_kwargs = {
            "workspace_id": str(workspace_id),
            "user_ids": ','.join(f"'{user_id}'" for user_id in workspace_user_ids)
        }
        return self.get_first("get_workspace_general_data", **query_kwargs)

    def get_activity_user_data(self, workspace_id: str, interval_days: int, today: datetime) -> dict:
        query_kwargs = {
            "workspace_id": str(workspace_id),
            "interval_days": interval_days,
            "today_datetime": today,
        }
        return self.get_first("get_workspace_user_activity_data", **query_kwargs)

    def count_users_ghost(self, user_ids: List[str]) -> int:
        user_ids = user_ids if user_ids else str(uuid.uuid4())
        query_kwargs = {
            "user_ids": ','.join(f"'{user_id}'" for user_id in user_ids)
        }
        return self.get_first("count_ghost_users", **query_kwargs).get("count_users")

    def get_mission_enrollments_data(self, workspace_id: str) -> dict:
        return self.get_first("get_workspace_mission_enrollments_data", **{"workspace_id": str(workspace_id)})

    def get_mission_enrollments_interval_data(
            self,
            workspace_id: str,
            interval_days: int,
            today: datetime
    ) -> dict:
        query_kwargs = {
            "workspace_id": str(workspace_id),
            "interval_days": interval_days,
            "today_datetime": today,
        }
        return self.get_first("get_workspace_mission_enrollments_interval_data", **query_kwargs)

    def get_enrollment_range_performance(self, workspace_id: str) -> dict:
        query_kwargs = {"workspace_id": str(workspace_id)}
        return self.get_first("get_workspace_mission_enrollments_performance", **query_kwargs)

    def list_missions_learn_contents(self, workspace_id: str) -> List:
        query_kwargs = {"workspace_id": str(workspace_id)}
        return self.list("list_workspace_missions_learn_contents", **query_kwargs)

    def get_pulses_data(self, workspace_id: str) -> dict:
        query_kwargs = {"workspace_id": str(workspace_id)}
        return self.get_first("get_workspace_pulses_data", **query_kwargs)

    def get_pulses_data_interval(self, workspace_id: str, interval_days: int, today: datetime) -> dict:
        query_kwargs = {
            "workspace_id": str(workspace_id),
            "interval_days": interval_days,
            "today_datetime": today,
        }
        return self.get_first("get_workspace_pulses_data_interval", **query_kwargs)

    def list_count_consumes_by_pulse_content(self, workspace_id: str) -> List:
        query_kwargs = {"workspace_id": str(workspace_id)}
        return self.list("list_workspace_count_consumes_by_pulse_content", **query_kwargs)

    def list_pulse_learn_contents_ids(self, workspace_id: str) -> List:
        query_kwargs = {"workspace_id": str(workspace_id)}
        results = self.list("list_pulse_learn_contents", **query_kwargs)
        return [row.get('learn_content_id') for row in results] if results else []

    def list_top_user_performances(self, workspace_id: str) -> List:
        query_kwargs = {"workspace_id": str(workspace_id)}
        return self.list("list_workspace_ranking_performances", **query_kwargs)

    def list_group_general_data(self, workspace_id: str) -> List:
        query_kwargs = {"workspace_id": str(workspace_id)}
        return self.list("list_workspace_groups_general_data", **query_kwargs)

    def list_group_missions_data(self, workspace_id: str) -> List:
        query_kwargs = {"workspace_id": str(workspace_id)}
        return self.list("list_workspace_group_missions_data", **query_kwargs)

    def get_workspace_count_missions_and_pulses(self, workspace_id: str) -> dict:
        query_kwargs = {"workspace_id": str(workspace_id)}
        return self.get_first("get_workspace_count_missions_and_pulses", **query_kwargs)
