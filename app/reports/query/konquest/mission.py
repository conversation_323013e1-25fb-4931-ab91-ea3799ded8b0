import datetime
from typing import List

from reports.query import QueryExecutor
from sqlalchemy.engine import Engine


class KonquestMissionQuery(QueryExecutor):
    def __init__(self, konquest_database_engine: Engine, template_folder: str):
        super().__init__(konquest_database_engine, template_folder)

    def get_mission_by_id(self, mission_id: str) -> dict:
        query_kwargs = {"mission_id": str(mission_id)}
        return self.get_first("get_mission_by_id", **query_kwargs)

    def get_general_mission_data(self, mission_id: str) -> dict:
        query_kwargs = {"mission_id": str(mission_id)}
        return self.get_first("get_general_mission_information_by_id", **query_kwargs)

    def get_enrollments_mission_data(self, mission_id: str, filters: str = '') -> dict:
        query_kwargs = {"mission_id": str(mission_id), "filters": filters}
        return self.get_first("get_enrollments_mission_information", **query_kwargs)

    def get_enrollments_mission_data_by_days_filter(self, mission_id: str, days_ago: int) -> dict:
        oldest_date = datetime.datetime.today() - datetime.timedelta(days=days_ago)
        sql_filter = f"AND mission_enrollment.created_date > '{oldest_date}'" \
                     f" OR (mission_enrollment.mission_id = '{mission_id}'" \
                     f"    AND mission_enrollment.end_date > '{oldest_date}')"
        return self.get_enrollments_mission_data(mission_id, sql_filter)

    def get_enrollment_range_performance_mission(self, mission_id: str) -> dict:
        query_kwargs = {"mission_id": str(mission_id)}
        return self.get_first("get_enrollments_range_performance_mission", **query_kwargs)

    def list_users_correct_answers_by_mission(self, mission_id: str) -> List[dict]:
        query_kwargs = {"mission_id": str(mission_id)}
        return self.list("list_users_correct_answers_by_mission", **query_kwargs)

    def get_mission_total_questions(self, mission_id: str) -> int:
        query_kwargs = {"mission_id": str(mission_id)}
        return self.get_first("count_mission_total_questions", **query_kwargs).get("total_questions")

    def list_mission_learn_content_ids(self, mission_id: str) -> List:
        query_kwargs = {"mission_id": str(mission_id)}
        results = self.list("list_mission_learn_contents", **query_kwargs)
        return [row.get('learn_content_id') for row in results] if results else []

    def list_mission_exams_information(self, mission_id: str) -> List[dict]:
        query_kwargs = {"mission_id": str(mission_id)}
        return self.list("list_mission_exams_information", **query_kwargs)

    def list_mission_ranking_performances(self, mission_id: str) -> List[dict]:
        query_kwargs = {"mission_id": str(mission_id)}
        return self.list("list_mission_ranking_performances", **query_kwargs)

    def list_mission_top_users_quiz_hits(self, mission_id: str) -> List[dict]:
        query_kwargs = {"mission_id": str(mission_id)}
        return self.list("list_mission_top_users_hits", **query_kwargs)

    def list_mission_groups_data(self, mission_id: str) -> List[dict]:
        query_kwargs = {"mission_id": str(mission_id)}
        return self.list("list_mission_groups_data", **query_kwargs)
