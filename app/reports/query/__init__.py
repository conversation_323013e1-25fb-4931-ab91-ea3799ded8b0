import uuid
from abc import ABC
from string import Template
from typing import List

from sqlalchemy.engine import Connection, Engine
from sqlalchemy.orm.exc import NoResultFound
from sqlalchemy import text
from sqlalchemy.engine.result import Result

from config.default import Config


class QueryExecutor(ABC):
    def __init__(self, db_engine: Engine, template_folder: str):
        self._engine = db_engine
        self._template_folder = template_folder

    def list(self, query_file_name: str, **query_filters) -> List[dict]:
        with self._engine.connect() as connection:
            result = self._load_query(connection, query_file_name, **query_filters)
            results = self._list_results(result)

        return results

    def get_first(self, query_file_name: str, **query_filters) -> dict:
        with self._engine.connect() as connection:
            result = self._load_query(connection, query_file_name, **query_filters)
            try:
                result_list = self._list_results(result)
                return result_list[0]
            except IndexError:
                raise NoResultFound('No result found') from IndexError

    def _load_query(self, connection: Connection, query_file_name: str, **query_args) -> Result:
        query_args = self._format_query_args(query_args)
        with open(f"{Config.BASE_DIR}/{self._template_folder}/{query_file_name}.sql") as file_message:
            filters = query_args.pop('filters', None)
            sql_template = Template(file_message.read())
            template_vars = {}
            template_vars['filters'] = filters if filters else ''
            template_vars.update(query_args)
            sql = sql_template.safe_substitute(**template_vars)
            
            sql_obj = text(sql)
            
            result = connection.execute(sql_obj, **query_args)

        return result

    @staticmethod
    def _format_query_args(query_args: dict):
        for arg_key in query_args:
            value = query_args[arg_key]
            if isinstance(value, list):
                value = value if len(value) > 0 else [uuid.uuid4()]
                query_args[arg_key] = ','.join(f"'{item}'" for item in value)

        return query_args

    @staticmethod
    def _list_results(result: Result) -> List[dict]:
        return [dict(row._mapping) for row in result]