from typing import Optional

from pandas import DataFrame
from reports.query import QueryExecutor
from reports.query.query_selector_config import QuerySelectorConfig


class QueryExecutorWithConfigInjectable(QueryExecutor):
    def __init__(self, config: QuerySelectorConfig):
        super().__init__(config.database_engine, config.template_folder)
        self._filter_sql = config.filter_sql
        self._limit_filter_date_range = config.limit_filter_date_range
        self._table_name = config.table_name
        self._foreign_tables_names = config.foreign_tables_names
        self._limit_filter_date_range = config.limit_filter_date_range
        self._query_name = config.query_name
        self._keys_filters_dates = config.keys_filters_dates

    def execute(self, workspace_id: str, filters: Optional[dict] = None) -> DataFrame:
        sql_filter = self._filter_sql.builder_sql_filter(
            table_name=self._table_name,
            filters=filters,
            keys_filter_dates=self._keys_filters_dates,
            default_keys_filter_dates=self._keys_filters_dates[0],
            foreign_tables_names=self._foreign_tables_names,
            limit_filter_date_range=self._limit_filter_date_range
        )
        raw_data = self.list(self._query_name, **{"workspace_id": workspace_id, "filters": sql_filter})
        return DataFrame(raw_data)
