FROM python:3.11-slim

WORKDIR /app

ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    HOME=/app \
    PATH=/app/.local/bin/:$PATH

# Instale o Supervisor
RUN apt-get clean && apt-get update && apt-get install python3-dev python3-lxml libxml2-dev libxslt1-dev \
    supervisor git gcc g++ build-essential swig netcat-traditional -y && \
    apt-get clean

# Copie os arquivos de requisitos da aplicação
COPY requirements.txt .

# Instale as dependências necessárias
RUN pip install --no-cache-dir --upgrade pip setuptools gunicorn gevent -r requirements.txt

COPY supervisord.conf /etc/supervisord.conf
COPY . /app

RUN chmod +x /app/entrypoint.sh && \
    chmod +rwx -R /app

EXPOSE 8000

ENTRYPOINT ["/app/entrypoint.sh"]
