[supervisord]
Description=Celery Service
nodaemon=true
user=root
logfile=/dev/null
loglevel=info

[program:celery]
directory=/app
command=celery -A config.celery worker --loglevel=INFO -n worker1@%%h --concurrency=4
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
redirect_stderr=true
autostart=true
autorestart=true
startsecs=10
stopwaitsecs=600
killasgroup=true
priority=999

[program:scheduler]
directory=/app
command=python cron_scheduler.py
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
redirect_stderr=true
autostart=true
autorestart=true
startsecs=10
stopwaitsecs=600
priority=1000