#!/bin/sh

# Função para verificar se o banco de dados está disponível
check_db() {
    echo "Aguardando o banco de dados estar disponível..."

    host=$(echo "$DATABASE_URL" | sed -n 's/.*@\(.*\):.*/\1/p')

    # Loop enquanto o comando nc não conseguir se conectar ao banco de dados
    while ! nc -z -w 5 "$host" 5432; do
        echo "Banco de dados não está disponível. Tentando novamente em 1 segundo..."
        sleep 1
    done

    echo "Banco de dados está disponível."
}

# Executa a função de verificação do banco de dados
check_db

# Executa o comando de migração
alembic upgrade head;

# Inicia a aplicação Flask
exec "$@"