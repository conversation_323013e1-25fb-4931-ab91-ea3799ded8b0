import json
import random
import string
from dataclasses import asdict
from uuid import UUID

import pytest
from config.default import Config
from controller.api.report_dto import AuthenticatedUser
from domain.common.dependencies import DatabaseEngine, DatabaseSession, ElasticsearchConnection, ElasticsearchURL
from domain.report import models
from domain.report.models import Report, ReportType
from elasticmock import FakeElasticsearch
from elasticsearch_dsl import connections
from faker import Faker
from faker.providers import address, date_time, internet, person
from flask.testing import FlaskClient
from injector import Module, provider, singleton
from keeps_flask.app import create_app
from sqlalchemy import create_engine
from sqlalchemy.orm import scoped_session, sessionmaker
from utils import database_transaction

fake = Faker()
fake.add_provider(person)
fake.add_provider(date_time)
fake.add_provider(address)
fake.add_provider(internet)

# Function to generate a random UUID
def generate_random_uuid():
    return UUID(random.randint(0, 2**128-1)) # NOSONAR

# Function to generate a random birthday
def generate_random_birthday():
    return fake.date_of_birth(tzinfo=None, minimum_age=18, maximum_age=65)


@pytest.fixture
def auth_header():
    token = 'eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJSYzRyNmZMREJYWU1NcE00dS10OWhPOC1ob3hxNGhrdlVtcXo1WW9hQURzIn0.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.jkbqduawssk7rs4cQ3uonbUpJi-yi5MLQ1pZbSkZgffDOzYBCLxUQU0gMhioQk2lMBG4PRJU6eJIg-BN-EDb-UkcAjcyjwi4CC0U3Lca6fzvbbrrL202Tl4KFAZk4Q3aoMxWANcFg4-YjU-DQu3llKuc3fwaWrAMPMSAoESLvFe9MS_9qmH_bdSlzSy2qnMEe2_WJtM6c3IOo0xYycL1BB2Cg7PglatGek-_dHRu3zUZnM2tixeZP4qXGMu-zyBNkEeROXnLsJ5fVaP7wEQQOKJnh9oCn8wgVRX77plcRbLEZ_oqi_E7XksoaYFeAegYiFQlr8g4Oy7NYoPfNx9FWA'  # noqa: E501
    return 'Authorization', token


@pytest.fixture(scope='function')
def database():
    engine = create_engine('sqlite://', convert_unicode=True)
    models.Base.metadata.create_all(engine)
    session = scoped_session(sessionmaker(autocommit=False, autoflush=True, bind=engine))
    return session()


@pytest.fixture
def request_user():
    return AuthenticatedUser(
        id="59f23138-ef19-422c-8175-546cf1e86d67",
        name="Sample User",
        email="<EMAIL>",
        locale="pt-BR",
        time_zone="America/Sao_Paulo"
    ) 


@pytest.fixture
def app(injector):
    _app = create_app(injector)
    _app.init()
    return _app


@pytest.fixture
def report_type(database):
    with database_transaction(database):
        letters = string.ascii_letters
        name = ''.join(random.choice(letters) for i in range(10)) # NOSONAR
        report_type = ReportType(
            name=name,
            description="mission presentation",
            model="PRESENTATION",
            application="KONQUEST"
        )
        database.add(report_type)
    return report_type


@pytest.fixture
def client(app) -> FlaskClient:
    return app.test_client()


@pytest.fixture
def report_type_export(database):
    with database_transaction(database):
        letters = string.ascii_letters
        name = ''.join(random.choice(letters) for i in range(10)) # NOSONAR
        report_type = ReportType(
            name=name,
            description="enrollments list",
            model="EXPORT",
            application="KONQUEST"
        )
        database.add(report_type)
    return report_type


@pytest.fixture
def report(database, report_type, request_user):
    report = Report(
        report_type_id=report_type.id,
        user_creator=asdict(request_user),
        user_creator_id=request_user.id,
        language="pt-BR",
        workspace_id="85d26de3-a6e8-470f-9d52-45485fca41fc",
    )
    database.add(report)
    database.flush()
    return report


@pytest.fixture
def report_export_model(database, report_type_export, request_user):
    report = Report(
        report_type_id=report_type_export.id,
        user_creator=asdict(request_user),
        user_creator_id=request_user.id,
        language="pt-BR",
        workspace_id="85d26de3-a6e8-470f-9d52-45485fca41fc",
        file_format="XLSX"
    )
    database.add(report)
    database.flush()
    return report


@pytest.fixture
def user_doc():
    return {
        'id': 1,
        'name': 'Usuário Nome',
        'nickname': 'User',
        'email': '<EMAIL>',
        'secondary_email': '<EMAIL>',
        'phone': '(48) 99938-0011',
        'gender': 'MALE',
        'job': 'Admin',
        'birthday': '1920-11-19',
        'address': '',
        'avatar': '',
        'status': True,
        'created_date': '2020-01-27 12:51:36',
        'updated_date': '2020-01-27 12:51:36',
        'language_id': '',
        'model_type': 'user',
        'origin': 'myaccount'
    }


@pytest.fixture
def notification_doc():
    return {
        "_id": "1",
        "id": "1",
        "user_receiving": {
            "id": "bbf47825-8dfb-49bc-8ad8-f8adc775f95f",
            "name": "Keeps Admin",
            "email": "<EMAIL>"
        },
        "notification_action": "DOWNLOAD",
        "message": "Konquest Mission Enrollment Export XLSX done",
        "workspace_id": "a6b23c1f-60e9-474a-8bb6-59085b90355f",
        "url": "https://s3.amazonaws.com/keeps.reports/reports/default/konquest-user-missions-enrollments-a6b23c1f-60e9-474a-8bb6-59085b90355f-2021-06-01_08.53.01.760906.xlsx",  # noqa
        "object_type": "REPORT",
        "read": False,
        "image": "https://media.keepsdev.com/konquest/assets/notification/report.png"
    }


@pytest.fixture
def course_doc():
    return {
        'id': 1,
        'name': 'Course name',
        'description': 'Course description',
        'duration_time': 1000,
        'points': 100,
        'created_date': '2020-02-01T19:53:02.476Z',
        'updated_date': '2020-03-01T19:53:02.476Z',
        'is_active': True,
        'development_status': 'IN_PROGRESS',
        'ratings': [],
        'enrollments': [],
        'bookmarks': [],
        'owner_companies': [{
            'company_id': 1,
            'id': 1,
            'created_date': '2020-01-01T19:53:02.476Z',
            'updated_date': '2020-02-01T19:53:02.476Z',
        }],
        'goals': [],
        'user_creator': {
            'id': 1,
            'user_name': 'User name'
        },
        'mission_type': {
            'id': 1,
            'name': 'Type',
        },
        'mission_category': {
            'id': 1,
            "name": 'Category',
        }
    }


@pytest.fixture
def course_enrollment_started_doc():
    return {
        'id': 1,
        'enrollment': {
            'id': '1',
            'points': 100,
            'start_date': '2020-01-01T19:53:02.476Z',
            'end_date': '2020-01-20T19:53:02.476Z',
            'goal_date': '2020-01-31T19:53:02.476Z',
            'give_up': False,
            'give_up_comment': 'Comment',
            'user_id': '1',
            'created_date': '2020-01-01T19:53:02.476Z',
            'updated_date': '2020-01-22T19:53:02.476Z',
            'status': 'STARTED',
            'performance': None
        },
        'owner_companies': [{
            'company_id': 1,
            'id': 1,
            'created_date': '2020-01-01T19:53:02.476Z',
            'updated_date': '2020-02-01T19:53:02.476Z',
            'owner': True

        }],
        'model_type': 'enrollment'
    }


@pytest.fixture
def course_enrollment_completed_doc():
    return {
        'id': 1,
        'enrollment': {
            'id': '1',
            'points': 100,
            'start_date': '2020-01-01T19:53:02.476Z',
            'end_date': '2020-01-20T19:53:02.476Z',
            'goal_date': '2020-01-31T19:53:02.476Z',
            'give_up': False,
            'give_up_comment': 'Comment',
            'user_id': '1',
            'created_date': '2020-01-01T19:53:02.476Z',
            'updated_date': '2020-01-22T19:53:02.476Z',
            'status': 'COMPLETED',
            'performance': None
        },
        'owner_companies': [{
            'company_id': 1,
            'id': 1,
            'created_date': '2020-01-01T19:53:02.476Z',
            'updated_date': '2020-02-01T19:53:02.476Z',
            'owner': True

        }],
        'model_type': 'enrollment'
    }


@pytest.fixture
def enrollment_2_started_1_completed_docs():
    return [{
        'id': 1,
        'model_type': 'enrollment',
        'enrollment': {
            'id': '1',
            'points': 100,
            'start_date': '2020-01-01T19:53:02.476Z',
            'end_date': '2020-01-20T19:53:02.476Z',
            'goal_date': '2020-01-31T19:53:02.476Z',
            'give_up': False,
            'give_up_comment': 'Comment',
            'user_id': '1',
            'created_date': '2020-01-01T19:53:02.476Z',
            'updated_date': '2020-01-22T19:53:02.476Z',
            'status': 'STARTED',
            'performance': None
        },
        'owner_companies': [{
            'company_id': 1,
            'id': 1,
            'created_date': '2020-01-01T19:53:02.476Z',
            'updated_date': '2020-02-01T19:53:02.476Z',
            'owner': True
        }]
    }, {
        'id': 2,
        'model_type': 'enrollment',
        'enrollment': {
            'id': '2',
            'points': 100,
            'start_date': '2020-01-01T19:53:02.476Z',
            'end_date': '2020-01-20T19:53:02.476Z',
            'goal_date': '2020-01-31T19:53:02.476Z',
            'give_up': False,
            'give_up_comment': 'Comment',
            'user_id': '1',
            'created_date': '2020-01-01T19:53:02.476Z',
            'updated_date': '2020-01-22T19:53:02.476Z',
            'status': 'STARTED',
            'performance': None
        },
        'owner_companies': [{
            'company_id': 1,
            'id': 1,
            'created_date': '2020-01-01T19:53:02.476Z',
            'updated_date': '2020-02-01T19:53:02.476Z',
            'owner': True
        }]
    }, {
        'id': 3,
        'model_type': 'enrollment',
        'enrollment': {
            'id': '1',
            'points': 100,
            'start_date': '2020-01-01T19:53:02.476Z',
            'end_date': '2020-01-20T19:53:02.476Z',
            'goal_date': '2020-01-31T19:53:02.476Z',
            'give_up': False,
            'give_up_comment': 'Comment',
            'user_id': '1',
            'created_date': '2020-01-01T19:53:02.476Z',
            'updated_date': '2020-01-22T19:53:02.476Z',
            'status': 'COMPLETED',
            'performance': None
        },
        'owner_companies': [{
            'company_id': 1,
            'id': 1,
            'created_date': '2020-01-01T19:53:02.476Z',
            'updated_date': '2020-02-01T19:53:02.476Z',
            'owner': True
        }],
    }]


@pytest.fixture
def general_user_information():
    return {'user_name': 'Gisele',
            'user_created_date': '14/08/2019',
            'user_last_access_date': '13/08/2020',
            'user_avatar': 'https://s3.amazonaws.com/keeps.reports/assets/mascote_icon.png',
            'profile_rate_red': '0%'}


@pytest.fixture
def dataset_user_missions():
    return {'conclusion_rate': '100%',
            'total_enrollments': 1,
            'enrollments_finished': 1,
            'mission_list': [{'mission_name': 'Feedback', 'mission_performance': '4%', 'mission_quiz_points': 10},
                             {'mission_name': 'Feedback', 'mission_performance': '4%', 'mission_quiz_points': 10},
                             {'mission_name': 'Feedback', 'mission_performance': '4%', 'mission_quiz_points': 10}]
            }


@pytest.fixture
def dataset_user_pulses():
    return {'total_pulse_consume': 10,
            'total_pulse_created': 8,
            'pulse_list': [{'pulse_name': 'Conceitos e exemplos', 'consume_time': 1, 'pulse_time': 12},
                           {'pulse_name': 'Vamos nos desafiar?', 'consume_time': 1, 'pulse_time': 1},
                           {'pulse_name': 'Contribuição da liderança situacional nas organizações', 'consume_time': 1,
                            'pulse_time': 94},
                           {'pulse_name': 'A origem da palavra', 'consume_time': 180, 'pulse_time': 8},
                           {'pulse_name': '20 coisas para não se fazer no feedback', 'consume_time': 192,
                            'pulse_time': 17},
                           {'pulse_name': 'Explicando a metodologia', 'consume_time': 1, 'pulse_time': 9},
                           {'pulse_name': 'O que é a liderança situacional?', 'consume_time': 3, 'pulse_time': 5},
                           {'pulse_name': 'A cultura do feedback', 'consume_time': 1, 'pulse_time': 1},
                           {'pulse_name': 'Você sabe o que é feedback?', 'consume_time': 1, 'pulse_time': 3},
                           {'pulse_name': 'Exemplos lúdicos de liderança situacional', 'consume_time': 1,
                            'pulse_time': 7}],
            }


@pytest.fixture
def dataset_user_groups():
    return {'total_groups': 0,
            'total_missions_in_groups': 0,
            'total_enrollments_in_groups': '0%',
            'group_list': [{'group_name': 'VINCULADO A NENHUM GRUPO',
                            'total_mission': 0,
                            'enrollments_completed': 0,
                            'enrollments_in_progress': 0,
                            'enrollments_not_started': 0}]
            }


@pytest.fixture
def load_mocked_json():
    with open(f'{Config.BASE_DIR}/mocked_report_data.json', "r") as read_file:
        data = json.load(read_file)
        return data


@pytest.fixture
def konquest_user_pages_data(load_mocked_json):
    return load_mocked_json['konquest_user_overview']


@pytest.fixture
def konquest_company_pages_data(load_mocked_json):
    return load_mocked_json['konquest_company_overview']


@pytest.fixture
def konquest_course_pages_data(load_mocked_json):
    return load_mocked_json['konquest_course_overview']


@pytest.fixture
def smartzap_course_pages_data(load_mocked_json):
    return load_mocked_json['smartzap_course_overview']


@pytest.fixture
def konquest_company_missions_export_data(load_mocked_json):
    return load_mocked_json['konquest_company_missions']


@pytest.fixture
def konquest_company_missions_enrollments_export_data(load_mocked_json):
    return load_mocked_json['konquest_company_missions-enrollments']


@pytest.fixture
def konquest_company_pulses_and_channels_export_data(load_mocked_json):
    return load_mocked_json['konquest_company_pulses_and_channels']


class FakeElasticsearchModule(Module):
    @singleton
    @provider
    def es_connection(self, url: ElasticsearchURL) -> ElasticsearchConnection:
        """
        Args:
            url: the elasticsearch's address.

        Returns:
            A Elasticsearch connection instance.
        """
        conn = FakeElasticsearch(url)
        connections.create_connection(hosts=['localhost'], timeout=20)
        return conn


class FakeDatabaseModule(Module):
    @singleton
    @provider
    def engine(self) -> DatabaseEngine:
        """
        Args:
            url: the database's address.

        Returns:
            A engine connection instance.
        """
        return create_engine('sqlite://', convert_unicode=True)

    @singleton
    @provider
    def session(self, engine: DatabaseEngine) -> DatabaseSession:
        """
        Args:
            engine: a reference for DatabaseEngine.

        Returns:
            The scoped_session to use.
        """
        models.Base.metadata.create_all(engine)
        session = scoped_session(sessionmaker(autocommit=False, autoflush=True, bind=engine))
        return session()
