from domain.report.models import Report
from domain.report.services import ReportService

from controller.api.report_dto import ReportDto, AuthenticatedUser

SEND_TASK = "config.celery.celery.send_task"


def test_add_report(database, request_user, report_type, mocker):
    send_task_spy = mocker.patch(SEND_TASK)
    service = ReportService(database)
    _request_user = request_user
    report = service.add_report(ReportDto(
        type_name=report_type.name,
        request_user=_request_user,
        workspace_id="85d26de3-a6e8-470f-9d52-45485fca41fc",
        language="pt-BR",
        object_id="85d26de3-a6e8-470f-9d52-45485fca41fc"
    ))['report']
    assert database.query(Report).filter(Report.id == report.id).first()
    send_task_spy.assert_called_once()


def test_report_already_requested_error(database, request_user, report):
    service = ReportService(database)
    _request_user = request_user
    response = service.add_report(ReportDto(
        type_name=report.report_type.name,
        request_user=_request_user,
        workspace_id=report.workspace_id,
        language=report.language,
        object_id=report.object_id,
        file_format=report.file_format,
        filters=report.filters,
        time_zone='America/Sao_Paulo'
    ))
    assert response["status"] == "in_process"
    assert response["report"].id is report.id
