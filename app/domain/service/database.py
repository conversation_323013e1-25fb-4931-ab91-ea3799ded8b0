from domain.common.dependencies import DatabaseSession
from keeps_flask.validation.exceptions import NotFoundException
from utils import database_transaction as transaction


class AbstractDatabaseService:
    def __init__(self, database: DatabaseSession, model_type):
        self.database = database
        self.model_type = model_type

    def add(self, obj):
        with transaction(self.database) as session:
            session.add(obj)
            return obj

    def update(self, obj_id, obj):
        _ = self.load(obj_id)
        with transaction(self.database) as session:
            obj.id = obj_id
            return session.merge(obj)

    def load(self, obj_id):
        obj = self.database.query(self.model_type).filter_by(id=obj_id).first()
        if not obj:
            raise NotFoundException(f'{self.model_type.__name__} not found')
        return obj

    def load_all(self):
        query = self.database.query(self.model_type)
        return query.all()

    def delete(self, obj_id):
        obj = self.load(obj_id)
        with transaction(self.database) as session:
            session.delete(obj)

    def delete_in_batch(self, ids):
        with transaction(self.database) as session:
            session.query(self.model_type).filter(self.model_type.id.in_(ids)).delete(synchronize_session=False)
