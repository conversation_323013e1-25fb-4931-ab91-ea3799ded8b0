import collections
import math

import sqlalchemy_filters


class FilterResolver:
    def __init__(self, model_type):
        self.model_type = model_type

    def _get_attrib_model(self, field):
        attributes = [a for a in dir(self.model_type) if a == field]
        if attributes:
            attrib = getattr(self.model_type, attributes[-1], None)
            if not attrib:
                return None
            prop = getattr(attrib, 'property', None)
            if not prop:
                return None
            back_pop = getattr(prop, 'back_populates', '')
            use_list = getattr(prop, 'uselist', True)
            if back_pop and not use_list:
                return prop.entity.class_
        return None

    def _add_models(self, filters):
        join_models = {}
        for filter_name in ['filter_spec', 'sort_spec']:
            for f in filters.get(filter_name, []):
                f['model'] = self.model_type.__name__
                field_name = f.get('field', '')
                v = field_name.split('__')
                if len(v) >= 2:
                    attrib_name = v[0]
                    attrib_model = self._get_attrib_model(attrib_name)
                    if attrib_model:
                        field_name = '__'.join(v[1:])
                        f['model'] = attrib_model.__name__
                        f['field'] = field_name
                        join_models[attrib_model.__name__] = attrib_model
        return join_models

    def exec_query_filters(self, query, filters):
        self._add_models(filters)

        filter_spec = filters.get('filter_spec', [])
        if filter_spec:
            query = sqlalchemy_filters.apply_filters(query, filter_spec)

        sort_spec = filters.get('sort_spec', [])
        if sort_spec:
            query = sqlalchemy_filters.apply_sort(query, sort_spec)

        query, pagination = _paginate_filters(query, filters.get('pag_spec', {}))
        return query.all(), pagination


def _paginate_filters(query, spec):
    page = spec.get('page', 1)
    per_page = spec.get('per_page', None)
    return _pagination(query, page, per_page)


def _pagination(query, page, per_page):
    query, pagination = sqlalchemy_filters.apply_pagination(query, page_number=page, page_size=per_page)
    total_pages = int(math.ceil(float(pagination.total_results) / float(per_page))) if per_page else None
    return query, create_pagination(page, per_page, total_pages, pagination.total_results)


def create_pagination(page, per_page, total_pages, count):
    SmartPage = collections.namedtuple(
        'SmartPage',
        ['page', 'per_page', 'total_pages', 'count']
    )
    return SmartPage(page, per_page, total_pages, count)
