from dataclasses import asdict
from http.client import BAD_REQUEST

from config.celery import celery
from config.constants import PROCESS_REPORT_TASK
from controller.api.report_dto import ReportDto
from domain.common.dependencies import DatabaseSession
from domain.report.models import Report, ReportStatus, ReportType
from domain.service.database import AbstractDatabaseService
from domain.service.filter import <PERSON><PERSON><PERSON><PERSON><PERSON>ver
from injector import inject
from reports.constants import KONQUEST_USER_CONSUMPTIONS_EXPORT
from tasks.report import get_report_raw_data
from utils import database_transaction as transaction


class ReportServiceError(Exception):
    def __init__(self, messages, status_code=500, status_error=None):
        super().__init__(messages)
        self.message = messages
        self.status_code = status_code
        self.status_error = status_error


class ReportService(AbstractDatabaseService):
    @inject
    def __init__(self, database: DatabaseSession, ):
        super().__init__(database, Report)

    def _add(self, report: Report) -> Report:
        with transaction(self.database):
            self.database.add(report)
            return report

    def load_report(self, report_id: str, workspace_id: str) -> Report:
        report = self.database.query(self.model_type).filter(
            Report.id == report_id, Report.workspace_id == workspace_id
        ).first()
        if not report:
            raise ReportServiceError(f'{self.model_type.__name__} not found', 404, "Not Found")
        return report

    def _there_is_another_one_in_process(self, report: Report) -> bool:
        return self.database.query(Report).filter(
            Report.object_id == report.object_id,
            Report.report_type_id == report.report_type_id,
            Report.status.in_([ReportStatus.queued, ReportStatus.processing]),
            Report.user_creator_id == report.user_creator_id,
            Report.workspace_id == report.workspace_id
        ).first()

    def _load_report_type(self, type_name: str) -> ReportType:
        return self.database.query(ReportType).filter(ReportType.name == type_name).first()

    def get_report_raw(self, report_dto: ReportDto):
        report_type = self._load_report_type(report_dto.type_name)
        if report_dto.type_name != KONQUEST_USER_CONSUMPTIONS_EXPORT:
            raise ReportServiceError("report_type_not_implemented", BAD_REQUEST, "Bad Request")
        report = Report()
        report.report_type_id = report_type.id
        report.report_type = report_type
        report.workspace_id = report_dto.workspace_id
        report.language = report_dto.language
        report.filters = self._clean_filters(report_dto.filters) if report_dto.filters else {}
        report.time_zone = report_dto.time_zone
        return get_report_raw_data(report)

    def add_report(self, report_dto: ReportDto) -> dict:
        report_type = self._load_report_type(report_dto.type_name)
        if not report_type:
            raise ReportServiceError("report_type_not_found", 400, "Not Found")

        report = Report()
        report.report_type_id = report_type.id
        report.user_creator = asdict(report_dto.request_user)
        report.user_creator_name = report_dto.request_user.name
        report.user_creator_id = report_dto.request_user.id
        report.workspace_id = report_dto.workspace_id
        report.language = report_dto.language
        report.filters = self._clean_filters(report_dto.filters) if report_dto.filters else {}
        report.object_id = report_dto.object_id
        report.file_format = "PDF" if report_type.model == "PRESENTATION" else report_dto.file_format
        report.time_zone = report_dto.time_zone
        report_duplicated = self._there_is_another_one_in_process(report)

        if report_duplicated:
            return {"status": "in_process", "report": report_duplicated}

        report = self._add(report)
        celery.send_task(PROCESS_REPORT_TASK, args=(report.id,))
        return {"status": "process_started", "report": report}

    @staticmethod
    def _clean_filters(filters: dict) -> dict:
        only_keys_with_values = {key: value for key, value in filters.items() if value is not None}
        return only_keys_with_values

    def load_filters(self, filters, client_id):
        query = self.database.query(Report).join(ReportType)
        query = query.filter(Report.workspace_id == client_id)
        return FilterResolver(Report).exec_query_filters(query, filters)


class ReportTypeService(AbstractDatabaseService):
    @inject
    def __init__(self, database: DatabaseSession):
        super().__init__(database, ReportType)

    @staticmethod
    def _clean_filters(filters: dict) -> dict:
        only_keys_with_values = {key: value for key, value in filters.items() if value}
        return only_keys_with_values

    def load_filters(self, filters):
        query = self.database.query(ReportType)
        return FilterResolver(ReportType).exec_query_filters(query, filters)
