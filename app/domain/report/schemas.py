from datetime import timedelta

import marshmallow
from config.default import Config
from dateutil.parser import parse
from dateutil.utils import today
from marshmallow import fields
from marshmallow_sqlalchemy import SQLAlchemyAutoSchema
from marshmallow_sqlalchemy.fields import Nested

from . import models


class ReportTypeSchema(SQLAlchemyAutoSchema):
    class Meta:
        include_fk = True
        transient = True
        load_instance = True
        model = models.ReportType


class ReportSchema(SQLAlchemyAutoSchema):
    class Meta:
        include_fk = True
        transient = True
        load_instance = True
        model = models.Report
        days_left = fields.Method("get_days_left")
        exclude = ("report_type_id", "workspace_id")

    report_type = Nested(ReportTypeSchema, many=False, dump_only=True)

    @marshmallow.post_dump()
    def _prepare_dump(self, data, many, **kwargs):
        created = parse(data["created"])
        limit_date = created + timedelta(days=Config.REPORT_LIFE_TIME_DAY)
        data['days_left'] = (limit_date - today()).days
        return data
