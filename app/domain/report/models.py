import uuid
from datetime import datetime

from pytz import timezone as get_timezone
from pytz.exceptions import UnknownTimeZoneError
from sqlalchemy import <PERSON>SO<PERSON>, Column, DateTime, ForeignKey, Integer, String
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, validates

Base = declarative_base()


class Timestamp(object):
    created = Column(DateTime, default=lambda: datetime.utcnow(), nullable=True)
    updated = Column(DateTime, default=lambda: datetime.utcnow(), nullable=True)


class ReportStatus:
    queued = 'QUEUED'
    processing = 'PROCESSING'
    done = 'DONE'
    error = 'ERROR'

    _all = [queued, processing, done, error]

    @classmethod
    def is_valid(cls, status):
        return status in cls._all


class Application:
    konquest = 'KONQUEST'
    smartzap = 'SMARTZAP'
    gameup = 'GAMEUP'
    myaccount = 'MY_ACCOUNT'

    _all = [konquest, gameup, smartzap, myaccount]

    @classmethod
    def is_valid(cls, application):
        return application in cls._all


class ReportFormat:
    xlsx = 'XLSX'
    csv = 'CSV'
    pdf = 'PDF'

    _all = [xlsx, csv, pdf]

    @classmethod
    def is_valid(cls, report_format):
        return report_format in cls._all


class ReportModel:
    presentation = 'PRESENTATION'
    export = 'EXPORT'

    _all = [presentation, export]

    @classmethod
    def is_valid(cls, model):
        return model in cls._all


class ReportType(Base, Timestamp):
    __tablename__ = 'report_type'

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String, nullable=False, unique=True)
    description = Column(String, nullable=False)
    application = Column(String, nullable=False)
    model = Column(String, nullable=False)

    @validates('application')
    def validate_application(self, _, application):
        assert Application.is_valid(application), 'Invalid Report Type application.'
        return application

    @validates('model')
    def validate_model(self, _, model):
        assert ReportModel.is_valid(model), 'Invalid Report Type model.'
        return model


class Report(Base, Timestamp):
    __tablename__ = 'report'

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    report_type_id = Column(String(36), ForeignKey('report_type.id'), nullable=False)
    processing_time = Column(Integer)
    url = Column(String)
    status = Column(String, default=ReportStatus.queued)
    filters = Column(JSON)
    language = Column(String)
    user_creator = Column(JSON, nullable=False)
    user_creator_name = Column(String)
    user_creator_id = Column(String, nullable=False)
    file_format = Column(String)
    time_zone = Column(String, default=get_timezone('America/Sao_Paulo').zone)
    object_id = Column(String(36))

    workspace_id = Column(String(36), nullable=False)
    report_type = relationship(
        'ReportType', primaryjoin='ReportType.id==Report.report_type_id', remote_side='ReportType.id', uselist=False
    )

    @validates('status')
    def validate_status(self, _, status):
        assert ReportStatus.is_valid(status), 'Invalid Report status.'
        return status

    @validates('time_zone')
    def validate_time_zone(self, _, time_zone):
        is_valid = True
        try:
            get_timezone(time_zone)
        except UnknownTimeZoneError:
            is_valid = False
        assert is_valid
        return time_zone

    @validates('file_format')
    def validate_file_format(self, _, file_format):
        assert ReportFormat.is_valid(file_format), 'Invalid Report Type file format.'
        return file_format
