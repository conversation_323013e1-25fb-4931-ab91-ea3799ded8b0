from config.default import Config
from elasticsearch_dsl import Boolean, Document, Object, Text


class Notification(Document):
    id = Text()
    message = Text(analyzer='snowball')
    read = Boolean()

    user_receiving = Object()
    company_id = Text()
    notification_action = Text()
    url = Text()
    image = Text()
    object_id = Text()
    object_type = Text()

    def save(self, ** kwargs):
        return super(Notification, self).save(** kwargs)

    class Index:
        name = Config.ELASTICSEARCH_INDEX_NOTIFICATIONS
