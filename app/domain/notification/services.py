import uuid

from config.default import Config
from domain.common.dependencies import ElasticsearchConnection, Logger
from domain.notification.mapping import notification_mapping
from domain.notification.models import Notification
from elasticsearch import NotFoundError
from elasticsearch_dsl import Q, Search
from injector import inject


class NotificationServiceError(Exception):
    """
    Exception class for errors while executing notification services.
    """
    status_code = 500

    def __init__(self, messages, status_code=None, status_error=None):
        Exception.__init__(self)
        self.message = messages
        self.status_code = status_code
        self.status_error = status_error


class NotificationService:
    @inject
    def __init__(
        self,
        logger: Logger,
        es: ElasticsearchConnection
    ):
        self.logger = logger
        self.es = es

    def get_user_notifications(self, user_id, workspace_id):
        s = Search(using=self.es, index=Config.ELASTICSEARCH_INDEX_NOTIFICATIONS)

        q = Q('match', user_receiving__id=str(user_id))
        s = s.filter('nested', path='user_receiving', query=q)
        s = s.filter(Q('term', company_id=workspace_id) & Q('term', read=False))

        response = s.execute().to_dict()
        return dict(total=response['hits']['total']['value'],
                    data=response['hits']['hits'])

    def save_notification_report(self, user: dict, workspace_id: str, report_url: str, message: str):
        index = Config.ELASTICSEARCH_INDEX_NOTIFICATIONS
        _user = {
            'id': str(user.get('id')),
            'name': user.get('name'),
            'email': user.get('email'),
        }

        if not self.es.indices.exists(index=index):
            self.es.indices.create(index=index, body=notification_mapping)

        notification_id = str(uuid.uuid4())
        notification = Notification(_id=notification_id,
                                    user_receiving=_user,
                                    notification_action='DOWNLOAD',
                                    message=message,
                                    company_id=str(workspace_id),
                                    url=report_url,
                                    object_type='REPORT',
                                    read=False,
                                    image=Config.NOTIFICATION_REPORT_IMAGE)
        notification.save()

    @staticmethod
    def read_notification(notification_id):
        notification = Notification.get(id=notification_id, ignore=[404])
        Notification.search().execute()
        if not notification:
            raise NotFoundError('Notification not Found', 'notification_not_found')
        notification.read = True
        notification.save()
        return notification.to_dict()
