notification_mapping = {
    'mappings': {
        'properties': {
            'id': {
                'type': 'keyword'
            },
            'message': {
                'type': 'text'
            },
            'read': {
                'type': 'boolean'
            },
            'image': {
                'type': 'keyword'
            },
            'url': {
                'type': 'keyword'
            },
            'notification_action': {
                'type': 'keyword'
            },
            'object_id': {
                'type': 'keyword'
            },
            'object_type': {
                'type': 'keyword'
            },
            'company_id': {
                'type': 'keyword'
            },
            'user_receiving': {
                'type': 'nested',
                'properties': {
                    'id': {'type': 'keyword'},
                    'name': {'type': 'text'},
                    'email': {'type': 'keyword'},
                }
            }
        }
    }
}
