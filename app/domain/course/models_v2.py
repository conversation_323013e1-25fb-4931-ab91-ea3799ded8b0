from domain.common.utils import normalize_end_date, normalize_start_date
from domain.elasticsearch.search_factory import SimpleFilter


class CourseFilter(SimpleFilter):
    workspace_id: str

    def __init__(self, workspace_id=None):
        self.workspace_id = workspace_id

    def __str__(self):
        return '<%s(%s)>' % (self.__class__.__name__, ', '.join([f'{k}={v}' for (k, v) in self.__dict__.items()]))


class CoursesStatsFilter(CourseFilter):
    def __init__(self, workspace_id=None, start_date=None, end_date=None, interval='month', data_size=0):
        super().__init__(workspace_id)

        self.start_date = start_date
        self.end_date = end_date
        self.interval = interval
        self.data_size = data_size

    def get_start_date(self) -> str:
        return normalize_start_date(self.start_date)

    def get_end_date(self) -> str:
        return normalize_end_date(self.end_date)


class CoursesListingFilter(CourseFilter):
    def __init__(self, workspace_id=None, search_term=None, page=0, page_size=10, sort=None):
        super().__init__(workspace_id)

        self.search_term = search_term
        self.page = page
        self.page_size = page_size
        self.sort = sort


class CourseEnrollmentsListingFilter(CoursesListingFilter):
    def __init__(self, workspace_id=None, search_term=None, start_date=None, end_date=None, page=0, page_size=10, sort=None):
        super().__init__(workspace_id, search_term, page, page_size, sort)

        self.start_date = start_date
        self.end_date = end_date

    def get_start_date(self) -> str:
        return normalize_start_date(self.start_date)

    def get_end_date(self) -> str:
        return normalize_end_date(self.end_date)


class CourseContentsListingFilter(CoursesListingFilter):
    def __init__(self, workspace_id=None, search_term=None, type=None, page=0, page_size=10, sort=None):
        super().__init__(workspace_id, search_term, page, page_size, sort)

        self.type = type
