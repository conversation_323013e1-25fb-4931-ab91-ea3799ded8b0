# Deprecated. Mapping on the Indexer Project.
pt_BR = {
    "settings": {
        "analysis": {
            "analyzer": {
                "default": {
                    "type": "brazilian"
                },
                "default_search": {
                    "type": "brazilian"
                }
            }
        }
    },
}

course_mapping = {
    'settings': {
        "analysis": {
            "normalizer": {
                "case_insensitive": {
                    "filter": ["lowercase"]
                }
            }
        }
    },
    'mappings': {
        'properties': {
            'id': {
                'type': 'keyword'
            },
            'origin': {
                'type': 'keyword'
            },
            'course_id': {
                'type': 'keyword'
            },
            'name': {
                'type': 'text',
                'copy_to': 'search_terms',
                'fields': {
                    "keyword": {'type': 'keyword'},
                    "sortable": {
                        'type': 'keyword',
                        'normalizer': 'case_insensitive'
                    },
                }
            },
            'description': {
                'type': 'text',
                'copy_to': 'search_terms'
            },
            'duration_time': {
                'type': 'float'
            },
            'points': {
                'type': 'integer'
            },
            'created_date': {
                'type': 'date'
            },
            'updated_date': {
                'type': 'date'
            },
            'is_active': {
                'type': 'boolean'
            },
            'development_status': {
                'type': 'keyword'
            },
            'model_type': {
                'type': 'keyword'
            },
            "search_terms": {
                "type": "text"
            },
            'owner_companies': {
                'type': 'nested',
                'properties': {
                    'id': {'type': 'keyword'},
                    'created_date': {'type': 'date'},
                    'updated_date': {'type': 'date'},
                    'company_id': {'type': 'keyword'},
                    'relationship_type': {'type': 'keyword'},
                }
            },
            'user': {
                'type': 'object',
                'properties': {
                    'id': {'type': 'keyword'},
                    'user_name': {
                        'type': 'text',
                        'copy_to': 'user.search_terms',
                        'fields': {
                            "keyword": {'type': 'keyword'},
                            "sortable": {
                                'type': 'keyword',
                                'normalizer': 'case_insensitive'
                            },
                        }
                    },
                    'status': {'type': 'keyword'},
                    'email': {'type': 'keyword'},
                    'avatar': {'type': 'keyword'},
                    'last_access_date': {'type': 'date'},
                    'search_terms': {'type': 'text'},
                }
            },
            'course_type': {
                'type': 'object',
                'properties': {
                    'id': {'type': 'keyword'},
                    'name': {
                        'type': 'text',
                        'fields': {
                            "keyword": {'type': 'keyword'}
                        }
                    },
                }
            },
            'course_category': {
                'type': 'object',
                'properties': {
                    'id': {'type': 'keyword'},
                    'name': {
                        'type': 'text',
                        'copy_to': 'search_terms',
                        'fields': {
                            "keyword": {'type': 'keyword'},
                            "sortable": {
                                'type': 'keyword',
                                'normalizer': 'case_insensitive'
                            },
                        }
                    },
                }
            },
            'enrollment': {
                'type': 'object',
                'properties': {
                    'id': {'type': 'keyword'},
                    'points': {'type': 'integer'},
                    'start_date': {'type': 'date'},
                    'end_date': {'type': 'date'},
                    'goal_date': {'type': 'date'},
                    'give_up': {'type': 'boolean'},
                    'give_up_comment': {'type': 'text'},
                    'status': {'type': 'keyword'},
                    'performance': {'type': 'float'},
                }
            },
            'goal': {
                'type': 'object',
                'properties': {
                    'id': {'type': 'keyword'},
                    'goal_type_id': {'type': 'keyword'},
                }
            },
            'rating': {
                'type': 'object',
                'properties': {
                    'id': {'type': 'keyword'},
                    'rating': {'type': 'integer'},
                }
            },
            'bookmark': {
                'type': 'object',
                'properties': {
                    'id': {'type': 'keyword'},
                }
            },
            'course_activity': {
                'type': 'object',
                'properties': {
                    'id': {'type': 'keyword'},
                    'action': {'type': 'text'},
                    'start_date': {'type': 'date'},
                    'stop_date': {'type': 'date'},
                    'mission_stage_content_id': {'type': 'keyword'},
                    'time_in': {'type': 'float'},
                }
            },
            'answer': {
                'type': 'object',
                'properties': {
                    'id': {'type': 'keyword'},
                    'answer': {
                        'type': 'object',
                        'properties': {
                            'id': {'type': 'keyword'},
                            'hit': {'type': 'boolean'},
                        }
                    },
                }
            },
            'course_stats': {
                'type': 'object',
                'properties': {
                    'updated_date': {'type': 'date'},
                    'enrollment': {
                        'type': 'object',
                        'properties': {
                            'total': {'type': 'integer'},
                            'started': {'type': 'integer'},
                            'completed': {'type': 'integer'},
                            'give_up': {'type': 'integer'},
                            'completed_ratio': {'type': 'float'},
                        }
                    },
                    'rating': {
                        'type': 'object',
                        'properties': {
                            'average': {'type': 'float'},
                            'total': {'type': 'integer'},
                        }
                    },
                    'content_types': {
                        'type': 'nested',
                        'properties': {
                            'id': {'type': 'keyword'},
                            'name': {'type': 'keyword'},
                            'total': {'type': 'integer'},
                        }
                    },
                    'answers': {
                        'type': 'object',
                        'properties': {
                            'total_exams': {'type': 'integer'},
                            'total_questions': {'type': 'integer'},
                            'total_answers': {'type': 'integer'},
                            'correct_answers': {'type': 'integer'},
                            'correct_ratio': {'type': 'float'},
                        }
                    },
                    'general': {
                        'type': 'object',
                        'properties': {
                            'total_activity_seconds': {'type': 'float'},
                            'total_comments': {'type': 'integer'},
                            'total_ratings': {'type': 'integer'},
                            'total_evaluations': {'type': 'integer'},
                            'total_feedbacks': {'type': 'integer'},
                        }
                    },
                    'nps': {
                        'type': 'object',
                        'properties': {
                            'total': {'type': 'integer'},
                            'cons': {'type': 'integer'},
                            'neutrals': {'type': 'integer'},
                            'pros': {'type': 'integer'},
                        }
                    },
                }
            },
            'course_contents': {
                'type': 'nested',
                'properties': {
                    'stage_id': {'type': 'keyword'},
                    'stage_order': {'type': 'integer'},
                    'stage_name': {'type': 'keyword'},

                    'content_id': {'type': 'keyword'},
                    'content_order': {'type': 'integer'},
                    'content_name': {
                        'type': 'text',
                        'copy_to': 'course_contents.search_terms',
                        'fields': {
                            "keyword": {'type': 'keyword'},
                            "sortable": {
                                'type': 'keyword',
                                'normalizer': 'case_insensitive'
                            },
                        }
                    },

                    'kontent_id': {'type': 'keyword'},
                    'content_type_id': {'type': 'keyword'},
                    'content_type_name': {'type': 'keyword'},

                    "search_terms": {"type": "text"},

                    'activity_stats': {
                        'type': 'object',
                        'properties': {
                            'updated_date': {'type': 'date'},
                            'total_seconds': {'type': 'integer'},
                            'total_users': {'type': 'integer'},
                            'total_views': {'type': 'integer'},
                        }
                    }
                }
            }
        }
    }
}
