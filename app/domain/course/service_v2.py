from domain.common.dependencies import Logger
from domain.course.models_v2 import (
    CourseContentsListingFilter,
    CourseEnrollmentsListingFilter,
    CourseFilter,
    CoursesListingFilter,
    CoursesStatsFilter,
)
from domain.elasticsearch.search_factory import ElasticSearchFactory
from elasticsearch_dsl import Q
from injector import inject


class CourseServiceV2:
    @inject
    def __init__(
        self,
        logger: Logger,
        esf: ElasticSearchFactory
    ):
        self.logger = logger
        self.esf = esf

    def get_course_data(self, course_id, filters: CourseFilter) -> dict:
        print(f'[Course::V2] get_course_data - course_id: {course_id} -  filter: {filters}')
        search = self.esf.make_search_courses(filters, course_id=course_id)

        search.aggs.bucket('course_contents', 'nested', path='course_contents')\
            .bucket('types', 'terms', field="course_contents.content_type_id", size=100)\
            .bucket('name', 'terms', field="course_contents.content_type_name")

        response = search.execute().to_dict()

        value = {
            'data': response['hits']['hits'],
            'aggs': response['aggregations'],
            'stats': {
                'enrollments': {
                    'total': 0,
                    'started': 0,
                    'completed': 0,
                    'give_up': 0,
                    'completed_ratio': 0,
                },
                'ratings': {
                    'total': 0,
                    'average': 0,
                },
                'answers': {
                    'total_exams': 0,
                    'total_questions': 0,
                    'total_answers': 0,
                    'correct_answers': 0,
                    'correct_ratio': 0,
                },
                'activities': {
                    'total_activity_seconds': 0,
                },
                'nps': {
                    'total': 0,
                    'cons': 0,
                    'neutrals': 0,
                    'pros': 0,
                }
            },
        }

        if not value['data']:
            return value

        # Enrollments Stats
        filter = self.esf.make_simple_filter(workspace_id=filters.workspace_id, data_size=0)
        search = self.esf.make_search_enrollments(filter)
        search = search.filter('term', course_id=course_id)
        search.aggs.metric('total', 'value_count', field='id')\
            .bucket('filtered', 'filters', filters={
                'started': Q('term', status='STARTED'),
                'completed': Q('term', status='COMPLETED'),
                'give_up': Q('term', give_up=True),
            })

        response = search.execute().to_dict()
        enrollment_total = response['aggregations']['total']['value']
        enrollment_started = response['aggregations']['filtered']['buckets']['started']['doc_count']
        enrollment_completed = response['aggregations']['filtered']['buckets']['completed']['doc_count']
        enrollment_give_up = response['aggregations']['filtered']['buckets']['give_up']['doc_count']
        enrollment_completed_ratio = enrollment_completed / enrollment_total if enrollment_total else 0

        value['stats']['enrollments'] = {
            'total': enrollment_total,
            'started': enrollment_started,
            'completed': enrollment_completed,
            'give_up': enrollment_give_up,
            'completed_ratio': enrollment_completed_ratio,
        }

        # Rating Stats
        filter = self.esf.make_simple_filter(workspace_id=filters.workspace_id, data_size=0)
        search = self.esf.make_search_ratings(filter)
        search = search.filter('term', course_id=course_id)
        search.aggs.metric('total', 'value_count', field='id')\
            .metric('average', 'avg', field='rating')

        response = search.execute().to_dict()
        rating_total = response['aggregations']['total']['value']
        rating_average = response['aggregations']['average']['value']

        value['stats']['ratings'] = {
            'total': rating_total,
            'average': rating_average,
        }

        # Answers stats
        filter = self.esf.make_simple_filter(workspace_id=filters.workspace_id, data_size=0)
        search = self.esf.make_search_answers(filter)
        search = search.filter('term', course_id=course_id)
        search.aggs.metric('total_answers', 'value_count', field='id')\
            .metric('total_questions', 'cardinality', field='question_id')\
            .metric('total_exams', 'cardinality', field='exam_id')\
            .bucket('correct_answers', 'filter', filter=Q('term', is_ok=True))

        response = search.execute().to_dict()
        answer_total_answers = response['aggregations']['total_answers']['value']
        answer_total_questions = response['aggregations']['total_questions']['value']
        answer_total_exams = response['aggregations']['total_exams']['value']
        answer_correct_answers = response['aggregations']['correct_answers']['doc_count']
        answer_correct_ratio = answer_correct_answers / answer_total_answers if answer_total_answers else 0

        value['stats']['answers'] = {
            'total_exams': answer_total_exams,
            'total_questions': answer_total_questions,
            'total_answers': answer_total_answers,
            'correct_answers': answer_correct_answers,
            'correct_ratio': answer_correct_ratio,
        }

        # Activities stats
        filter = self.esf.make_simple_filter(workspace_id=filters.workspace_id, data_size=0)
        search = self.esf.make_search_activities(filter)
        search = search.filter('term', course_id=course_id)
        search.aggs.metric('total_activity_seconds', 'sum', field='time_in')

        response = search.execute().to_dict()
        activity_total_seconds = response['aggregations']['total_activity_seconds']['value']

        value['stats']['activities'] = {
            'total_activity_seconds': activity_total_seconds,
        }

        # Evaluations stats
        filter = self.esf.make_simple_filter(data_size=0)
        search = self.esf.make_search_evaluations(filter)
        search = search.filter('term', course_id=course_id)
        search.aggs.metric('total', 'value_count', field='id')\
            .bucket('ranges', 'range', field='nps', ranges=[
                {'key': 'cons', 'to': 6},
                {'key': 'neutrals', 'from': 6, 'to': 8},
                {'key': 'pros', 'from': 8},
            ])

        response = search.execute().to_dict()
        nps_total = response['aggregations']['total']['value']
        nps_cons = response['aggregations']['ranges']['buckets'][0]['doc_count']
        nps_neutrals = response['aggregations']['ranges']['buckets'][1]['doc_count']
        nps_pros = response['aggregations']['ranges']['buckets'][2]['doc_count']

        value['stats']['nps'] = {
            'total': nps_total,
            'cons': nps_cons,
            'neutrals': nps_neutrals,
            'pros': nps_pros,
        }

        return value

    def get_course_enrollments(self, course_id, filters: CourseEnrollmentsListingFilter) -> dict:
        print(f'[Course::V2] get_course_enrollments - course_id: {course_id} -  filter: {filters}')
        search = self.esf.make_search_enrollments(filters, range_field='start_date')

        search = search.filter('term', course_id=course_id)

        if filters.search_term:
            query = self.esf.make_query_search_as_you_type(search_field="search_terms", search_term=filters.search_term)
            search = search.query(query)

        idx_start = filters.page * filters.page_size
        idx_end = idx_start + filters.page_size
        search = search[idx_start:idx_end]

        if filters.sort:
            search = search.sort(filters.sort)

        search.aggs.metric('total', 'value_count', field='id')

        response = search.execute().to_dict()
        return {
            'total': response['aggregations']['total']['value'],
            'data': response['hits']['hits']
        }

    def get_course_contents(self, course_id, filters: CourseContentsListingFilter) -> dict:
        print(f'[Course::V2] get_course_contents - course_id: {course_id} -  filter: {filters}')

        search = self.esf.make_search_courses(filters, course_id=course_id)

        inner_options = {
            'from': filters.page_size * filters.page,
            'size': filters.page_size
        }

        if filters.sort:
            desc = False
            sort = filters.sort
            if sort[0] == '-':
                desc = True
                sort = sort[1:]
            sort = 'course_contents.' + sort
            inner_options['sort'] = {sort: 'desc' if desc else 'asc'}

        query = Q('exists', field='course_contents.content_id')  # Everything

        if filters.search_term:
            query = query & self.esf.make_query_search_as_you_type(
                search_field="course_contents.search_terms",
                search_term=filters.search_term)

        if filters.type:
            query = query & Q('match', course_contents__content_type_name=filters.type)

        search = search.query('nested', path='course_contents', inner_hits=inner_options, query=query)

        response = search.execute().to_dict()

        if not response['hits']['hits']:
            return {'total': 0, 'data': []}

        inner_hits = response['hits']['hits'][0]['inner_hits']['course_contents']
        total = inner_hits['hits']['total']['value']
        data = inner_hits['hits']['hits']
        content_ids = [content['_source']['content_id'] for content in data]

        # Activity stats
        filter = self.esf.make_simple_filter(workspace_id=filters.workspace_id, data_size=0)
        search = self.esf.make_search_activities(filter)

        search = search.filter('term', course_id=course_id)\
            .filter('terms', stage_content_id=content_ids)

        search.aggs.bucket('per_content', 'terms', field='stage_content_id')\
            .metric('total_views', 'value_count', field="id")\
            .metric('total_seconds', 'sum', field="time_in")\
            .metric('total_users', 'cardinality', field="user_id")

        response = search.execute().to_dict()
        activity_buckets = response['aggregations']['per_content']['buckets']

        # Enrollment Stats
        filter = self.esf.make_simple_filter(workspace_id=filters.workspace_id, data_size=0)
        search = self.esf.make_search_enrollments(filter)

        search = search.filter('term', course_id=course_id)

        search.aggs.metric('total', 'value_count', field='id')

        response = search.execute().to_dict()
        enrollments_total = response['aggregations']['total']['value']

        for content in data:
            content_id = content['_source']['content_id']
            activities_stats = next((bucket for bucket in activity_buckets if bucket['key'] == content_id), {
                'total_views': {'value': 0},
                'total_seconds': {'value': 0},
                'total_users': {'value': 0},
            })
            content['stats'] = {
                'activities': activities_stats,
                'enrollments': {
                    'total': enrollments_total,
                    'engagement_rate': activities_stats['total_users']['value'] / enrollments_total if enrollments_total else 0
                }
            }

        return {
            'total': total,
            'data': data
        }

    def get_total_courses(self, filters: CoursesStatsFilter) -> dict:
        print(f'[Course::V2] get_total_courses -  filter: {filters}')
        s = self.esf.make_search_courses(filters, range_field='created_date')

        s.aggs\
            .metric('total', 'value_count', field='id')\
            .bucket('agg_courses', 'date_histogram', field='created_date', calendar_interval=filters.interval)
        response = s.execute().to_dict()

        return {
            'data': response['hits']['hits'],
            'total': response['aggregations']['total']['value'],
            'aggs': response['aggregations']['agg_courses']
        }

    def get_total_courses_started(self, filters: CoursesStatsFilter) -> dict:
        print(f'[Course::V2] get_total_courses_started -  filter: {filters}')

        s = self.esf.make_search_enrollments(filters, range_field='start_date')
        s = s.filter('term', status='STARTED')

        s.aggs\
            .metric('total', 'value_count', field='id')\
            .bucket('agg_courses', 'date_histogram', field='start_date', calendar_interval=filters.interval)
        response = s.execute().to_dict()

        return {
            'data': response['hits']['hits'],
            'total': response['aggregations']['total']['value'],
            'aggs': response['aggregations']['agg_courses']
        }

    def get_total_courses_completed(self, filters: CoursesStatsFilter) -> dict:
        print(f'[Course::V2] get_total_courses_completed -  filter: {filters}')

        s = self.esf.make_search_enrollments(filters, range_field='end_date')
        s = s.filter('term', status='COMPLETED')

        s.aggs\
            .metric('total', 'value_count', field='id')\
            .bucket('agg_courses', 'date_histogram', field='end_date', calendar_interval=filters.interval)
        response = s.execute().to_dict()

        return {
            'data': response['hits']['hits'],
            'total': response['aggregations']['total']['value'],
            'aggs': response['aggregations']['agg_courses']
        }

    def get_total_courses_new_enrollment(self, filters: CoursesStatsFilter) -> dict:
        print(f'[Course::V2] get_total_courses_new_enrollment -  filter: {filters}')

        s = self.esf.make_search_enrollments(filters, range_field='created_date')

        s.aggs\
            .metric('total', 'value_count', field='id')\
            .bucket('agg_courses', 'date_histogram', field='created_date', calendar_interval=filters.interval)
        response = s.execute().to_dict()

        return {
            'data': response['hits']['hits'],
            'total': response['aggregations']['total']['value'],
            'aggs': response['aggregations']['agg_courses']
        }

    def get_ratio_courses_completed(self, filters: CoursesStatsFilter) -> dict:
        print(f'[Course::V2] get_ratio_courses_completed -  filter: {filters}')

        filters.data_size = 0
        s = self.esf.make_search_enrollments(filters, 'created_date')
        s.aggs.metric('total', 'value_count', field='id')\
            .bucket('status', 'filters', filters={
                'started': Q('term', status='STARTED'),
                'completed': Q('term', status='COMPLETED'),
            })

        response = s.execute().to_dict()
        total = response['aggregations']['total']['value']
        completed = response['aggregations']['status']['buckets']['completed']['doc_count']
        # started = response['aggregations']['status']['buckets']['started']['doc_count']

        return {
            'data': response['hits']['hits'],
            'total': total,
            'stats': {
                'completed_ratio': completed / total if total else 0
            }
        }

    def get_total_courses_content_hours_available(self, filters: CoursesStatsFilter) -> dict:
        print(f'[Course::V2] get_total_courses_content_hours_available -  filter: {filters}')

        s = self.esf.make_search_courses(filters, 'created_date')

        s.aggs\
            .metric('total', 'value_count', field='id')\
            .metric('available_time', 'sum', field='duration_time')
        response = s.execute().to_dict()

        return {
            'data': response['hits']['hits'],
            'total': response['aggregations']['total']['value'],
            'stats': {
                'total_available_seconds': response['aggregations']['available_time']['value']
            }
        }

    def get_total_courses_content_hours_consumed(self, filters: CoursesStatsFilter) -> dict:
        print(f'[Course::V2] get_total_courses_content_hours_consumed -  filter: {filters}')

        s = self.esf.make_search_activities(filters, 'created_date')

        s = s.filter('exists', field='course_id')

        s.aggs\
            .metric('total', 'value_count', field='id')\
            .metric('consumed_time', 'sum', field='time_in')
        response = s.execute().to_dict()

        return {
            'data': response['hits']['hits'],
            'total': response['aggregations']['total']['value'],
            'stats': {
                'total_consumed_seconds': response['aggregations']['consumed_time']['value']
            }
        }

    def get_courses_ratings(self, filters: CoursesStatsFilter) -> dict:
        print(f'[Course::V2] get_courses_ratings -  filter: {filters}')

        s = self.esf.make_search_ratings(filters, range_field='created_date')
        s.aggs\
            .metric('total', 'value_count', field='id')\
            .metric('rating', 'avg', field='rating')

        response = s.execute().to_dict()
        return {
            'data': response['hits']['hits'],
            'total': response['aggregations']['total']['value'],
            'aggs': response['aggregations']
        }

    def get_courses_types(self, filters: CoursesStatsFilter) -> dict:
        print(f'[Course::V2] get_courses_types -  filter: {filters}')

        s = self.esf.make_search_courses(filters, range_field='created_date')
        s.aggs\
            .metric('total', 'value_count', field='id')\
            .bucket('types', 'terms', field='course_type.id', size=100)\
            .bucket('name', 'terms', field="course_type.name.keyword")

        response = s.execute().to_dict()
        return {
            'data': response['hits']['hits'],
            'total': response['aggregations']['total']['value'],
            'aggs': response['aggregations']
        }

    def get_courses_categories(self, filters: CoursesStatsFilter) -> dict:
        print(f'[Course::V2] get_courses_categories -  filter: {filters}')

        s = self.esf.make_search_courses(filters, range_field='created_date')
        s.aggs\
            .metric('total', 'value_count', field='id')\
            .bucket('categories', 'terms', field='course_category.id', size=100)\
            .bucket('name', 'terms', field="course_category.name.keyword")

        response = s.execute().to_dict()
        return {
            'data': response['hits']['hits'],
            'total': response['aggregations']['total']['value'],
            'aggs': response['aggregations']
        }

    def get_courses_content_types(self, filters: CoursesStatsFilter) -> dict:
        print(f'[Course::V2] get_courses_content_types -  filter: {filters}')

        s = self.esf.make_search_courses(filters, range_field='created_date')
        s.aggs\
            .metric('total', 'value_count', field='id')\
            .bucket('course_contents', 'nested', path='course_contents')\
            .bucket('types', 'terms', field="course_contents.content_type_id", size=100)\
            .bucket('name', 'terms', field="course_contents.content_type_name")

        response = s.execute().to_dict()
        return {
            'data': response['hits']['hits'],
            'total': response['aggregations']['total']['value'],
            'aggs': response['aggregations']
        }

    def get_courses_list(self, filters: CoursesListingFilter) -> dict:
        print(f'[Course::V2] get_courses_list - filter: {filters}')

        search = self.esf.make_search_courses(filters)

        # Select fields
        search = search.source(excludes=['course_contents', 'owner_companies'])

        if filters.search_term:
            query = self.esf.make_query_search_as_you_type(search_field="search_terms", search_term=filters.search_term)
            search = search.query(query)

        idx_start = filters.page * filters.page_size
        idx_end = idx_start + filters.page_size
        search = search[idx_start:idx_end]

        if filters.sort:
            search = search.sort(filters.sort)

        search.aggs.metric('total', 'value_count', field='id')

        response = search.execute().to_dict()

        total = response['aggregations']['total']['value'],
        data = response['hits']['hits']
        course_ids = [course['_id'] for course in data]

        # Init stats
        for course in data:
            course['stats'] = {
                'enrollments': {
                    'total': 0,
                    'started': 0,
                    'completed': 0,
                    'give_up': 0,
                    'completed_ratio': 0,
                },
                'ratings': {
                    'total': 0,
                    'average': 0,
                }
            }

        # Enrollments Stats
        filter = self.esf.make_simple_filter(workspace_id=filters.workspace_id, data_size=0)
        search = self.esf.make_search_enrollments(filter)
        search = search.filter('terms', course_id=course_ids)
        search.aggs.bucket('per_course', 'terms', field='course_id', size=100)\
        .metric('total', 'value_count', field='id')\
        .bucket('filtered', 'filters', filters={
                'started': Q('term', status='STARTED'),
                'completed': Q('term', status='COMPLETED'),
                'give_up': Q('term', give_up=True),
            })

        response = search.execute().to_dict()

        for course in data:
            aggs = next((stat for stat in response['aggregations']['per_course']['buckets'] if stat['key'] == course['_id']), {})
            if not aggs:
                continue
            enrollment_total = aggs['total']['value']
            enrollment_started = aggs['filtered']['buckets']['started']['doc_count']
            enrollment_completed = aggs['filtered']['buckets']['completed']['doc_count']
            enrollment_give_up = aggs['filtered']['buckets']['give_up']['doc_count']
            enrollment_completed_ratio = enrollment_completed / enrollment_total if enrollment_total else 0

            course['stats']['enrollments'] = {
                'total': enrollment_total,
                'started': enrollment_started,
                'completed': enrollment_completed,
                'give_up': enrollment_give_up,
                'completed_ratio': enrollment_completed_ratio,
            }

        # Ratings Stats
        filter = self.esf.make_simple_filter(workspace_id=filters.workspace_id, data_size=0)
        search = self.esf.make_search_ratings(filter)
        search = search.filter('terms', course_id=course_ids)
        search.aggs.bucket('per_course', 'terms', field='course_id')\
            .metric('total', 'value_count', field='id')\
            .metric('average', 'avg', field='rating')

        response = search.execute().to_dict()

        for course in data:
            aggs = next((stat for stat in response['aggregations']['per_course']['buckets'] if stat['key'] == course['_id']), {})
            if not aggs:
                continue
            rating_total = aggs['total']['value']
            rating_average = aggs['average']['value']

            course['stats']['ratings'] = {
                'total': rating_total,
                'average': rating_average,
            }

        return {
            'total': total,
            'data': data
        }

    def get_total_active_users(self, filters: CoursesStatsFilter) -> dict:
        print(f'[Course::V2] get_total_active_users -  filter: {filters}')

        users_ids = self.esf.search_user_ids(filters)

        s = self.esf.make_search_activities(filters, range_field='created_date')

        s = s.filter('terms', user_id=users_ids)
        s = s.filter('exists', field='course_id')

        s.aggs\
            .metric('total', 'value_count', field='id')\
            .metric('active_users', 'cardinality', field='user_id')\
            .bucket('activity_histogram', 'date_histogram', field='created_date', calendar_interval=filters.interval)\
            .metric('users_per_interval', 'cardinality', field='user_id')

        s.aggs.bucket('avg_users_per_interval', 'avg_bucket', buckets_path='activity_histogram>users_per_interval')

        response = s.execute().to_dict()

        return {
            'data': response['hits']['hits'],
            'total': response['aggregations']['total']['value'],
            'aggs': response['aggregations']
        }
