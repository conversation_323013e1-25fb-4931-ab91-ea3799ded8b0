from config.default import Config
from domain.common.dependencies import ElasticsearchConnection, Logger
from domain.course.models import (
    CourseContentsListingFilter,
    CourseEnrollmentsListingFilter,
    CourseFilter,
    CourseListingFilter,
    CourseTotalFilter,
)
from elasticsearch_dsl import Q, Search
from injector import inject


class CourseServiceError(Exception):
    """
    Exception class for errors while executing course services.
    """
    status_code = 500

    def __init__(self, messages, status_code=None, status_error=None):
        Exception.__init__(self)
        self.message = messages
        self.status_code = status_code
        self.status_error = status_error


# Deprecated. Use v2
class CourseService:
    @inject
    def __init__(
        self,
        logger: Logger,
        es: ElasticsearchConnection
    ):
        self.logger = logger
        self.es = es

    def _make_search_course(self, course_id, filters) -> Search:
        # print(f'_make_search_course. course_id: {course_id} - filters: {filters}')

        s = Search(using=self.es, index=Config.ELASTICSEARCH_INDEX_COURSES_V1)

        # Model type
        s = s.filter('term', model_type='course')

        # Company
        q = Q('match', owner_companies__company_id=filters.company_id) &\
            Q('match', owner_companies__relationship_type='OWNER')
        s = s.filter('nested', path='owner_companies', query=q)

        # Course ID
        s = s.filter('term', course_id=course_id)

        # print(s.to_dict())
        return s

    def _make_search(self, filters, range_field=None, model_type=None) -> Search:
        s = Search(using=self.es, index=Config.ELASTICSEARCH_INDEX_COURSES_V1)

        # Company
        q = Q('match', owner_companies__company_id=filters.company_id) &\
            Q('match', owner_companies__relationship_type='OWNER')
        s = s.filter('nested', path='owner_companies', query=q)

        # Date Range
        if range_field and (filters.start_date or filters.end_date):
            s = s.filter('range', **{
                range_field: {
                    'gte': filters.get_start_date(),
                    'lte': filters.get_end_date()
                }
            })

        # Model Type
        if model_type:
            s = s.filter('term', model_type=model_type)

        # Data Size
        if hasattr(filters, 'data_size'):
            s = s[:filters.data_size]

        return s

    def get_course_data(self, course_id, filters: CourseFilter) -> dict:
        # print(f'get_course_data - course_id: {course_id} -  filter: {filters}')
        s = self._make_search_course(course_id, filters)

        response = s.execute().to_dict()

        return dict(data=response['hits']['hits'],
                    total=response['hits']['total']['value'])

    def get_course_enrollments(self, course_id, filters: CourseEnrollmentsListingFilter) -> dict:
        # print(f'get_course_enrollments - course_id: {course_id} -  filter: {filters}')
        s = self._make_search(filters, range_field='enrollment.start_date', model_type='enrollment')

        s = s.filter('term', course_id=course_id)

        if filters.search_term:
            s = s.query("wildcard", user__search_terms=f'*{filters.search_term}*')

        idx_start = filters.page * filters.page_size
        idx_end = idx_start + filters.page_size
        s = s[idx_start:idx_end]

        if filters.sort:
            s = s.sort(filters.sort)
        else:
            # default filter
            s = s.sort(
                '-enrollment.end_date',
                '-enrollment.start_date',
                'user.user_name.sortable'
            )

        response = s.execute().to_dict()
        return dict(total=response['hits']['total']['value'],
                    data=response['hits']['hits'])

    def get_course_contents(self, course_id, filters: CourseContentsListingFilter) -> dict:
        # print(f'get_course_contents - course_id: {course_id} -  filter: {filters}')
        s = self._make_search_course(course_id, filters)

        inner_options = {
            'from': filters.page_size * filters.page,
            'size': filters.page_size
        }

        if filters.sort:
            desc = False
            sort = filters.sort
            if sort[0] == '-':
                desc = True
                sort = sort[1:]
            sort = 'course_contents.' + sort
            inner_options['sort'] = {sort: 'desc' if desc else 'asc'}

        q = Q('exists', field='course_contents.content_id')  # Everything

        if filters.search_term:
            q = q & Q('wildcard', course_contents__search_terms=f'*{filters.search_term}*')

        if filters.type:
            q = q & Q('match', course_contents__content_type_name=filters.type)

        s = s.query('nested', path='course_contents', inner_hits=inner_options, query=q)

        response = s.execute().to_dict()

        if not response['hits']['hits']:
            return dict(total=0, data=[])

        inner_hits = response['hits']['hits'][0]['inner_hits']['course_contents']
        return dict(total=inner_hits['hits']['total']['value'],
                    data=inner_hits['hits']['hits'])

    def get_total_courses(self, filters: CourseTotalFilter) -> dict:
        """
        Returns the total courses created in the period, and aggregation of date histogram.
        """
        s = self._make_search(filters, range_field='created_date', model_type='course')

        s.aggs.bucket('agg_courses', 'date_histogram', field='created_date', interval=filters.interval)
        response = s.execute().to_dict()

        return dict(data=response['hits']['hits'],
                    total=response['hits']['total']['value'],
                    aggs=response['aggregations']['agg_courses'])

    def get_total_courses_started(self, filters: CourseTotalFilter) -> dict:
        """
        Returns the total courses with enrollment started in the period, and aggregation of date histogram.
        """
        s = self._make_search(filters, range_field='enrollment__start_date', model_type='enrollment')
        s = s.filter('term', enrollment__status='STARTED')

        s.aggs.bucket('agg_courses', 'date_histogram', field='enrollment.start_date', interval=filters.interval)
        response = s.execute().to_dict()

        return dict(data=response['hits']['hits'],
                    total=response['hits']['total']['value'],
                    aggs=response['aggregations']['agg_courses'])

    def get_total_courses_completed(self, filters: CourseTotalFilter) -> dict:
        """
        Returns the total courses with enrollment ending in the period, and aggregation of date histogram.
        """
        s = self._make_search(filters, range_field='enrollment__end_date', model_type='enrollment')
        s = s.filter('term', enrollment__status='COMPLETED')

        s.aggs.bucket('agg_courses', 'date_histogram', field='enrollment.end_date', interval=filters.interval)
        response = s.execute().to_dict()

        return dict(data=response['hits']['hits'],
                    total=response['hits']['total']['value'],
                    aggs=response['aggregations']['agg_courses'])

    def get_total_courses_new_enrollment(self, filters: CourseTotalFilter) -> dict:
        """
        Returns the total enrollments created in the period, and aggregation of date histogram.
        """
        s = self._make_search(filters, 'created_date', 'enrollment')

        s.aggs.bucket('agg_courses', 'date_histogram', field='created_date', interval=filters.interval)
        response = s.execute().to_dict()

        return dict(data=response['hits']['hits'],
                    total=response['hits']['total']['value'],
                    aggs=response['aggregations']['agg_courses'])

    def get_ratio_courses_completed(self, filters: CourseTotalFilter) -> dict:
        """
        Returns the ratio of completed enrollments over total, in the period.
        """
        filters.data_size = 0
        s = self._make_search(filters, 'created_date', 'enrollment')
        s.aggs.bucket('status', 'filters', filters={
            'started': Q('term', enrollment__status='STARTED'),
            'completed': Q('term', enrollment__status='COMPLETED'),
        })
        response = s.execute().to_dict()
        total = response['hits']['total']['value']
        completed = response['aggregations']['status']['buckets']['completed']['doc_count']
        # started = response['aggregations']['status']['buckets']['started']['doc_count']
        return completed / total if total else 0

    def get_total_courses_content_hours_available(self, filters: CourseTotalFilter) -> dict:
        """
        Returns the sum of total hours of content of courses available in the period.
        """
        s = self._make_search(filters, 'created_date', 'course')

        s.aggs.metric('total_hours', 'sum', field='duration_time')
        response = s.execute().to_dict()

        return dict(data=response['hits']['hits'],
                    total=response['aggregations']['total_hours']['value'] / (60 * 60))

    def get_total_courses_content_hours_consumed(self, filters: CourseTotalFilter) -> dict:
        """
        Returns the sum of total hours of content of activities in the period.
        """
        s = self._make_search(filters, 'created_date', 'course_activity')

        s.aggs.metric('consumed_hours', 'sum', field='activity.time_in')
        response = s.execute().to_dict()

        return dict(data=response['hits']['hits'],
                    total=response['aggregations']['consumed_hours']['value'] / (60 * 60))

    def get_courses_ratings(self, filters: CourseTotalFilter) -> dict:
        """
        Returns the average ratings of the courses.
        """

        # Old Implementation, based on rating docs
        # s = self._make_search(filters, range_field='created_date', model_type="rating")
        # s.aggs.metric('rating', 'avg', field='enrollment.rating')

        # New implementation, based on pre-computed course stats
        s = self._make_search(filters, range_field='created_date', model_type="course")
        s.aggs.metric('rating', 'avg', field='course_stats.rating.average')

        response = s.execute().to_dict()
        return dict(data=response['hits']['hits'],
                    total=response['hits']['total']['value'],
                    aggs=response['aggregations'])

    def get_courses_types(self, filters: CourseTotalFilter) -> dict:
        """
        Returns the ranking of course types, id and name in subaggregation.
        """
        s = self._make_search(filters, range_field='created_date', model_type="course")
        s.aggs.bucket('types', 'terms', field='course_type.id', size=100)\
            .bucket('name', 'terms', field="course_type.name.keyword")

        response = s.execute().to_dict()
        return dict(data=response['hits']['hits'],
                    total=response['hits']['total']['value'],
                    aggs=response['aggregations'])

    def get_courses_categories(self, filters: CourseTotalFilter) -> dict:
        """
        Returns the ranking of course categories, id and name in subaggregation.
        """
        s = self._make_search(filters, range_field='created_date', model_type="course")
        s.aggs.bucket('categories', 'terms', field='course_category.id', size=100)\
            .bucket('name', 'terms', field="course_category.name.keyword")

        response = s.execute().to_dict()
        return dict(data=response['hits']['hits'],
                    total=response['hits']['total']['value'],
                    aggs=response['aggregations'])

    def get_courses_content_types(self, filters: CourseTotalFilter) -> dict:
        """
        Returns the ranking of content types, id and name in subaggregation.
        """
        s = self._make_search(filters, range_field='created_date', model_type="course")
        s.aggs.bucket('content_types', 'nested', path='course_stats.content_types')\
            .bucket('types', 'terms', field="course_stats.content_types.id", order={'total': 'desc'}, size=100)\
            .metric('total', 'sum', field="course_stats.content_types.total")\
            .bucket('name', 'terms', field="course_stats.content_types.name")

        response = s.execute().to_dict()
        return dict(data=response['hits']['hits'],
                    total=response['hits']['total']['value'],
                    aggs=response['aggregations'])

    def get_courses_list(self, filters: CourseListingFilter) -> dict:
        s = self._make_search(filters, model_type="course")

        # Select fields
        s = s.source(excludes=['course_contents', 'owner_companies'])

        if filters.search_term:
            s = s.query("wildcard", search_terms=f'*{filters.search_term}*')

        idx_start = filters.page * filters.page_size
        idx_end = idx_start + filters.page_size
        s = s[idx_start:idx_end]

        if filters.sort:
            if filters.sort in ['name.keyword', '-name.keyword']:  # TODO remove-me. temporary fix for outdated frontend.
                s = s.sort(filters.sort.replace('keyword', 'sortable'))
            else:
                s = s.sort(filters.sort)
        else:
            # default filter
            s = s.sort(
                '-course_stats.enrollment.total',
                'course_stats.enrollment.completed',
                'name.sortable'
            )

        response = s.execute().to_dict()
        return dict(total=response['hits']['total']['value'],
                    data=response['hits']['hits'])

    def get_total_active_users(self, filters: CourseTotalFilter) -> dict:
        """
        Count active users
        """
        s = self._make_search(filters, range_field='created_date')
        q = Q(
            'bool',
            minimum_should_match=1,
            should=[
                Q('term', model_type='answer'),
                Q('term', model_type='course_activity')
            ],
        )
        s = s.filter(q)

        s.aggs\
            .metric('active_users', 'cardinality', field='user.id')\
            .bucket('activity_histogram', 'date_histogram', field='created_date', interval=filters.interval)\
            .metric('users_per_interval', 'cardinality', field='user.id')

        s.aggs.bucket('avg_users_per_interval', 'avg_bucket', buckets_path='activity_histogram>users_per_interval')

        response = s.execute().to_dict()

        return dict(data=response['hits']['hits'],
                    total=response['hits']['total']['value'],
                    aggs=response['aggregations'])
