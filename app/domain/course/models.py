from config.default import Config
from domain.common.utils import normalize_end_date, normalize_start_date
from elasticsearch_dsl import Boolean, Date, Document, Float, Integer, Keyword, Object, Text


# Deprecated. Use v2.
class CourseFilter:
    def __init__(self, company_id):
        self.company_id = company_id

    def __str__(self):
        return '<CourseFilter(company_id: %s)>' % (self.company_id)


class CourseListingFilter:
    def __init__(self, company_id, search_term=None, page=0, page_size=10, sort=None):
        self.company_id = company_id
        self.search_term = search_term

        self.page = page
        self.page_size = page_size
        self.sort = sort


class CourseEnrollmentsListingFilter:
    def __init__(self, company_id, search_term=None, start_date=None, end_date=None, page=0, page_size=10, sort=None):
        self.company_id = company_id
        self.search_term = search_term

        self.start_date = start_date
        self.end_date = end_date

        self.page = page
        self.page_size = page_size
        self.sort = sort

    def get_start_date(self) -> str:
        return normalize_start_date(self.start_date)

    def get_end_date(self) -> str:
        return normalize_end_date(self.end_date)


class CourseContentsListingFilter:
    def __init__(self, company_id, search_term=None, type=None, page=0, page_size=10, sort=None):
        self.company_id = company_id
        self.search_term = search_term
        self.type = type

        self.page = page
        self.page_size = page_size
        self.sort = sort

    def get_start_date(self) -> str:
        return normalize_start_date(self.start_date)

    def get_end_date(self) -> str:
        return normalize_end_date(self.end_date)


class CourseTotalFilter:
    def __init__(self, company_id, start_date=None, end_date=None, interval='month', data_size=0):
        self.company_id = company_id
        self.start_date = start_date
        self.end_date = end_date
        self.interval = interval
        self.data_size = data_size

    def get_start_date(self) -> str:
        return normalize_start_date(self.start_date)

    def get_end_date(self) -> str:
        return normalize_end_date(self.end_date)

    def __str__(self):
        return '<CourseTotalFilter(company_id: %s, start: %s, end: %s, interval: %s)>' % \
            (self.company_id, self.start_date, self.end_date, self.interval)


class CourseTotalResponse:
    def __init__(self, data, total, aggs):
        self.data = data
        self.total = total
        self.aggs = aggs


class Course(Document):
    id = Text()
    origin = Keyword()
    model_type = Keyword()
    course_id = Keyword()
    name = Text(analyzer='snowball')
    description = Text(analyzer='snowball')
    search_terms = Text(analyzer='snowball')
    created_date = Date()
    updated_date = Date()
    points = Integer()
    is_active = Boolean()
    development_status = Keyword()
    duration_time = Float()
    course_category = Object()
    course_type = Object()
    user_creator = Object()
    bookmarks = Object()
    enrollments = Object()
    goals = Object()
    owner_companies = Object()
    ratings = Object()
    course_type = Object()
    course_category = Object()
    course_stats = Object()
    contents = Object()

    def save(self, ** kwargs):
        return super(Course, self).save(** kwargs)

    class Index:
        name = Config.ELASTICSEARCH_INDEX_COURSES_V1
