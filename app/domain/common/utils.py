import json
from datetime import datetime

from flask import jsonify, request

FILTER_DEFAULT_START_DATE = '2010-01-01'
FILTER_DEFAULT_START_TIME = 'T00:00:00.000Z'
FILTER_DEFAULT_END_DATE = '2100-01-01'
FILTER_DEFAULT_END_TIME = 'T23:59:59.999Z'


def normalize_start_date(date):
    if date is None:
        date = FILTER_DEFAULT_START_DATE
    sufix = FILTER_DEFAULT_START_TIME
    return f'{date}{sufix}'


def normalize_end_date(date):
    if date is None:
        date = FILTER_DEFAULT_END_DATE
    sufix = FILTER_DEFAULT_END_TIME
    return f'{date}{sufix}'


def get_filters(accept_filters: list) -> dict:
    request_args_dict = request.args.to_dict(flat=True)
    filters = build_filter_payload(original_dict=request_args_dict, accept_keys=accept_filters)

    return filters


def build_filter_payload(original_dict: dict, accept_keys: list = None, no_accept_keys: list = None) -> dict:
    """
    Function to control the key values on a dict. To use this function, can be pass the accept_keys or the no_accept_keys
    :param original_dict: the dict to remove the keys
    :param accept_keys: a list of the only keys than can be present on the original_dict
    :param no_accept_keys: a list of the keys to delete in the original_dict

    :return a clean dict:
    """

    if accept_keys and no_accept_keys:
        return original_dict

    keys_outsider = []
    if accept_keys:
        for key in original_dict:
            if key not in accept_keys:
                keys_outsider.append(key)

    for key_outsider in keys_outsider:
        original_dict.pop(key_outsider)

    if no_accept_keys:
        for key in no_accept_keys:
            del original_dict[key]

    return original_dict


def make_error(status_code, message=''):
    response = jsonify({
        'message': message
    })
    response.status_code = status_code
    return response


def make_error_message_post(status_code, message=''):
    response = jsonify({
        'error_message': message,
        'status': 'ERROR'
    })
    response.status_code = status_code
    return response


def make_keeps_error(detail, i18n, status_code):
    response = jsonify({
        'detail': detail,
        'i18n': i18n
    })
    response.status_code = status_code
    return response


def split_chunks(data_list, size):
    for i in range(0, len(data_list), size):
        yield data_list[i:i + size]


class CustomEncoder(json.JSONEncoder):

    def default(self, o):
        if isinstance(o, datetime):
            return {'__datetime__': o.replace(microsecond=0).isoformat()}

        return {'__{}__'.format(o.__class__.__name__): o.__dict__}
