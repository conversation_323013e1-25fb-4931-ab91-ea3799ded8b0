from injector import Key

DatabaseUrl = Key('database_url')
DatabaseEngine = Key('database_engine')
DatabaseSession = Key('database_session')
DatabaseMyAccountUrl = Key('database_myaccount_url')
CourseService = Key('course_service')
NotificationService = Key('notification_service')
Logger = Key('logging')
Settings = Key('settings')
ElasticSearchService = Key('elasticsearch_service')
ElasticsearchURL = Key('elasticsearch_url')
ElasticsearchAUTH = Key('elasticsearch_auth')
ElasticsearchConnection = Key('elasticsearch_connection')
Celery = Key('celery')
CeleryBrokenURL = Key('celery_broken_url')
CeleryResultBackend = Key('celery_result_backend')
Report = Key('report_container')
ReportService = Key('report_service')
ReportTypeService = Key('report_type_service')
WebhookLogger = Key('webhook_logger')
