import uuid
from functools import wraps

import jwt
from flask import current_app, jsonify, request

from controller.api.report_dto import AuthenticatedUser


class HeaderUtils:
    @staticmethod
    def get_clean_header(header_name):
        raw = request.headers.get(header_name, "")
        return raw.split(",")[0].strip()

    @staticmethod
    def validate_uuid(value):
        try:
            return str(uuid.UUID(value))
        except ValueError:
            return None


class JWTAuthenticator:
    def __init__(self, public_key, issuer):
        self.public_key = f"-----BEGIN PUBLIC KEY-----\n{public_key.strip()}\n-----END PUBLIC KEY-----"
        self.issuer = issuer

    def decode_token(self, token):
        return jwt.decode(
            token,
            self.public_key,
            algorithms=["RS256"],
            issuer=self.issuer,
            options={
                "verify_signature": True,
                "verify_exp": True,
                "verify_aud": False,
                "verify_iss": True
            }
        )


def authorize(*allowed_roles):
    def decorator(f):
        @wraps(f)
        def wrapper(*args, **kwargs):
            token = HeaderUtils.get_clean_header("Authorization").replace("Bearer ", "").strip()
            if not token:
                return jsonify({"error": "JWT Token missing or invalid"}), 401

            x_client = HeaderUtils.get_clean_header("x-client")
            if not x_client:
                return jsonify({"error": "x-client header missing"}), 403

            valid_x_client = HeaderUtils.validate_uuid(x_client)
            if not valid_x_client:
                return jsonify({"error": "x-client header is not a valid UUID"}), 400

            request.x_client = valid_x_client

            try:
                authenticator = JWTAuthenticator(
                    public_key=current_app.config['KEYCLOAK_PUBLIC_KEY'],
                    issuer=current_app.config['KEYCLOAK_ISS']
                )

                payload = authenticator.decode_token(token)

                if allowed_roles:
                    roles = payload.get("realm_access", {}).get("roles", [])
                    if not any(role in roles for role in allowed_roles):
                        return jsonify({"error": "Access denied: insufficient permission"}), 403

                request.user = AuthenticatedUser(
                    id=str(payload.get("sub")),
                    name=payload.get("given_name", ""),
                    email=payload.get("email", ""),
                    locale=payload.get("locale", "pt-BR"),
                    time_zone=payload.get("time_zone", "America/Sao_Paulo")
                )

                return f(*args, **kwargs)

            except jwt.ExpiredSignatureError:
                return jsonify({"error": "Token expired"}), 401
            except jwt.InvalidTokenError as e:
                current_app.logger.warning(f"Invalid token: {str(e)}")
                return jsonify({"error": "Invalid token"}), 401

        return wrapper
    return decorator
