from domain.common.dependencies import ElasticsearchConnection, Logger
from flask import jsonify
from injector import inject


class ElasticSearchServiceError(Exception):
    """
    Exception class for errors while executing course services.
    """
    status_code = 500

    def __init__(self, messages, status_code=None, status_error=None):
        Exception.__init__(self)
        self.message = messages
        self.status_code = status_code
        self.status_error = status_error


class ElasticSearchService:
    @inject
    def __init__(
        self,
        logger: Logger,
        es: ElasticsearchConnection
    ):
        self.logger = logger
        self.es = es

    def search(self, index, body):
        res = self.es.search(index=index, body=body)
        return jsonify(res)

    def count(self, index, body):
        res = self.es.count(index=index, body=body)
        return jsonify(res)

    def remove_index(self, index):
        res = self.es.indices.delete(index=index, ignore=[400, 404])
        return jsonify(res)

    def create(self, index, id, body):
        res = self.es.create(index=index, id=id, body=body, ignore=[409])
        return jsonify(res)

    def info(self):
        res = self.es.info()
        return jsonify(res)
