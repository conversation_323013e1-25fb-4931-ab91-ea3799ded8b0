import itertools
from typing import Any, Iterator, List

from config.default import Config
from domain.common.dependencies import ElasticsearchConnection, Logger
from domain.common.utils import normalize_end_date, normalize_start_date
from elasticsearch_dsl import Q, Search, query
from injector import inject


class SimpleFilter():
    workspace_id: str
    start_date: str
    end_date: str
    data_size: int
    workspace_ids: List[str] = []

    def get_start_date(self) -> str:
        return normalize_start_date(self.start_date)

    def get_end_date(self) -> str:
        return normalize_end_date(self.end_date)

    def get_workspace_ids(self) -> List[str]:
        return self.workspace_ids or [self.workspace_id]

    def __str__(self):
        return f'<SimpleFilter(workspace_id: {self.workspace_id}, start_date: {self.start_date}, end_date: {self.end_date}, '\
               f'data_size: {self.data_size})>'


class ElasticSearchFactory:
    @inject
    def __init__(
        self,
        logger: Logger,
        es: ElasticsearchConnection
    ):
        self.logger = logger
        self.es = es

    def make_simple_filter(self, **args) -> SimpleFilter:
        print(f'[ElasticSearchFactory] make_filter - args: {args}')
        filter = SimpleFilter()
        filter.workspace_id = args.get('workspace_id')
        filter.start_date = args.get('start_date')
        filter.end_date = args.get('end_date')
        filter.data_size = args.get('data_size')
        return filter

    def make_search_courses(self, filters: SimpleFilter, range_field=None, course_id=None) -> Search:
        print(f'[ElasticSearchFactory] make_search_courses - filters: {filters} - range_field: {range_field} - course_id: {course_id}')

        search = Search(using=self.es, index=Config.ELASTICSEARCH_INDEX_COURSES)

        # Course ID
        if course_id:
            search = search.filter('term', id=course_id)

        # Company
        q = Q('match', workspaces__workspace_id=filters.workspace_id) &\
            Q('match', workspaces__relationship_type='OWNER')
        search = search.filter('nested', path='workspaces', query=q)

        search = self._apply_filter_range_and_size(search, filters, range_field)

        return search

    def make_search_enrollments(self, filters: SimpleFilter, range_field=None) -> Search:
        print(f'[ElasticSearchFactory] make_search_enrollments - filters: {filters} - range_field: {range_field}')

        search = Search(using=self.es, index=Config.ELASTICSEARCH_INDEX_ENROLLMENTS)

        # Company
        search = search.filter('term', workspace_id=filters.workspace_id)

        search = self._apply_filter_range_and_size(search, filters, range_field)

        return search

    def make_search_users(self, filters: SimpleFilter, range_field=None, user_id=None) -> Search:
        print(f'[ElasticSearchFactory] make_search_users - filters: {filters} - range: {range_field} - user_id: {user_id}')

        search = Search(using=self.es, index=Config.ELASTICSEARCH_INDEX_USERS)

        # User ID
        if user_id:
            search = search.filter('term', id=user_id)

        # Company
        workspace_ids = filters.get_workspace_ids()

        q = Q('terms', **{"user_role_workspace.workspace_id": workspace_ids}) &\
            Q('match', user_role_workspace__role__application_id=Config.KONQUEST_APPLICATION_ID)
        search = search.filter('nested', path='user_role_workspace', query=q)

        search = self._apply_filter_range_and_size(search, filters, range_field)

        return search

    def make_search_activities(self, filters: SimpleFilter, range_field=None) -> Search:
        print(f'[ElasticSearchFactory] make_search_activities - filters: {filters} - range: {range_field}')

        search = Search(using=self.es, index=Config.ELASTICSEARCH_INDEX_ACTIVITIES)

        # Company
        search = search.filter('terms', workspace_id=filters.get_workspace_ids())

        search = self._apply_filter_range_and_size(search, filters, range_field)

        return search

    def make_search_ratings(self, filters: SimpleFilter, range_field=None) -> Search:
        print(f'[ElasticSearchFactory] make_search_ratings - filters: {filters} - range: {range_field}')

        search = Search(using=self.es, index=Config.ELASTICSEARCH_INDEX_COURSE_RATINGS)

        # Company
        search = search.filter('term', workspace_id=filters.workspace_id)

        search = self._apply_filter_range_and_size(search, filters, range_field)

        return search

    def make_search_answers(self, filters: SimpleFilter, range_field=None) -> Search:
        print(f'[ElasticSearchFactory] make_search_answers - filters: {filters} - range: {range_field}')

        search = Search(using=self.es, index=Config.ELASTICSEARCH_INDEX_ANSWERS)

        search = self._apply_filter_range_and_size(search, filters, range_field)

        return search

    def make_search_evaluations(self, filters: SimpleFilter, range_field=None) -> Search:
        print(f'[ElasticSearchFactory] make_search_evaluations - filters: {filters} - range: {range_field}')

        search = Search(using=self.es, index=Config.ELASTICSEARCH_INDEX_COURSE_EVALUATIONS)

        search = self._apply_filter_range_and_size(search, filters, range_field)

        return search

    def make_search_channels(self, filters: SimpleFilter, range_field=None) -> Search:
        print(f'[ElasticSearchFactory] make_search_channels - filters: {filters} - range_field: {range_field}')

        search = Search(using=self.es, index=Config.ELASTICSEARCH_INDEX_CHANNELS)

        # Company
        search = search.filter('term', workspace_id=filters.workspace_id)

        search = self._apply_filter_range_and_size(search, filters, range_field)

        return search

    def make_search_pulses(self, filters: SimpleFilter, range_field=None) -> Search:
        print(f'[ElasticSearchFactory] make_search_pulses - filters: {filters} - range_field: {range_field}')

        search = Search(using=self.es, index=Config.ELASTICSEARCH_INDEX_PULSES)

        search = self._apply_filter_range_and_size(search, filters, range_field)

        return search

    def search_user_ids(self, filters: SimpleFilter):
        print(f'[ElasticSearchFactory] search_user_ids - filters: {filters}')
        search = Search(using=self.es, index=Config.ELASTICSEARCH_INDEX_USERS)

        search = search.source(includes=['id'])

        # Company
        q = Q('match', user_role_workspace__workspace_id=filters.workspace_id) &\
            Q('match', user_role_workspace__role__application_id=Config.KONQUEST_APPLICATION_ID)
        search = search.filter('nested', path='user_role_workspace', query=q)

        response = self.do_search_all(search)
        ids = [user['_id'] for user in response]
        return ids

    # Ref.: https://www.elastic.co/guide/en/elasticsearch/reference/current/paginate-search-results.html
    # Ref.: https://github.com/elastic/elasticsearch-dsl-py/issues/1329
    def do_search_all(self, search: Search) -> List[Any]:
        print(f'[ElasticSearchFactory] do_search_all - search: {search}')

        indexes = search._index
        if len(indexes) != 1:
            raise ValueError(f'Can only perform search all for a single index. Indexes provided: {indexes}')

        # create a point in time for continuous search on the index
        pit = self._make_point_in_time(index=indexes[0])

        # clear index and use point in time sorted by internal shard doc (optimized)
        search = search.index().extra(pit={"id": pit.get("id"), 'keep_alive': '1m'}).sort('_shard_doc')

        # uses max allowed size per iteration
        search = search[:10000]

        all_hits = list(itertools.chain([hits for hits in self._search_all_iteration(search)]))
        return all_hits

    def _search_all_iteration(self, search: Search) -> Iterator[List[Any]]:
        # print(f'[ESF] _search_iteration - search: {json.dumps(search.to_dict())}')
        response = search.execute().to_dict()
        hits = response['hits']['hits']

        if hits:
            yield from hits

            last_hit = hits[-1]
            search = search.extra(search_after=last_hit['sort'])
            # print(f'[ESF] _search_iteration - total_hits: {len(hits)} - last_hit: {last_hit}')
            yield from self._search_all_iteration(search)

    def _make_point_in_time(self, index: str, keep_alive: str = '1m'):
        # print(f'[ElasticSearchFactory] make_point_in_time - index: {index} - keep_alive: {keep_alive}')
        pit = self.es.open_point_in_time(index=index, keep_alive=keep_alive)
        # print(f'[ElasticSearchFactory] make_point_in_time - pit: {pit}')
        return pit

    @staticmethod
    def _apply_filter_range_and_size(search: Search, filters: SimpleFilter, range_field=None) -> Search:

        # Date Range
        if range_field and (filters.start_date or filters.end_date):
            search = search.filter('range', **{
                range_field: {
                    'gte': filters.get_start_date(),
                    'lte': filters.get_end_date()
                }
            })

        # Data Size
        if hasattr(filters, 'data_size'):
            search = search[:filters.data_size]

        return search

    @staticmethod
    def flatten_buckets(buckets: dict):
        return [{
            'id': bucket['key'],
            'name': bucket['name']['buckets'][0]['key'],
            'total': bucket['doc_count'],
        } for bucket in buckets]

    @staticmethod
    def first_item(items: list, empty=None):
        return next(iter(items), empty),

    @staticmethod
    def make_query_search_as_you_type(search_field: str, search_term: str) -> query.Query:
        query = Q(
            'bool',
            minimum_should_match=1,
            should=[
                Q('match_phrase_prefix', **{search_field: search_term}),
                Q('multi_match',
                    query=search_term,
                    type="bool_prefix",
                    fields=[
                        search_field,
                        f'{search_field}._2gram',
                        f'{search_field}._3gram',
                    ])
            ]
        )
        return query
