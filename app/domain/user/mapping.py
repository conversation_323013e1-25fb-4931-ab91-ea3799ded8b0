# Deprecated. Mapping on the Indexer Project.
user_mapping = {
    'settings': {
        "analysis": {
            "normalizer": {
                "case_insensitive": {
                    "filter": ["lowercase"]
                }
            }
        }
    },
    'mappings': {
        'properties': {
            'id': {
                'type': 'keyword'
            },
            'user_id': {
                'type': 'keyword'
            },
            'name': {
                'type': 'text',
                'copy_to': 'search_terms',
                'fields': {
                    "keyword": {'type': 'keyword'},
                    "sortable": {
                        'type': 'keyword',
                        'normalizer': 'case_insensitive'
                    },
                }
            },
            'nickname': {
                'type': 'text',
                'copy_to': 'search_terms',
                'fields': {
                    "keyword": {'type': 'keyword'}
                }
            },
            'email': {
                'type': 'keyword'
            },
            'secondary_email': {
                'type': 'keyword'
            },
            'phone': {
                'type': 'keyword'
            },
            'gender': {
                'type': 'keyword'
            },
            'job': {
                'type': 'keyword'
            },
            'birthday': {
                'type': 'date'
            },
            'address': {
                'type': 'keyword'
            },
            'avatar': {
                'type': 'keyword'
            },
            'status': {
                'type': 'keyword'
            },
            'created_date': {
                'type': 'date'
            },
            'updated_date': {
                'type': 'date'
            },
            'language_id': {
                'type': 'keyword'
            },
            'model_type': {
                'type': 'keyword'
            },
            'origin': {
                'type': 'keyword'
            },
            "search_terms": {
                "type": "text",
            },
            'user_role_company': {
                'type': 'nested',
                'properties': {
                    'id': {'type': 'keyword'},
                    'company_id': {'type': 'keyword'},
                    'role': {
                        'type': 'object',
                        'properties': {
                            'id': {'type': 'keyword'},
                            'name': {'type': 'keyword'},
                        }
                    },
                    'status': {'type': 'keyword'},
                }
            },
            'enrollments': {
                'type': 'nested',
                'properties': {
                    'id': {'type': 'keyword'},
                    'company_id': {'type': 'keyword'},
                    'mission_id': {'type': 'keyword'},
                    'mission_name': {
                        'type': 'text',
                        'copy_to': 'enrollments.search_terms',
                        'fields': {
                            "keyword": {'type': 'keyword'},
                            "sortable": {
                                'type': 'keyword',
                                'normalizer': 'case_insensitive'
                            }
                        }
                    },
                    'category_id': {'type': 'keyword'},
                    'category_name': {
                        'type': 'text',
                        'copy_to': 'enrollments.search_terms',
                        'fields': {
                            "keyword": {'type': 'keyword'},
                            "sortable": {
                                'type': 'keyword',
                                'normalizer': 'case_insensitive'
                            }
                        }
                    },
                    'status': {'type': 'keyword'},
                    'points': {'type': 'float'},
                    'performance': {'type': 'float'},
                    'rating': {'type': 'float'},
                    'activities_total_time': {'type': 'float'},
                    'created_date': {'type': 'date'},
                    'updated_date': {'type': 'date'},
                    'start_date': {'type': 'date'},
                    'end_date': {'type': 'date'},
                    "search_terms": {
                        "type": "text",
                    },
                }
            },
            'activities': {
                'type': 'nested',
                'properties': {
                    'id': {'type': 'keyword'},
                    'pulse_id': {'type': 'keyword'},
                    'mission_stage_content_id': {'type': 'keyword'},
                    'stage_id': {'type': 'keyword'},
                    'mission_id': {'type': 'keyword'},
                    'kontent_id': {'type': 'keyword'},
                    'company_ids': {'type': 'keyword'},  # array
                    'content_name': {'type': 'keyword'},
                    'content_type_id': {'type': 'keyword'},
                    'content_type_name': {'type': 'keyword'},
                    'created_date': {'type': 'date'},
                    'start_date': {'type': 'date'},
                    'stop_date': {'type': 'date'},
                    'time_in': {'type': 'float'},
                }
            },
            'owned_channels': {
                'type': 'nested',
                'properties': {
                    'id': {'type': 'keyword'},
                    'company_id': {'type': 'keyword'},
                    'name': {'type': 'keyword'},
                    'created_date': {'type': 'date'},
                    'updated_date': {'type': 'date'},
                    'is_active': {'type': 'boolean'}
                }
            },
            'owned_courses': {
                'type': 'nested',
                'properties': {  # TODO nested companies?
                    'id': {'type': 'keyword'},
                    'name': {'type': 'keyword'},
                    'created_date': {'type': 'date'},
                    'updated_date': {'type': 'date'},
                    'is_active': {'type': 'boolean'}
                }
            },
            'user_stats': {
                'type': 'object',
                'properties': {
                    'updated_date': {'type': 'date'},
                    'courses': {
                        'type': 'object',
                        'properties': {
                            'total': {'type': 'integer'},
                            'completed': {'type': 'integer'},
                            'completed_ratio': {'type': 'float'},
                            'created': {'type': 'integer'},
                            'contributed': {'type': 'integer'},
                            'channels_created': {'type': 'integer'},
                            'pulses_created': {'type': 'integer'},
                        }
                    },
                    'activities': {
                        'type': 'object',
                        'properties': {
                            'reference_date': {'type': 'date'},
                            'total_missions_activities': {'type': 'integer'},
                            'total_unique_missions': {'type': 'integer'},
                            'total_pulses_activities': {'type': 'integer'},
                            'total_unique_pulses': {'type': 'integer'},
                            'missions_last_7_days': {'type': 'integer'},
                            'missions_last_30_days': {'type': 'integer'},
                            'missions_previous_last_7_days': {'type': 'integer'},
                            'missions_previous_last_30_days': {'type': 'integer'},
                            'pulses_last_7_days': {'type': 'integer'},
                            'pulses_last_30_days': {'type': 'integer'},
                            'pulses_previous_last_7_days': {'type': 'integer'},
                            'pulses_previous_last_30_days': {'type': 'integer'},
                        }
                    },
                    'answers': {
                        'type': 'object',
                        'properties': {
                            'total_exams': {'type': 'integer'},
                            'total_questions': {'type': 'integer'},
                            'total_answers': {'type': 'integer'},
                            'correct_answers': {'type': 'integer'},
                            'correct_answers_ratio': {'type': 'float'},
                        }
                    },
                    'course_categories': {
                        'type': 'nested',
                        'properties': {
                            'id': {'type': 'keyword'},
                            'name': {'type': 'keyword'},
                            'total': {'type': 'integer'},
                        }
                    },
                    'content_types': {
                        'type': 'nested',
                        'properties': {
                            'id': {'type': 'keyword'},
                            'name': {'type': 'keyword'},
                            'total': {'type': 'integer'},
                        }
                    },
                }
            }
        }
    }
}
