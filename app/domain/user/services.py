import datetime

from config.default import Config
from domain.common.dependencies import ElasticsearchConnection, Logger
from domain.user.models import <PERSON><PERSON><PERSON><PERSON><PERSON>, UserListingFilter, UserTotalFilter
from elasticsearch_dsl import Q, Search
from injector import inject


class UserServiceError(Exception):
    """
    Exception class for errors while executing course services.
    """
    status_code = 500

    def __init__(self, messages, status_code=None, status_error=None):
        Exception.__init__(self)
        self.message = messages
        self.status_code = status_code
        self.status_error = status_error


# Deprecated. Use v2
class UserService:
    @inject
    def __init__(
        self,
        logger: Logger,
        es: ElasticsearchConnection
    ):
        self.logger = logger
        self.es = es

    def _make_search_user(self, user_id, filters) -> Search:
        # print(f'_make_search_user. user_id: {user_id} - filters: {filters}')

        s = Search(using=self.es, index=Config.ELASTICSEARCH_INDEX_USERS_V1)

        # Model type
        s = s.filter('term', model_type='user')

        # Company
        s = s.filter('nested', path='user_role_company', query=Q('match', user_role_company__company_id=filters.company_id))

        # User ID
        s = s.filter('term', user_id=user_id)

        # print(s.to_dict())
        return s

    def _make_search_users(self, filters, range_field=None, nested_range_field=None, model_type='user') -> Search:
        # print(f'_make_search_users. range: {range_field} - type: {model_type} - filters: {filters}')
        s = Search(using=self.es, index=Config.ELASTICSEARCH_INDEX_USERS_V1)

        # Company
        s = s.filter('nested', path='user_role_company', query=Q('match', user_role_company__company_id=filters.company_id))

        # Date Range
        if (range_field or nested_range_field) and (filters.start_date or filters.end_date):
            if range_field:
                # print('Range field')
                s = s.filter('range', **{
                    range_field: {
                        'gte': filters.get_start_date(),
                        'lte': filters.get_end_date()
                    }
                })
            if nested_range_field:
                # print('Nested field')
                nested_path = nested_range_field.split('.')[0]
                q = Q('range', **{
                    nested_range_field: {
                        'gte': filters.get_start_date(),
                        'lte': filters.get_end_date()
                    }
                })
                s = s.filter('nested', path=nested_path, query=q)

        # Model Type
        if model_type:
            s = s.filter('term', model_type=model_type)

        # Data Size
        if hasattr(filters, 'data_size'):
            s = s[:filters.data_size]

        # print(s.to_dict())
        return s

    def get_user_data(self, user_id, filters: UserFilter) -> dict:
        print(f'get_user_data - user_id: {user_id} -  filter: {filters}')
        s = self._make_search_user(user_id, filters)

        # Select fields
        s = s.source(excludes=['enrollments', 'activities', 'user_role_company'])

        enrollments = s.aggs\
            .bucket('enrollments', 'nested', path='enrollments')\
            .metric('performance_avg', 'avg', field='enrollments.performance')\
            .metric('activities_total_seconds', 'sum', field='enrollments.activities_total_time')

        enrollments\
            .bucket('completed_enrollments', 'filter', filter=Q('match', enrollments__status='COMPLETED'))\
            .metric('activities_total_seconds', 'sum', field='enrollments.activities_total_time')\
            .metric('performance_avg', 'avg', field='enrollments.performance')\
            .bucket('performance_ranges', 'range', field='enrollments.performance', ranges=[
                {'to': 0.1},
                {'from': 0.1, 'to': 0.25},
                {'from': 0.25, 'to': 0.50},
                {'from': 0.50, 'to': 0.75},
                {'from': 0.75},
            ])

        enrollments\
            .bucket('categories', 'terms', field='enrollments.category_id', size=100)\
            .bucket('name', 'terms', field="enrollments.category_name.keyword")

        activities = s.aggs.bucket('activities', 'nested', path='activities')

        activities\
            .bucket('content_types', 'terms', field="activities.content_type_id", size=100)\
            .bucket('name', 'terms', field="activities.content_type_name")

        response = s.execute().to_dict()

        return dict(data=response['hits']['hits'],
                    total=response['hits']['total']['value'],
                    aggs=response['aggregations'])

    def get_user_enrollments(self, user_id, filters: UserFilter) -> dict:
        # print(f'get_user_enrollments - user_id: {user_id} -  filter: {filters}')
        s = self._make_search_user(user_id, filters)

        inner_options = {
            'from': filters.page_size * filters.page,
            'size': filters.page_size
        }

        if filters.sort:
            desc = False
            sort = filters.sort
            if sort[0] == '-':
                desc = True
                sort = sort[1:]
            sort = 'enrollments.' + sort
            inner_options['sort'] = {sort: 'desc' if desc else 'asc'}

        q = Q('exists', field='enrollments.id')  # Everything
        if filters.search_term:
            q = Q('wildcard', enrollments__search_terms=f'*{filters.search_term}*')

        s = s.query('nested', path='enrollments', inner_hits=inner_options, query=q)

        response = s.execute().to_dict()

        if not response['hits']['hits']:
            return dict(total=0, data=[])

        inner_hits = response['hits']['hits'][0]['inner_hits']['enrollments']
        return dict(total=inner_hits['hits']['total']['value'],
                    data=inner_hits['hits']['hits'])

    def get_total_users(self, filters: UserTotalFilter) -> dict:
        s = self._make_search_users(filters, range_field='created_date')

        s.aggs.bucket('agg_users', 'date_histogram', field='created_date', interval=filters.interval)
        response = s.execute().to_dict()

        return dict(data=response['hits']['hits'],
                    total=response['hits']['total']['value'],
                    aggs=response['aggregations']['agg_users'])

    def get_active_users(self, filters: UserTotalFilter) -> dict:
        s = self._make_search_users(filters, nested_range_field="activities.start_date")

        s.aggs.metric('active_users', 'cardinality', field='user_id')

        activities_agg = s.aggs\
            .bucket('activities', 'nested', path='activities')\
            .bucket('activities_in_range', 'filter', filter=Q('range', **{
                'activities.start_date': {
                    'gte': filters.get_start_date(),
                    'lte': filters.get_end_date()
                }
            }))

        activities_agg\
            .bucket('activity_histogram', 'date_histogram', field='activities.start_date', interval=filters.interval)\
            .bucket('histogram_users', 'reverse_nested')\
            .metric('users_per_interval', 'cardinality', field='user_id')

        activities_agg\
            .bucket('avg_users_per_interval', 'avg_bucket', buckets_path='activity_histogram>histogram_users>users_per_interval')

        response = s.execute().to_dict()

        return dict(data=response['hits']['hits'],
                    total=response['hits']['total']['value'],
                    aggs=response['aggregations'])

    def get_users_with_enrollments(self, filters: UserTotalFilter) -> dict:
        # print('get_users_with_enrollments - filter: ' + str(filters))
        s = self._make_search_users(filters, nested_range_field="enrollments.start_date")

        s = s.query('nested', path='enrollments', query=Q('exists', field='enrollments'))

        s.aggs.bucket('per_user', 'terms', field='user_id', size=9999999)\
            .bucket('user_enrollments', 'nested', path='enrollments')\
            .bucket('per_status', 'terms', field='enrollments.status')

        response = s.execute().to_dict()
        return response

    def get_users_without_enrollments(self, filters: UserTotalFilter) -> dict:
        # print('get_users_without_enrollments - filter: ' + str(filters))
        s = self._make_search_users(filters)

        q = Q('nested', path='enrollments', query=Q('exists', field='enrollments'))
        s = s.query('bool', must_not=[q])

        response = s.execute().to_dict()
        return response

    def get_users_with_started_enrollments(self, filters: UserTotalFilter) -> dict:
        # print('get_users_with_started_enrollments - filter: ' + str(filters))
        s = self._make_search_users(filters, nested_range_field='enrollments.start_date')

        s = s.filter('nested', path='enrollments', query=Q('match', enrollments__status='STARTED'))

        s.aggs.bucket('per_user', 'terms', field='user_id', size=9999999)\
            .bucket('user_enrollments', 'nested', path='enrollments')\
            .bucket('per_status', 'terms', field='enrollments.status')

        response = s.execute().to_dict()
        return response

    def get_users_with_completed_enrollments(self, filters: UserTotalFilter) -> dict:
        # print('get_users_with_completed_enrollments - filter: ' + str(filters))
        s = self._make_search_users(filters, nested_range_field='enrollments.end_date')

        s = s.filter('nested', path='enrollments', query=Q('match', enrollments__status='COMPLETED'))

        s.aggs.bucket('per_user', 'terms', field='user_id', size=9999999)\
            .bucket('user_enrollments', 'nested', path='enrollments')\
            .bucket('per_status', 'terms', field='enrollments.status')

        response = s.execute().to_dict()
        return response

    def get_distribution_users_enrollments(self, filters: UserTotalFilter) -> dict:
        # print('get_distribution_users_enrollments - filter: ' + str(filters))

        enrollments_response = self.get_users_with_enrollments(filters)
        without_response = self.get_users_without_enrollments(filters)

        ranges = [
            {'range': '0', 'total': without_response['hits']['total'], 'started': 0, 'completed': 0},
            {'range': '1-3', 'total': 0, 'started': 0, 'completed': 0},
            {'range': '3-5', 'total': 0, 'started': 0, 'completed': 0},
            {'range': '5-10', 'total': 0, 'started': 0, 'completed': 0},
            {'range': '10-15', 'total': 0, 'started': 0, 'completed': 0},
            {'range': '15+', 'total': 0, 'started': 0, 'completed': 0},
        ]

        def range_index(count: int) -> int:
            if count == 0:
                return 0
            if count <= 3:
                return 1  # 1-3
            if count <= 5:
                return 2  # 3-5
            if count <= 10:
                return 3  # 5-10
            if count <= 15:
                return 4  # 10-15
            return 5  # 15+

        # print(enrollments_response['aggregations'])

        for user in enrollments_response['aggregations']['per_user']['buckets']:
            total = user['user_enrollments']['doc_count']
            per_status = user['user_enrollments']['per_status']['buckets']
            started = next((x for x in per_status if x['key'] == 'STARTED'), {'doc_count': 0})['doc_count']
            completed = next((x for x in per_status if x['key'] == 'COMPLETED'), {'doc_count': 0})['doc_count']

            ranges[range_index(total)]['total'] += 1
            ranges[range_index(started)]['started'] += 1
            ranges[range_index(completed)]['completed'] += 1

        return dict(aggs={'enrollments_per_user': ranges})

    def get_engagement_rate(self, filters: UserTotalFilter) -> dict:
        # print('get_engagement_rate - filter: ' + str(filters))

        # Last 30 days from end_date or today
        if not filters.end_date:
            filters.end_date = str(datetime.date.today())
        filters.start_date = str(datetime.date(*map(int, filters.end_date.split('-'))) - datetime.timedelta(days=30))

        # Company stats for Courses
        company_total_missions = self._count_company_missions(filters)
        company_total_pulses = self._count_company_pulses(filters)
        # print(f'company missions: {company_total_missions}')
        # print(f'company pulses: {company_total_pulses}')

        s = self._make_search_users(filters)

        per_user = s.aggs.bucket('per_user', 'terms', field='user_id', size=9999999)

        q = Q('match', enrollments__company_id=filters.company_id)\
            & Q('match', enrollments__status='COMPLETED')\
            & Q('range', **{'enrollments.start_date': {
                'gte': filters.get_start_date(),
                'lte': filters.get_end_date()
            }})

        per_user.bucket('user_enrollments', 'nested', path='enrollments')\
            .bucket('completed_enrollments', 'filter', filter=q)\
            .metric('enrollments_count', 'cardinality', field='enrollments.id')\
            .metric('performance_avg', 'avg', field='enrollments.performance')

        user_activities = per_user.bucket('user_activities', 'nested', path='activities')

        q = Q('match', activities__company_ids=filters.company_id)\
            & Q('range', **{'activities.start_date': {
                'gte': filters.get_start_date(),
                'lte': filters.get_end_date()
            }})

        user_activities\
            .bucket('pulse_activities', 'filter', filter=q & Q('exists', field='activities.pulse_id'))\
            .metric('pulses_count', 'cardinality', field='activities.pulse_id')\
            .bucket('pulses_per_day', 'date_histogram', field='activities.start_date', interval='day', min_doc_count='1')

        user_activities\
            .bucket('mission_activities', 'filter', filter=q & Q('exists', field='activities.mission_id'))\
            .metric('missions_count', 'cardinality', field='activities.mission_id')\
            .bucket('missions_per_day', 'date_histogram', field='activities.start_date', interval='day', min_doc_count='1')

        response = s.execute().to_dict()

        total_users = response['hits']['total']['value']
        # print(f'company users: {total_users}')
        engagement_map = dict()
        engagement_sum = 0

        for user in response['aggregations']['per_user']['buckets']:
            user_id = user['key']

            total_days_pulses_consumed = len(user['user_activities']['pulse_activities']['pulses_per_day']['buckets'])
            total_days_missions_consumed = len(user['user_activities']['mission_activities']['missions_per_day']['buckets'])
            total_days_consumed = total_days_pulses_consumed + total_days_missions_consumed

            if total_days_consumed == 0:
                engagement_map[user_id] = 0
                continue

            total_missions_completed = user['user_enrollments']['completed_enrollments']['enrollments_count']['value']
            avg_mission_performance = user['user_enrollments']['completed_enrollments']['performance_avg']['value']
            total_pulses_consumed = user['user_activities']['pulse_activities']['pulses_count']['value']

            if company_total_missions < 10:
                total_missions_completed = 4
                avg_mission_performance = 1

            if company_total_pulses < 10:
                total_pulses_consumed = 4

            total_missions_completed = max(total_missions_completed, 4)
            total_pulses_consumed = max(total_pulses_consumed, 4)
            total_days_consumed = max(total_days_consumed, 10)
            avg_mission_performance = avg_mission_performance or 0

            # Parameters
            multiplier_missions_consumed = 0.25
            multiplier_pulses_consumed = 0.25
            multiplier_days_consumed = 0.1

            values = [
                avg_mission_performance,
                total_missions_completed * multiplier_missions_consumed,
                total_pulses_consumed * multiplier_pulses_consumed,
                total_days_consumed * multiplier_days_consumed
            ]
            weight = [3, 2, 1, 2]

            engagement = sum(values[i] * weight[i] for i in range(len(values))) / sum(weight)
            engagement_map[user_id] = engagement
            engagement_sum += engagement

        # print('-----\n' + str(engagement_map))
        avg_engagement_rate = engagement_sum / total_users if total_users else 0
        return avg_engagement_rate

    def get_content_consumed_per_user(self, filters: UserTotalFilter) -> dict:
        # print('get_content_consumed_per_user - filter: ' + str(filters))
        s = self._make_search_users(filters, nested_range_field='activities.created_date')

        s.aggs.metric('total_users', 'cardinality', field='user_id')
        activities_agg = s.aggs.bucket('activities', 'nested', path='activities')

        # Filter nested activities in range
        is_nested = False
        if filters.start_date or filters.end_date:
            is_nested = True
            q = Q('range', **{
                'activities.created_date': {
                    'gte': filters.get_start_date(),
                    'lte': filters.get_end_date()
                }
            })
            activities_agg = activities_agg.bucket('activities_in_range', 'filter', filter=q)

        activities_agg.metric('consumed_seconds', 'sum', field='activities.time_in')

        response = s.execute().to_dict()

        if is_nested:
            total = response['aggregations']['activities']['activities_in_range']['consumed_seconds']['value']
        else:
            total = response['aggregations']['activities']['consumed_seconds']['value']

        total_users = response['aggregations']['total_users']['value']
        hours_per_user = (total / (60 * 60)) / total_users if total_users else 0

        return dict(data=response['hits']['hits'],
                    total=response['hits']['total']['value'],
                    aggs=response['aggregations'],
                    hours_per_user=hours_per_user)

    def get_users_creators(self, filters: UserTotalFilter) -> dict:
        # print('get_users_creators - filter: ' + str(filters))

        s = self._make_search_users(filters)

        if filters.start_date or filters.end_date:
            q1 = Q('nested', path='owned_channels', query=Q('range', **{
                'owned_channels.created_date': {
                    'gte': filters.get_start_date(),
                    'lte': filters.get_end_date()
                }
            }))
            q2 = Q('nested', path='owned_courses', query=Q('range', **{
                'owned_courses.created_date': {
                    'gte': filters.get_start_date(),
                    'lte': filters.get_end_date()
                }
            }))

        else:
            q1 = Q('nested', path='owned_channels', query=Q('exists', field='owned_channels'))
            q2 = Q('nested', path='owned_courses', query=Q('exists', field='owned_courses'))

        q = Q(
            'bool',
            # minimum_should_match=1,
            should=[q1, q2],
        )
        s = s.filter(q)

        response = s.execute().to_dict()

        return dict(total=response['hits']['total']['value'],
                    data=response['hits']['hits'])

    def get_users_enrollment_categories(self, filters: UserTotalFilter) -> dict:
        # print('get_users_enrollment_categories - filter: ' + str(filters))

        s = self._make_search_users(filters, nested_range_field='enrollments.created_date')
        s.aggs.bucket('enrollments', 'nested', path='enrollments')\
            .bucket('company_enrollments', 'filter', filter=Q('term', enrollments__company_id=filters.company_id))\
            .bucket('categories', 'terms', field='enrollments.category_id', size=100)\
            .bucket('name', 'terms', field="enrollments.category_name.keyword")

        response = s.execute().to_dict()
        return dict(data=response['hits']['hits'],
                    total=response['hits']['total']['value'],
                    aggs=response['aggregations'])

    def get_users_activity_content_types(self, filters: UserTotalFilter) -> dict:
        # print('get_users_activity_content_types - filter: ' + str(filters))

        s = self._make_search_users(filters, nested_range_field='activities.created_date')

        s.aggs.bucket('activities', 'nested', path='activities')\
            .bucket('company_activities', 'filter', filter=Q('term', activities__company_ids=filters.company_id))\
            .bucket('content_types', 'terms', field="activities.content_type_id", size=100)\
            .bucket('name', 'terms', field="activities.content_type_name")

        response = s.execute().to_dict()
        return dict(data=response['hits']['hits'],
                    total=response['hits']['total']['value'],
                    aggs=response['aggregations'])

    def get_users_list(self, filters: UserListingFilter) -> dict:
        # print('get_users_list - filter: ' + str(filters))
        s = self._make_search_users(filters, model_type='user')

        # Select fields
        s = s.source(excludes=['enrollments', 'activities', 'owned_*', 'user_role_company'])

        if filters.search_term:
            s = s.query("wildcard", search_terms=f'*{filters.search_term}*')

        idx_start = filters.page * filters.page_size
        idx_end = idx_start + filters.page_size
        s = s[idx_start:idx_end]

        if filters.sort:
            s = s.sort(filters.sort)
        else:
            # default filter
            s = s.sort(
                '-user_stats.courses.completed_ratio',
                '-user_stats.courses.total',
                'name.sortable'
            )

        response = s.execute().to_dict()
        return dict(total=response['hits']['total']['value'],
                    data=response['hits']['hits'])

    # Count missions owned by the company
    def _count_company_missions(self, filters: UserTotalFilter) -> int:
        s = Search(using=self.es, index=Config.ELASTICSEARCH_INDEX_COURSES_V1)[:0]

        s = s.filter('term', model_type='course')

        q = Q('match', owner_companies__company_id=filters.company_id) &\
            Q('match', owner_companies__relationship_type='OWNER')
        s = s.filter('nested', path='owner_companies', query=q)

        response = s.execute().to_dict()
        return response['hits']['total']['value']

    # Count unique pulses ids from related activities
    def _count_company_pulses(self, filters: UserTotalFilter) -> int:
        s = Search(using=self.es, index=Config.ELASTICSEARCH_INDEX_USERS_V1)[:0]

        # company pulses activities query
        q = Q('exists', field='activities.pulse_id')\
            & Q('match', activities__company_ids=filters.company_id)

        s = s.filter('nested', path='activities', query=q)

        s.aggs.bucket('activities', 'nested', path='activities')\
            .bucket('company_pulse_activities', 'filter', filter=q)\
            .metric('unique_pulses_count', 'cardinality', field='activities.pulse_id')

        response = s.execute().to_dict()
        return response['aggregations']['activities']['company_pulse_activities']['unique_pulses_count']['value']
