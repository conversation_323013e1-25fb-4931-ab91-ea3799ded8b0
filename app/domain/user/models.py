from config.default import Config
from domain.common.utils import normalize_end_date, normalize_start_date
from elasticsearch_dsl import Date, Document, Object, Text


# Deprecated. Use v2
class UserFilter:
    def __init__(self, company_id):
        self.company_id = company_id

    def __str__(self):
        return '<UserFilter(company_id: %s)>' % (self.company_id)


class UserListingFilter:
    def __init__(self, company_id, search_term=None, page=0, page_size=10, sort=None):
        self.company_id = company_id
        self.search_term = search_term

        self.page = page
        self.page_size = page_size
        self.sort = sort


class UserTotalFilter:
    def __init__(self, company_id, start_date=None, end_date=None, interval='month', data_size=0):
        self.company_id = company_id
        self.start_date = start_date
        self.end_date = end_date
        self.interval = interval
        self.data_size = data_size

    def get_start_date(self) -> str:
        return normalize_start_date(self.start_date)

    def get_end_date(self) -> str:
        return normalize_end_date(self.end_date)

    def __str__(self):
        return '<UserTotalFilter(company_id: %s, start: %s, end: %s, interval: %s)>' % \
            (self.company_id, self.start_date, self.end_date, self.interval)


class UserTotalResponse:
    def __init__(self, data, total, aggs):
        self.data = data
        self.total = total
        self.aggs = aggs


class User(Document):
    id = Text()
    name = Text(analyzer='snowball')
    nickname = Text(analyzer='snowball')
    email = Text(analyzer='snowball')
    secondary_email = Text(analyzer='snowball')
    created_date = Date()
    updated_date = Date()

    phone = Text()
    gender = Text()
    job = Text()
    birthday = Text()
    address = Text()
    avatar = Text()
    status = Text()
    language_id = Text()
    model_type = Text()
    origin = Text()
    user_role_company = Object()
    enrollments = Object()
    activities = Object()
    owned_channels = Object()
    owned_courses = Object()
    user_stats = Object()

    def save(self, ** kwargs):
        return super(User, self).save(** kwargs)

    class Index:
        name = Config.ELASTICSEARCH_INDEX_USERS_V1
