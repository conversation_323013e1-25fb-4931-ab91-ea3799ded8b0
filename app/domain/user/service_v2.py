import datetime

from domain.common.dependencies import Logger
from domain.elasticsearch.search_factory import ElasticSearchFactory
from domain.user.models_v2 import UserData<PERSON>ilter, UserEnrollmentsFilter, UsersListingFilter, UsersStatsFilter
from elasticsearch_dsl import Q
from injector import inject


class UserServiceV2:
    @inject
    def __init__(
        self,
        logger: Logger,
        esf: ElasticSearchFactory
    ):
        self.logger = logger
        self.esf = esf

    def get_user_data(self, user_id, filters: UserDataFilter) -> dict:
        # print(f'[User::v2] get_user_data - user_id: {user_id} -  filter: {filters}')
        search = self.esf.make_search_users(filters, user_id=user_id)

        # Select fields
        search = search.source(excludes=['user_role_company'])

        response = search.execute().to_dict()

        value = {
            'data': response['hits']['hits'],
            'stats': {
                'enrollments': {
                    'total': 0,
                    'performance_avg': 0,
                    'categories': [],
                    'completed': {
                        'total': 0,
                        'ratio': 0,
                        'performance_avg': 0,
                        'ranges': []
                    }
                },

                'activities': {
                    'total_seconds': 0,
                    'enrollments_total_activities': 0,
                    'enrollments_total_seconds': 0,
                    'completed_enrollments_total_activities': 0,
                    'completed_enrollments_total_seconds': 0,

                    'courses_total_activities': 0,
                    'courses_total_courses': 0,
                    'courses_recent_activities': {},

                    'pulses_total_activities': 0,
                    'pulses_total_pulses': 0,
                    'pulses_recent_activities': {},

                    'content_types': [],
                },

                'answers': {
                    'total_exams': 0,
                    'total_questions': 0,
                    'total_answers': 0,
                    'correct_answers': 0,
                    'correct_ratio': 0,
                },

                'courses': {
                    'courses_created': 0,
                    'courses_contributed': 0,
                    'channels_created': 0,
                    'pulses_created': 0,
                }

            },
        }

        if not value['data']:
            return value

        # Enrollments Stats
        filter = self.esf.make_simple_filter(**filters.__dict__, data_size=9999)
        search = self.esf.make_search_enrollments(filter, range_field='start_date')
        search = search.source(includes=['id'])
        search = search.filter('term', user_id=user_id)

        search.aggs\
            .metric('total', 'value_count', field='id')\
            .metric('performance_avg', 'avg', field='performance')

        search.aggs\
            .bucket('categories', 'terms', field='category_id', size=100)\
            .bucket('name', 'terms', field="category_name.keyword")

        completed_enrollments = search.aggs.bucket('completed_enrollments', 'filter', filter=Q('match', status='COMPLETED'))

        completed_enrollments\
            .metric('total', 'value_count', field='id')\
            .metric('performance_avg', 'avg', field='performance')\
            .bucket('performance_ranges', 'range', field='performance', ranges=[
                {'to': 0.1},
                {'from': 0.1, 'to': 0.25},
                {'from': 0.25, 'to': 0.50},
                {'from': 0.50, 'to': 0.75},
                {'from': 0.75},
            ])

        completed_enrollments\
            .bucket('completed_ids', 'terms', field='id', size=9999)

        response = search.execute().to_dict()
        aggs = response['aggregations']

        enrollment_total = aggs['total']['value']
        enrollment_avg = aggs['performance_avg']['value']
        enrollment_categories = aggs['categories']['buckets']

        enrollment_completed_total = aggs['completed_enrollments']['total']['value']
        enrollment_completed_avg = aggs['completed_enrollments']['performance_avg']['value']
        enrollment_completed_ranges = aggs['completed_enrollments']['performance_ranges']['buckets']

        enrollments_ids = [enrollment['_id'] for enrollment in response['hits']['hits']]
        completed_ids = [bucket['key'] for bucket in aggs['completed_enrollments']['completed_ids']['buckets']]

        value['stats']['enrollments'] = {
            'total': enrollment_total,
            'performance_avg': enrollment_avg,
            'categories': enrollment_categories,
            'completed': {
                'total': enrollment_completed_total,
                'ratio': enrollment_completed_total / enrollment_total if enrollment_total else 0,
                'performance_avg': enrollment_completed_avg,
                'ranges': enrollment_completed_ranges
            }
        }

        # Activities Stats
        filter = self.esf.make_simple_filter(**filters.__dict__, data_size=0)
        search = self.esf.make_search_activities(filter, range_field='start_date')
        search = search.filter('term', user_id=user_id)

        search.aggs\
            .metric('total_seconds', 'sum', field='time_in')\
            .bucket('content_types', 'terms', field="content_type_id", size=100)\
            .bucket('name', 'terms', field="content_type_name")

        enrollment_activities = search.aggs\
            .bucket('enrollment_activities', 'filter', filter=Q('terms', enrollment_id=enrollments_ids))\
            .metric('total_seconds', 'sum', field='time_in')

        enrollment_activities\
            .bucket('completed_activities', 'filter', filter=Q('terms', enrollment_id=completed_ids))\
            .metric('total_seconds', 'sum', field='time_in')

        search.aggs\
            .bucket('course_activities', 'filter', filter=Q('exists', field='course_id'))\
            .metric('total_activities', 'value_count', field='id')\
            .metric('total_courses', 'cardinality', field='course_id')\
            .bucket('recent_activities', 'filters', filters={
                'last_7_days': Q('range', created_date={'gte': 'now-7d'}),
                'last_30_days': Q('range', created_date={'gte': 'now-30d'}),
                'previous_7_days': Q('range', created_date={'gte': 'now-14d', 'lt': 'now-7d'}),
                'previous_30_days': Q('range', created_date={'gte': 'now-60d', 'lt': 'now-30d'}),
            })\
            .metric('unique_courses', 'cardinality', field='course_id')

        search.aggs\
            .bucket('pulse_activities', 'filter', filter=Q('exists', field='pulse_id'))\
            .metric('total_activities', 'value_count', field='id')\
            .metric('total_pulses', 'cardinality', field='pulse_id')\
            .bucket('recent_activities', 'filters', filters={
                'last_7_days': Q('range', created_date={'gte': 'now-7d'}),
                'last_30_days': Q('range', created_date={'gte': 'now-30d'}),
                'previous_7_days': Q('range', created_date={'gte': 'now-14d', 'lt': 'now-7d'}),
                'previous_30_days': Q('range', created_date={'gte': 'now-60d', 'lt': 'now-30d'}),
            })\
            .metric('unique_pulses', 'cardinality', field='pulse_id')

        # print(f'search: {json.dumps(search.to_dict())}')
        response = search.execute().to_dict()
        aggs = response['aggregations']

        activities_total_seconds = aggs['total_seconds']['value']
        activities_enrollments_total = aggs['enrollment_activities']['doc_count']
        activities_enrollments_total_seconds = aggs['enrollment_activities']['total_seconds']['value']
        activities_completed_enrollments_total = aggs['enrollment_activities']['completed_activities']['doc_count']
        activities_completed_enrollments_total_seconds = aggs['enrollment_activities']['completed_activities']['total_seconds']['value']
        activities_pulses_total_activities = aggs['pulse_activities']['total_activities']['value']
        activities_pulses_total_pulses = aggs['pulse_activities']['total_pulses']['value']
        activities_pulses_recent = aggs['pulse_activities']['recent_activities']['buckets']
        activities_courses_total_activities = aggs['course_activities']['total_activities']['value']
        activities_courses_total_courses = aggs['course_activities']['total_courses']['value']
        activities_courses_recent = aggs['course_activities']['recent_activities']['buckets']
        activities_content_types = aggs['content_types']['buckets']

        value['stats']['activities'] = {
            'total_seconds': activities_total_seconds,
            'enrollments_total_activities': activities_enrollments_total,
            'enrollments_total_seconds': activities_enrollments_total_seconds,
            'completed_enrollments_total_activities': activities_completed_enrollments_total,
            'completed_enrollments_total_seconds': activities_completed_enrollments_total_seconds,
            'pulses_total_activities': activities_pulses_total_activities,
            'pulses_total_pulses': activities_pulses_total_pulses,
            'pulses_recent_activities': activities_pulses_recent,
            'courses_total_activities': activities_courses_total_activities,
            'courses_total_courses': activities_courses_total_courses,
            'courses_recent_activities': activities_courses_recent,
            'content_types': activities_content_types,
        }

        # Answers stats
        filter = self.esf.make_simple_filter(**filters.__dict__, data_size=0)
        search = self.esf.make_search_answers(filter, range_field='created_date')
        search = search.filter('term', user_id=user_id)

        search.aggs.metric('total_answers', 'value_count', field='id')\
            .metric('total_questions', 'cardinality', field='question_id')\
            .metric('total_exams', 'cardinality', field='exam_id')\
            .bucket('correct_answers', 'filter', filter=Q('term', is_ok=True))

        response = search.execute().to_dict()
        aggs = response['aggregations']
        answer_total_answers = aggs['total_answers']['value']
        answer_total_questions = aggs['total_questions']['value']
        answer_total_exams = aggs['total_exams']['value']
        answer_correct_answers = aggs['correct_answers']['doc_count']
        answer_correct_ratio = answer_correct_answers / answer_total_answers if answer_total_answers else 0

        value['stats']['answers'] = {
            'total_exams': answer_total_exams,
            'total_questions': answer_total_questions,
            'total_answers': answer_total_answers,
            'correct_answers': answer_correct_answers,
            'correct_ratio': answer_correct_ratio,
        }

        # Courses + Channels + Pulses Stats
        filter = self.esf.make_simple_filter(**filters.__dict__, data_size=0)

        search = self.esf.make_search_courses(filter, range_field='created_date')
        search = search.filter('term', user_creator__id=user_id)
        search.aggs.metric('total', 'value_count', field='id')
        response = search.execute().to_dict()
        courses_created = response['aggregations']['total']['value']

        search = self.esf.make_search_courses(filter, range_field='created_date')
        search = search.filter('nested', path='contributors', query=Q('match', contributors__user_id=user_id))
        search.aggs.metric('total', 'value_count', field='id')
        response = search.execute().to_dict()
        courses_contributed = response['aggregations']['total']['value']

        search = self.esf.make_search_channels(filter, range_field='created_date')
        search = search.filter('term', user_creator__id=user_id)
        search.aggs.metric('total', 'value_count', field='id')
        response = search.execute().to_dict()
        channels_created = response['aggregations']['total']['value']

        search = self.esf.make_search_pulses(filter, range_field='created_date')
        search = search.filter('term', user_creator__id=user_id)
        search.aggs.metric('total', 'value_count', field='id')
        response = search.execute().to_dict()
        pulses_created = response['aggregations']['total']['value']

        value['stats']['courses'] = {
            'courses_created': courses_created,
            'courses_contributed': courses_contributed,
            'channels_created': channels_created,
            'pulses_created': pulses_created,
        }

        return value

    def get_user_enrollments(self, user_id, filters: UserEnrollmentsFilter) -> dict:
        # print(f'[User::v2] get_user_enrollments - user_id: {user_id} -  filter: {filters}')

        search = self.esf.make_search_enrollments(filters)
        search = search.filter('term', user_id=user_id)

        if filters.performance_min is not None or filters.performance_max is not None:
            search = search.filter('range', performance={
                'gte': filters.performance_min,
                'lte': filters.performance_max
            })

        if filters.start_date_min or filters.start_date_max:
            search = search.filter('range', start_date={
                'gte': filters.start_date_min,
                'lte': filters.start_date_max
            })

        if filters.end_date_min or filters.end_date_max:
            search = search.filter('range', end_date={
                'gte': filters.end_date_min,
                'lte': filters.end_date_max
            })

        if filters.course_category:
            categories = filters.course_category.split(',')
            search = search.filter('terms', category_id=categories)

        if filters.search_term:
            query = self.esf.make_query_search_as_you_type(search_field="search_terms", search_term=filters.search_term)
            search = search.query(query)

        idx_start = filters.page * filters.page_size
        idx_end = idx_start + filters.page_size
        search = search[idx_start:idx_end]

        if filters.sort:
            search = search.sort(filters.sort)

        search.aggs.metric('total', 'value_count', field='id')

        response = search.execute().to_dict()

        total = response['aggregations']['total']['value']
        data = response['hits']['hits']

        enrollments_ids = [enrollment['_id'] for enrollment in data]
        courses_ids = [enrollment['_source']['course_id'] for enrollment in data]

        # Init stats
        for enrollment in data:
            enrollment['stats'] = {
                'ratings': {
                    'value': 0,
                },
                'activities': {
                    'total_seconds': 0
                },
            }

        # Course Ratings Stats
        filter = self.esf.make_simple_filter(workspace_id=filters.workspace_id, data_size=9999)
        search = self.esf.make_search_ratings(filter)
        search = search.filter('term', user_id=user_id)\
            .filter('terms', course_id=courses_ids)

        response = search.execute().to_dict()

        ratings = response['hits']['hits']

        for enrollment in data:
            course_id = enrollment['_source']['course_id']
            rating_doc = next((rating for rating in ratings if rating['_source']['course_id'] == course_id), {})
            if not rating_doc:
                continue
            rating_value = rating_doc['_source']['rating']

            enrollment['stats']['ratings'] = {
                'value': rating_value,
            }

        # Activities Stats
        filter = self.esf.make_simple_filter(workspace_id=filters.workspace_id, data_size=0)
        search = self.esf.make_search_activities(filter)
        search = search.filter('terms', enrollment_id=enrollments_ids)

        search.aggs.bucket('per_enrollment', 'terms', field='enrollment_id')\
            .metric('total_seconds', 'sum', field='time_in')

        response = search.execute().to_dict()

        buckets = response['aggregations']['per_enrollment']['buckets']

        for enrollment in data:
            enrollment_id = enrollment['_id']
            aggs = next((bucket for bucket in buckets if bucket['key'] == enrollment_id), {})
            if not aggs:
                continue
            activities_total_seconds = aggs['total_seconds']['value']

            enrollment['stats']['activities'] = {
                'total_seconds': activities_total_seconds,
            }

        return {
            'total': total,
            'data': data
        }

    def get_total_users(self, filters: UsersStatsFilter) -> dict:
        s = self.esf.make_search_users(filters, range_field='created_date')

        s.aggs\
            .metric('total', 'value_count', field='id')\
            .bucket('agg_users', 'date_histogram', field='created_date', calendar_interval=filters.interval)
        response = s.execute().to_dict()

        return {
            'data': response['hits']['hits'],
            'total': response['aggregations']['total']['value'],
            'aggs': response['aggregations']['agg_users']
        }

    def get_active_users(self, filters: UsersStatsFilter) -> dict:
        s = self.esf.make_search_activities(filters, range_field="start_date")
        s.aggs\
            .metric('total', 'value_count', field='id')\
            .metric('active_users', 'cardinality', field='user_id')

        s.aggs\
            .bucket('activity_histogram', 'date_histogram', field='start_date', calendar_interval=filters.interval)\
            .metric('users_per_interval', 'cardinality', field='user_id')

        s.aggs\
            .bucket('avg_users_per_interval', 'avg_bucket', buckets_path='activity_histogram>users_per_interval')

        response = s.execute().to_dict()

        return {
            'data': response['hits']['hits'],
            'total': response['aggregations']['total']['value'],
            'aggs': response['aggregations']
        }

    def get_distribution_users_enrollments(self, filters: UsersStatsFilter) -> dict:
        # print(f'[User::v2] get_distribution_users_enrollments - filter: {filters}')

        s = self.esf.make_search_enrollments(filters, range_field="start_date")

        s.aggs.bucket('per_user', 'terms', field='user_id', size=9999999)\
            .metric('total', 'value_count', field='id')\
            .bucket('per_status', 'filters', filters={
                'started': Q('term', status='STARTED'),
                'completed': Q('term', status='COMPLETED'),
            })

        response = s.execute().to_dict()

        ranges = [
            {'range': '0', 'total': 0, 'started': 0, 'completed': 0},
            {'range': '1-3', 'total': 0, 'started': 0, 'completed': 0},
            {'range': '3-5', 'total': 0, 'started': 0, 'completed': 0},
            {'range': '5-10', 'total': 0, 'started': 0, 'completed': 0},
            {'range': '10-15', 'total': 0, 'started': 0, 'completed': 0},
            {'range': '15+', 'total': 0, 'started': 0, 'completed': 0},
        ]

        def range_index(count: int) -> int:
            if count == 0:
                return 0
            if count <= 3:
                return 1  # 1-3
            if count <= 5:
                return 2  # 3-5
            if count <= 10:
                return 3  # 5-10
            if count <= 15:
                return 4  # 10-15
            return 5  # 15+

        for user_bucket in response['aggregations']['per_user']['buckets']:
            total = user_bucket['total']['value']
            started = user_bucket['per_status']['buckets']['started']['doc_count']
            completed = user_bucket['per_status']['buckets']['completed']['doc_count']

            ranges[range_index(total)]['total'] += 1
            ranges[range_index(started)]['started'] += 1
            ranges[range_index(completed)]['completed'] += 1

        return {
            'stats': {
                'enrollments_per_user': ranges
            }
        }

    def get_engagement_rate(self, filters: UsersStatsFilter) -> dict:
        # print(f'[User::v2] get_engagement_rate - filter: {filters}')

        if not filters.end_date:
            filters.end_date = str(datetime.date.today())
        if not filters.start_date:
            filters.start_date = str(datetime.date(*map(int, filters.end_date.split('-'))) - datetime.timedelta(days=30))

        if filters.start_date > filters.end_date:
            raise ValueError("start_date cannot be greater than end_date")
        # Company Users
        simple_filter = self.esf.make_simple_filter(workspace_id=filters.workspace_id)
        search = self.esf.make_search_users(simple_filter)
        search = search.source(includes=['id'])

        company_users_list = self.esf.do_search_all(search)
        company_users_total = len(company_users_list)
        # print(f'company users: {company_users_total}')

        # Stats for Courses
        simple_filter = self.esf.make_simple_filter(workspace_id=filters.workspace_id, data_size=0)
        search = self.esf.make_search_courses(simple_filter)
        search.aggs.metric('total', 'value_count', field='id')
        response = search.execute().to_dict()
        company_courses_total = response['aggregations']['total']['value']
        # print(f'company courses: {company_courses_total}')

        # Stats for Pulses
        simple_filter = self.esf.make_simple_filter(workspace_id=filters.workspace_id, data_size=0)
        search = self.esf.make_search_activities(simple_filter)
        search = search.filter('exists', field='pulse_id')

        search.aggs.metric('unique_pulses_count', 'cardinality', field='pulse_id')

        response = search.execute().to_dict()
        company_pulses_total = response['aggregations']['unique_pulses_count']['value']
        # print(f'company pulses: {company_pulses_total}')

        # Stats for Completed Enrollments per User
        search = self.esf.make_search_enrollments(filters, 'start_date')
        search = search.filter('term', status='COMPLETED')
        search.aggs\
            .bucket('per_user', 'terms', field='user_id', size=999999)\
            .metric('enrollments_total', 'cardinality', field='id')\
            .metric('performance_avg', 'avg', field='performance')

        enrollments_response = search.execute().to_dict()

        # Stats for Activities per User
        search = self.esf.make_search_activities(filters, 'start_date')

        per_user = search.aggs.bucket('per_user', 'terms', field='user_id', size=999999)

        per_user\
            .bucket('pulses_activities', 'filter', filter=Q('exists', field='pulse_id'))\
            .metric('pulses_total', 'cardinality', field='pulse_id')\
            .bucket('pulses_per_day', 'date_histogram', field='start_date', calendar_interval='day', min_doc_count='1')

        per_user\
            .bucket('courses_activities', 'filter', filter=Q('exists', field='course_id'))\
            .metric('courses_total', 'cardinality', field='course_id')\
            .bucket('courses_per_day', 'date_histogram', field='start_date', calendar_interval='day', min_doc_count='1')

        activities_response = search.execute().to_dict()

        # Engagement calc
        engagement_map = dict()
        engagement_sum = 0

        for user in company_users_list:
            user_id = user['_source']['id']

            buckets = activities_response['aggregations']['per_user']['buckets']
            user_activities = next((activities for activities in buckets if activities['key'] == user_id), {})

            pulses_consumed_total_days = len(user_activities.get('pulses_activities', {}).get('pulses_per_day', {}).get('buckets', []))
            courses_consumed_total_days = len(user_activities.get('courses_activities', {}).get('courses_per_day', {}).get('buckets', []))
            sum_total_days = pulses_consumed_total_days + courses_consumed_total_days

            if sum_total_days == 0:
                engagement_map[user_id] = 0
                continue

            buckets = enrollments_response['aggregations']['per_user']['buckets']
            user_enrollments = next((enrollments for enrollments in buckets if enrollments['key'] == user_id), {})

            courses_completed_total = user_enrollments.get('enrollments_total', {}).get('value', 0)
            courses_performance_avg = user_enrollments.get('performance_avg', {}).get('value', 0)
            pulses_consumed_total = user_activities.get('pulses_activities', {}).get('pulses_total', {}).get('value', 0)

            if company_courses_total < 10:
                courses_completed_total = 4
                courses_performance_avg = 1

            if company_pulses_total < 10:
                pulses_consumed_total = 4

            courses_completed_total = max(courses_completed_total, 4)
            pulses_consumed_total = max(pulses_consumed_total, 4)
            sum_total_days = max(sum_total_days, 10)
            courses_performance_avg = courses_performance_avg or 0

            # Parameters
            multiplier_courses_consumed = 0.25
            multiplier_pulses_consumed = 0.25
            multiplier_days_consumed = 0.1

            values = [
                courses_performance_avg,
                courses_completed_total * multiplier_courses_consumed,
                pulses_consumed_total * multiplier_pulses_consumed,
                sum_total_days * multiplier_days_consumed
            ]
            weight = [3, 2, 1, 2]

            engagement = sum(values[i] * weight[i] for i in range(len(values))) / sum(weight)
            engagement_map[user_id] = engagement
            engagement_sum += engagement

        # print('-----\n' + str(engagement_map))
        avg_engagement_rate = engagement_sum / company_users_total if company_users_total else 0
        return {
            'stats': {
                'engagement_rate_avg': avg_engagement_rate
            }
        }

    def get_content_consumed_per_user(self, filters: UsersStatsFilter) -> dict:
        # print(f'[User::v2] get_content_consumed_per_user - filter: {filters}')

        s = self.esf.make_search_activities(filters, range_field='created_date')

        s.aggs\
            .metric('total', 'value_count', field='id')\
            .metric('total_users', 'cardinality', field='user_id')\
            .metric('total_activities_seconds', 'sum', field='time_in')

        response = s.execute().to_dict()

        total_users = response['aggregations']['total_users']['value']
        total_seconds = response['aggregations']['total_activities_seconds']['value']

        hours_per_user = (total_seconds / (60 * 60)) / total_users if total_users else 0

        return {
            'data': response['hits']['hits'],
            'total': response['aggregations']['total']['value'],
            'aggs': response['aggregations'],
            'stats': {
                'avg_hours_per_user': hours_per_user
            }
        }

    def get_users_creators(self, filters: UsersStatsFilter) -> dict:
        # print(f'[User::v2] get_users_creators - filter: {filters}')

        search = self.esf.make_search_courses(filters, range_field='created_date')
        search.aggs.bucket('users_creators', 'terms', field='user_creator.id', size=9999999)
        response = search.execute().to_dict()
        courses_users_creators_ids = [bucket['key'] for bucket in response['aggregations']['users_creators']['buckets']]

        search = self.esf.make_search_channels(filters, range_field='created_date')
        search.aggs.bucket('users_creators', 'terms', field='user_creator.id', size=9999999)
        response = search.execute().to_dict()
        channels_users_creators_ids = [bucket['key'] for bucket in response['aggregations']['users_creators']['buckets']]

        unique_ids = set(courses_users_creators_ids)
        unique_ids.update(channels_users_creators_ids)

        return {
            'total': len(unique_ids),
            'data': list(unique_ids),
            'aggs': {
                'courses_creators': courses_users_creators_ids,
                'channels_creators': channels_users_creators_ids,
            }
        }

    def get_users_enrollment_categories(self, filters: UsersStatsFilter) -> dict:
        # print(f'[User::v2] get_users_enrollment_categories - filter: {filters}')

        s = self.esf.make_search_enrollments(filters, range_field='created_date')
        s.aggs\
            .metric('total', 'value_count', field='id')\
            .bucket('categories', 'terms', field='category_id', size=100)\
            .bucket('name', 'terms', field="category_name.keyword")

        response = s.execute().to_dict()

        return {
            'data': response['hits']['hits'],
            'total': response['aggregations']['total']['value'],
            'aggs': response['aggregations']
        }

    def get_users_activity_content_types(self, filters: UsersStatsFilter) -> dict:
        # print(f'[User::v2] get_users_activity_content_types - filter: {filters}')

        s = self.esf.make_search_activities(filters, range_field='created_date')

        s.aggs\
            .metric('total', 'value_count', field='id')\
            .bucket('content_types', 'terms', field="content_type_id", size=100)\
            .bucket('name', 'terms', field="content_type_name")

        response = s.execute().to_dict()

        return {
            'data': response['hits']['hits'],
            'total': response['aggregations']['total']['value'],
            'aggs': response['aggregations'],
        }

    def get_users_list(self, filters: UsersListingFilter) -> dict:
        # print(f'[User::v2] get_users_list - filter: {filters}')

        search = self.esf.make_search_users(filters)

        # Select fields
        search = search.source(excludes=['user_role_company', 'user_role_workspace'])

        if filters.leader:
            leaders = filters.leader.split(',')
            search = search.filter('terms', leader_id=leaders)

        if filters.completion_rate_min is not None or filters.completion_rate_max is not None:
            search = search.filter('nested', path='user_progress_workspace', query=(
                Q('term', user_progress_workspace__workspace_id=filters.workspace_id) & \
                Q('range', user_progress_workspace__enrollments_completed_ratio={
                    'gte': filters.completion_rate_min,
                    'lte': filters.completion_rate_max
                }
            )))

        if filters.search_term:
            query = self.esf.make_query_search_as_you_type(search_field="search_terms", search_term=filters.search_term)
            search = search.query(query)

        idx_start = filters.page * filters.page_size
        idx_end = idx_start + filters.page_size
        search = search[idx_start:idx_end]

        if filters.sort:
            if 'completion_rate' in filters.sort:
                search = search.sort({
                    'user_progress_workspace.enrollments_completed_ratio': {
                        'order': 'desc' if filters.sort.startswith('-') else 'asc',
                        'nested': {
                            'path': 'user_progress_workspace',
                            'filter': {
                                'term': { 'user_progress_workspace.workspace_id': filters.workspace_id }
                            }
                        }
                    }
                })
            else:
                search = search.sort(filters.sort)

        search.aggs.metric('total', 'value_count', field='id')

        response = search.execute().to_dict()

        total = response['aggregations']['total']['value']
        data = response['hits']['hits']

        # Compute stats for each user
        for user in data:
            user['stats'] = {
                'enrollments': {
                    'total': 0,
                    'completed': 0,
                    'completed_ratio': 0,
                },
                'profile': {
                    "job": None,
                    "area_of_activity": None,
                    "manager": None,
                    "director": None,
                },
            }

            # Enrollments
            profile = next((progress for progress in user['_source']['user_progress_workspace'] if progress['workspace_id'] == filters.workspace_id), None)
            if profile:
                user['stats']['enrollments'] = {
                    'total': profile['enrollments_total'],
                    'completed': profile['enrollments_completed'],
                    'completed_ratio': profile['enrollments_completed_ratio'],
                }
            del user['_source']['user_progress_workspace']

            # Profile
            profile = next((profile for profile in user['_source']['user_profile_workspace'] if profile['workspace_id'] == filters.workspace_id), None)
            if profile:
                user['stats']['profile'] = profile
            del user['_source']['user_profile_workspace']

        return {
            'total': total,
            'data': data
        }
