from typing import List, Optional

from domain.common.utils import normalize_end_date, normalize_start_date


class UserFilter:
    workspace_id: str
    workspace_ids: Optional[List]

    def __init__(self, workspace_id=None, workspace_ids=None):
        self.workspace_id = workspace_id
        self.workspace_ids = workspace_ids

    def get_workspace_ids(self) -> List[str]:
        return self.workspace_ids or [self.workspace_id]

    def __str__(self):
        return '<%s(%s)>' % (self.__class__.__name__, ', '.join([f'{k}={v}' for (k, v) in self.__dict__.items()]))


class UserDataFilter(UserFilter):
    def __init__(self, workspace_id=None, start_date=None, end_date=None):
        super().__init__(workspace_id)

        self.start_date = start_date
        self.end_date = end_date

    def get_start_date(self) -> str:
        return normalize_start_date(self.start_date)

    def get_end_date(self) -> str:
        return normalize_end_date(self.end_date)

class UsersStatsFilter(UserFilter):
    def __init__(
        self, workspace_id=None, start_date=None, end_date=None, interval='month', data_size=0, workspace_ids=None
    ):
        super().__init__(workspace_id, workspace_ids)

        self.start_date = start_date
        self.end_date = end_date
        self.interval = interval
        self.data_size = data_size

    def get_start_date(self) -> str:
        return normalize_start_date(self.start_date)

    def get_end_date(self) -> str:
        return normalize_end_date(self.end_date)


class UsersListingFilter(UserFilter):
    def __init__(self, workspace_id=None, search_term=None, leader=None, completion_rate_min=None, completion_rate_max=None, page=0, page_size=10, sort=None):
        super().__init__(workspace_id)

        self.search_term = search_term
        self.leader = leader
        self.completion_rate_min = completion_rate_min
        self.completion_rate_max = completion_rate_max

        self.page = page
        self.page_size = page_size
        self.sort = sort


class UserEnrollmentsFilter(UserFilter):
    def __init__(self, workspace_id=None,
                search_term=None, course_category=None,
                performance_min=None, performance_max=None,
                start_date_min=None, start_date_max=None,
                end_date_min=None, end_date_max=None,
                page=0, page_size=10, sort=None):
        super().__init__(workspace_id)

        self.search_term = search_term
        self.course_category = course_category

        self.performance_min = performance_min
        self.performance_max = performance_max
        self.start_date_min = start_date_min
        self.start_date_max = start_date_max
        self.end_date_min = end_date_min
        self.end_date_max = end_date_max

        self.page = page
        self.page_size = page_size
        self.sort = sort
