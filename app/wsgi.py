import logging

import database
from elasticapm.contrib.flask import ElasticAPM
from keeps_flask.app import initialize, run
from modules.app_module import AppModule
from modules.config_module import ConfigModule
from modules.db_module import DatabaseModule
from modules.es_module import ElasticsearchModule

injector, app = initialize([ConfigModule, AppModule, DatabaseModule, ElasticsearchModule])

apm = ElasticAPM(app, logging=logging.ERROR)


def run_server():
    run(injector)


@app.teardown_appcontext
def shutdown_session(exception=None):
    database.app_session.remove()


if __name__ == "__main__":
    run_server()
