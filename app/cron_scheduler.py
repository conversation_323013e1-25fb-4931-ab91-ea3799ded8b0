import datetime

import tasks
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.schedulers.blocking import BlockingScheduler
from config.default import Config
from tasks.report import cancel_older_processing_reports, delete_older_reports


def start_background_schedulers():
    scheduler = BackgroundScheduler(timezone=Config.TIMEZONE)
    shift_hours_for_stage = 0
    if Config.DEBUG:
        shift_hours_for_stage = 4

    scheduler.add_job(
        delete_older_reports,
        trigger='cron',
        day='*',
        hour='0'
    )
    scheduler.add_job(
        tasks.start_delete_temp_files,
        trigger='cron',
        day='*',
        hour=0 + shift_hours_for_stage
    )

    scheduler.add_job(
        cancel_older_processing_reports,
        trigger='cron',
        day='*',
        hour='*'
    )
    # --- Celery indexers disabled ---
    # scheduler.add_job(
    #     tasks.start_indexer_courses,
    #     trigger='cron',
    #     day='*',
    #     hour=1 + shift_hours_for_stage
    # )
    # scheduler.add_job(
    #     tasks.start_indexer_users,
    #     trigger='cron',
    #     day='*/2',
    #     hour=2 + shift_hours_for_stage
    # )

    scheduler.start()


def health_check():
    print(f"{datetime.datetime.now()} Schedulers alive")


if __name__ == "__main__":
    print("Running schedulers...")
    start_background_schedulers()
    LOCK_SCHEDULER = BlockingScheduler(timezone="America/Sao_Paulo")
    LOCK_SCHEDULER.add_job(health_check, trigger="interval", minutes=60)
    LOCK_SCHEDULER.start()
