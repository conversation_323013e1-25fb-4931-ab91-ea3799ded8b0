import colorsys


def hex_to_rgb(hex_color):
    hex_color = hex_color.lstrip('#')
    return tuple(int(hex_color[i:i + 2], 16) for i in (0, 2, 4))


def rgb_to_hex(rgb_color):
    return '#{:02x}{:02x}{:02x}'.format(*rgb_color)


def generate_palette_colors(hex_color, num_colors=8):
    # Convert the input hex color to RGB
    r, g, b = hex_to_rgb(hex_color)

    # Convert RGB to HSL
    h, _, s = colorsys.rgb_to_hls(r / 255.0, g / 255.0, b / 255.0)

    palette_colors = []

    # Define a range for lightness values
    lightness_range = [0.2, 0.5]

    for i in range(num_colors):
        # Calculate new lightness value based on the index and the total number of colors
        l_new = lightness_range[0] + (i / (num_colors - 1)) * (lightness_range[1] - lightness_range[0])

        # Convert HSL back to RGB
        rgb_new = colorsys.hls_to_rgb(h, l_new, s)
        hex_new = rgb_to_hex((int(rgb_new[0] * 255), int(rgb_new[1] * 255), int(rgb_new[2] * 255)))
        palette_colors.append(hex_new)

    return palette_colors


# Input hexadecimal color code
input_color = "#FF5733"

# Generate a palette of colors in the same gradient
palette_colors = generate_palette_colors(input_color, num_colors=8)

# Print the generated palette colors
for color in palette_colors:
    print(color)
