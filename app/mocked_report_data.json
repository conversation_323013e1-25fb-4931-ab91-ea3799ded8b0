{"konquest_company_missions": [{"mission_id": "f2c67b63-b883-4480-9f4b-ada8765f6a79", "mission_name": "Liderança Situacional", "user_creator": "Keeps Admin", "date_updated": "2020-11-11 12:01", "date_created": "2019-08-20 11:44", "status": "EM_REVISÃO", "mission_type": "Fechado Para Empresa", "mission_category": "Liderança", "group_name": "None", "duration(sec)": "4032.0", "duration(h)": "1.12", "enrollments": "1"}, {"mission_id": "ae486db0-dc9c-4eba-a179-c658bcc10bc4", "mission_name": "Você é do Tamanho dos seus Sonhos", "user_creator": "Gabriel Keeps", "date_updated": "2020-06-05 19:49", "date_created": "2020-06-05 17:54", "status": "FEITA(O)", "mission_type": "Aberto Para Empresa", "mission_category": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "duration(sec)": "11731.0", "duration(h)": "3.26", "enrollments": "1"}, {"mission_id": "36c48bbc-547a-4e50-b07e-7481c38e7d9f", "mission_name": "<PERSON> - <PERSON><PERSON><PERSON> em <PERSON>", "user_creator": "Gabriel Keeps", "date_updated": "2020-06-08 13:17", "date_created": "2020-06-05 18:30", "status": "FEITA(O)", "mission_type": "Aberto Para Empresa", "mission_category": "Desenvolvimento pessoal", "group_name": "None", "duration(sec)": "28506.0", "duration(h)": "7.92", "enrollments": "1"}, {"mission_id": "4cfcf6ee-e33e-4329-90c1-72e2389accc0", "mission_name": "Como vender mais pelo facebook", "user_creator": "Keeps Admin", "date_updated": "2020-06-07 22:48", "date_created": "2020-04-22 12:29", "status": "FEITA(O)", "mission_type": "Aberto Para Empresa", "mission_category": "<PERSON><PERSON><PERSON>", "group_name": "Grupo Missão Paga", "duration(sec)": "2106.0", "duration(h)": "0.59", "enrollments": "6"}, {"mission_id": "8dbc4f4b-1724-4df9-b77a-efaa98e860f9", "mission_name": "Como vender mais pelo instagram", "user_creator": "Keeps Admin", "date_updated": "2020-06-07 22:34", "date_created": "2020-04-22 12:35", "status": "FEITA(O)", "mission_type": "Aberto Para Empresa", "mission_category": "<PERSON><PERSON><PERSON>", "group_name": "<PERSON><PERSON><PERSON>", "duration(sec)": "1729.0", "duration(h)": "0.48", "enrollments": "10"}, {"mission_id": "665cccc1-ca81-485c-90f6-b31f64839631", "mission_name": "Relação do ensino inovador com bioconstrução", "user_creator": "<PERSON><PERSON>", "date_updated": "2020-05-20 17:29", "date_created": "2020-04-08 20:12", "status": "FEITA(O)", "mission_type": "Aberto Para Empresa", "mission_category": "Design", "group_name": "None", "duration(sec)": "None", "duration(h)": "None", "enrollments": "2"}, {"mission_id": "5d9d27c1-5999-404f-ae43-47a2bc3af8a4", "mission_name": "Programa de Aceleração do Crescimento para Vendas", "user_creator": "<PERSON>", "date_updated": "2020-10-12 20:52", "date_created": "2020-10-12 18:41", "status": "FEITA(O)", "mission_type": "Aberto Para Empresa", "mission_category": "<PERSON><PERSON><PERSON>", "group_name": "None", "duration(sec)": "27315.0", "duration(h)": "7.59", "enrollments": "2"}, {"mission_id": "38894c01-6abb-4865-8292-8dfb117cfa7c", "mission_name": "<PERSON><PERSON> da Mente Milionária", "user_creator": "Gabriel Keeps", "date_updated": "2020-06-08 13:15", "date_created": "2020-06-05 17:06", "status": "FEITA(O)", "mission_type": "Aberto Para Empresa", "mission_category": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "group_name": "None", "duration(sec)": "26967.0", "duration(h)": "7.49", "enrollments": "None"}, {"mission_id": "6dc66b3b-4a90-4baa-8091-9af9de01a795", "mission_name": "<PERSON> - <PERSON>", "user_creator": "Gabriel Keeps", "date_updated": "2020-06-05 19:48", "date_created": "2020-06-05 17:17", "status": "FEITA(O)", "mission_type": "Aberto Para Empresa", "mission_category": "Comunicações", "group_name": "None", "duration(sec)": "10374.0", "duration(h)": "2.88", "enrollments": "None"}, {"mission_id": "7a02e59e-81d8-4a08-9e31-6ec60410d268", "mission_name": "A Arte da Guerra - Curso e Áudio Book", "user_creator": "<PERSON>l <PERSON>", "date_updated": "2020-06-22 12:15", "date_created": "2020-05-27 12:30", "status": "FEITA(O)", "mission_type": "Aberto Para Empresa", "mission_category": "Liderança", "group_name": "None", "duration(sec)": "5414.0", "duration(h)": "1.50", "enrollments": "3"}, {"mission_id": "fd7930a7-123f-421d-bef4-ff3af80944ff", "mission_name": "Casais Inteligentes Enriquecem Juntos", "user_creator": "<PERSON><PERSON><PERSON><PERSON>", "date_updated": "2020-06-05 19:44", "date_created": "2020-06-03 17:50", "status": "EM_REVISÃO", "mission_type": "Aberto Para Empresa", "mission_category": "Educação Financeira", "group_name": "None", "duration(sec)": "14712.0", "duration(h)": "4.09", "enrollments": "None"}, {"mission_id": "5ef34e4d-dbb0-4b99-b14c-bd227a008b90", "mission_name": "Curso Fundamentos de Professional Services", "user_creator": "<PERSON>", "date_updated": "2020-05-11 01:56", "date_created": "2020-04-11 02:44", "status": "FEITA(O)", "mission_type": "Aberto Para Empresa", "mission_category": "Management", "group_name": "GrupoTeste", "duration(sec)": "2348.0", "duration(h)": "0.65", "enrollments": "18"}, {"mission_id": "98791d04-238b-4629-87bd-0b7cd12d8ca6", "mission_name": "Visão Sistêmica", "user_creator": "Gabriel Keeps", "date_updated": "2020-06-08 13:30", "date_created": "2020-06-05 19:19", "status": "FEITA(O)", "mission_type": "Aberto Para Empresa", "mission_category": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "group_name": "None", "duration(sec)": "3534.0", "duration(h)": "0.98", "enrollments": "1"}, {"mission_id": "0608a187-81d8-4152-b8a8-4837d31c6e87", "mission_name": "Como Fazer Amigos & Influenciar Pessoas", "user_creator": "Gabriel Keeps", "date_updated": "2020-06-05 19:26", "date_created": "2020-06-05 17:24", "status": "FEITA(O)", "mission_type": "Aberto Para Empresa", "mission_category": "Comunicações", "group_name": "None", "duration(sec)": "32963.0", "duration(h)": "9.16", "enrollments": "2"}, {"mission_id": "c449d50d-b648-4191-8e12-f8e0d4c98e79", "mission_name": "Cooperativismo Contemporâneo e Planejamento Estratégico", "user_creator": "Keeps Admin", "date_updated": "2020-06-04 15:04", "date_created": "2020-05-30 12:48", "status": "FEITA(O)", "mission_type": "Aberto Para Empresa", "mission_category": "Estratégia", "group_name": "None", "duration(sec)": "3527.0", "duration(h)": "0.98", "enrollments": "1"}, {"mission_id": "11a78ec9-8bf1-474c-b3a2-2a1160793e90", "mission_name": "<PERSON><PERSON> da Mente Milionária", "user_creator": "<PERSON><PERSON><PERSON><PERSON>", "date_updated": "2020-06-03 19:17", "date_created": "2020-06-03 18:01", "status": "EM_REVISÃO", "mission_type": "Aberto Para Empresa", "mission_category": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "group_name": "None", "duration(sec)": "26967.0", "duration(h)": "7.49", "enrollments": "None"}, {"mission_id": "910acbf0-a32f-4ee5-9c6b-64b9f37ed6f4", "mission_name": "IA para Educação", "user_creator": "Keeps Admin", "date_updated": "2020-11-18 20:47", "date_created": "2020-10-16 16:53", "status": "FEITA(O)", "mission_type": "Aberto Para Empresa", "mission_category": "Educação Financeira", "group_name": "None", "duration(sec)": "2617.0", "duration(h)": "0.73", "enrollments": "1"}, {"mission_id": "b17834db-e3fd-429e-843c-13711c07134d", "mission_name": "Konquest na prática", "user_creator": "<PERSON>", "date_updated": "2020-07-27 19:30", "date_created": "2020-07-27 17:05", "status": "FEITA(O)", "mission_type": "Fechado Para Empresa", "mission_category": "Tecnologia", "group_name": "GrupoTeste", "duration(sec)": "None", "duration(h)": "None", "enrollments": "9"}, {"mission_id": "b0a4cbde-458a-4c58-a1e1-94b095f53862", "mission_name": "Como vender mais pelo whatsapp?", "user_creator": "Keeps Admin", "date_updated": "2020-08-11 17:25", "date_created": "2020-04-21 18:55", "status": "FEITA(O)", "mission_type": "Aberto Para Empresa", "mission_category": "<PERSON><PERSON><PERSON>", "group_name": "None", "duration(sec)": "1987.0", "duration(h)": "0.55", "enrollments": "6"}, {"mission_id": "f335802c-f67c-47da-b93d-0e30a701ae28", "mission_name": "Superdicas para falar bem", "user_creator": "Gabriel Keeps", "date_updated": "2020-06-09 15:38", "date_created": "2020-06-05 19:35", "status": "FEITA(O)", "mission_type": "Aberto Para Empresa", "mission_category": "Desenvolvimento pessoal", "group_name": "None", "duration(sec)": "6464.0", "duration(h)": "1.80", "enrollments": "None"}, {"mission_id": "b17834db-e3fd-429e-843c-13711c07134d", "mission_name": "Konquest na prática", "user_creator": "<PERSON>", "date_updated": "2020-07-27 19:30", "date_created": "2020-07-27 17:05", "status": "FEITA(O)", "mission_type": "Fechado Para Empresa", "mission_category": "Tecnologia", "group_name": "Usuarios", "duration(sec)": "None", "duration(h)": "None", "enrollments": "9"}, {"mission_id": "b17834db-e3fd-429e-843c-13711c07134d", "mission_name": "Konquest na prática", "user_creator": "<PERSON>", "date_updated": "2020-07-27 19:30", "date_created": "2020-07-27 17:05", "status": "FEITA(O)", "mission_type": "Fechado Para Empresa", "mission_category": "Tecnologia", "group_name": "Keepers", "duration(sec)": "None", "duration(h)": "None", "enrollments": "9"}, {"mission_id": "2e4bd790-024e-407a-9d31-40771c59bd45", "mission_name": "Como criar um estúdio de gravação em casa", "user_creator": "Keeps Admin", "date_updated": "2020-11-11 11:51", "date_created": "2020-05-21 22:02", "status": "FEITA(O)", "mission_type": "Aberto Para Empresa", "mission_category": "Comunicações", "group_name": "None", "duration(sec)": "960.0", "duration(h)": "0.27", "enrollments": "2"}, {"mission_id": "b17834db-e3fd-429e-843c-13711c07134d", "mission_name": "Konquest na prática", "user_creator": "<PERSON>", "date_updated": "2020-07-27 19:30", "date_created": "2020-07-27 17:05", "status": "FEITA(O)", "mission_type": "Fechado Para Empresa", "mission_category": "Tecnologia", "group_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "duration(sec)": "None", "duration(h)": "None", "enrollments": "9"}, {"mission_id": "9543c838-e93c-4955-a185-106796c4eaa8", "mission_name": "<PERSON><PERSON><PERSON>", "user_creator": "Keeps Admin", "date_updated": "2020-08-05 12:17", "date_created": "2019-08-20 11:26", "status": "FEITA(O)", "mission_type": "Fechado Para Empresa", "mission_category": "Management", "group_name": "None", "duration(sec)": "6919.0", "duration(h)": "1.92", "enrollments": "1"}], "konquest_company_missions-enrollments": [{"mission_id": "b0a4cbde-458a-4c58-a1e1-94b095f53862", "enrollments_concluídas": "1"}, {"mission_id": "4cfcf6ee-e33e-4329-90c1-72e2389accc0", "enrollments_concluídas": "1"}, {"mission_id": "5ef34e4d-dbb0-4b99-b14c-bd227a008b90", "enrollments_concluídas": "9"}, {"mission_id": "5d9d27c1-5999-404f-ae43-47a2bc3af8a4", "enrollments_concluídas": "1"}, {"mission_id": "8dbc4f4b-1724-4df9-b77a-efaa98e860f9", "enrollments_concluídas": "2"}, {"mission_id": "665cccc1-ca81-485c-90f6-b31f64839631", "enrollments_concluídas": "2"}, {"mission_id": "0608a187-81d8-4152-b8a8-4837d31c6e87", "enrollments_concluídas": "1"}], "konquest_company_overview": [{"template": "konquest_overview_cover", "dataset": {"data": "26/11/2020"}, "params": {"company_name": "DEMONSTRAÇÃO"}}, {"template": "konquest_overview_general_information", "dataset": {"count_missions_created": 22, "count_pulses_created": 22, "count_total_users": 15, "count_hours_duration_missions": 16.7675, "count_hours_duration_pulses": 4, "count_users_active": 8}, "params": {}}, {"template": "konquest_overview_users_records", "dataset": {"total_users_registered": 15, "count_users_active_week": 2, "count_users_finished_mission_week": 0, "count_users_finished_pulse_week": 2, "percentage_growth_week": "0%", "arrow_image_week": "https://s3.amazonaws.com/keeps.reports/assets/icon_arrow_down.png", "count_users_active_month": 6, "count_users_finished_mission_month": 1, "count_users_finished_pulse_month": 3, "percentage_growth_month": "50%", "arrow_image_month": "https://s3.amazonaws.com/keeps.reports/assets/icon_arrow_up.png", "count_users_absent": 7}, "params": {}}, {"template": "konquest_overview_enrollments_and_users", "dataset": {"count_unique_users": 8, "count_enrollments_finished": 7, "count_enrollments_in_progress": 9, "count_total_enrollments_finished_week": 0, "count_distinct_enrollments_finished_week": 0, "percentage_enrollments_finished_growth_week": "0%", "enrollments_finished_week_arrow_image": "https://s3.amazonaws.com/keeps.reports/assets/icon_arrow_down.png", "count_total_enrollments_finished_month": 1, "count_distinct_enrollments_finished_month": 1, "percentage_enrollments_finished_growth_month": "0%", "enrollments_finished_month_arrow_image": "https://s3.amazonaws.com/keeps.reports/assets/icon_arrow_down.png", "count_total_enrollments_in_progress_week": 0, "count_distinct_enrollments_in_progress_week": 0, "percentage_enrollments_in_progress_growth_week": "0%", "enrollments_in_progress_week_arrow_image": "https://s3.amazonaws.com/keeps.reports/assets/icon_arrow_down.png", "count_total_enrollments_in_progress_month": 2, "count_distinct_enrollments_in_progress_month": 2, "percentage_enrollments_in_progress_growth_month": "0%", "enrollments_in_progress_month_arrow_image": "https://s3.amazonaws.com/keeps.reports/assets/icon_arrow_down.png"}, "params": {}}, {"template": "konquest_overview_enrollments_performance_analysis", "dataset": {"count_enrollments_performance_0_10": 6, "count_enrollments_performance_10_30": 0, "count_enrollments_performance_30_50": 0, "count_enrollments_performance_50_75": 0, "count_enrollments_performance_75_100": 0}, "params": {}}, {"template": "konquest_overview_contents_type_in_missions", "dataset": {"count_video": 21, "count_podcast": 5, "count_doc": 1, "count_spreadsheet": 0, "count_pdf": 15, "count_image": 4, "count_presentation": 2, "count_quiz": 0}, "params": {}}, {"template": "konquest_overview_pulses_analysis", "dataset": {"count_total_pulses": 22, "count_total_consume": 312, "count_pulses_created_week": 2, "count_pulses_created_month": 3, "count_consume_week": 1, "count_consume_month": 2, "pulses_created_week_arrow_image": "https://s3.amazonaws.com/keeps.reports/assets/icon_arrow_down.png", "percentage_pulses_created_week": "0%", "pulses_created_month_arrow_image": "https://s3.amazonaws.com/keeps.reports/assets/icon_arrow_down.png", "percentage_pulses_created_month": "0%", "consume_week_arrow_image": "https://s3.amazonaws.com/keeps.reports/assets/icon_arrow_down.png", "percentage_consume_week": "0%", "consume_month_arrow_image": "https://s3.amazonaws.com/keeps.reports/assets/icon_arrow_down.png", "percentage_consume_month": "-75%", "popular_channel": "<PERSON><PERSON><PERSON>"}, "params": {}}, {"template": "konquest_overview_consume_pulses_analysis", "dataset": {"percentage_consume_video": "41%", "percentage_consume_podcast": "24%", "percentage_consume_doc": "0%", "percentage_consume_spreadsheet": "0%", "percentage_consume_pdf": "29%", "percentage_consume_image": "2%", "percentage_consume_presentation": "0%", "percentage_consume_quiz": "0%"}, "params": {}}, {"template": "konquest_overview_contents_type_in_pulses", "dataset": {"count_video": 8, "count_podcast": 2, "count_doc": 0, "count_spreadsheet": 0, "count_pdf": 4, "count_image": 2, "count_presentation": 0, "count_quiz": 0}, "params": {}}, {"template": "konquest_overview_ranking_user_performance", "dataset": [{"user_name": "CURIOSO", "performance_percentage": "5%", "points": 4.0}, {"user_name": "GISELE", "performance_percentage": "4%", "points": 40.0}, {"user_name": "GUSTAVO", "performance_percentage": "1%", "points": 2.0}, {"user_name": "CURIOSO", "performance_percentage": "0%", "points": 0.0}, {"user_name": "CURIOSO", "performance_percentage": "0%", "points": 0.0}, {"user_name": "CURIOSO", "performance_percentage": "0%", "points": 0.0}], "params": {}}, {"template": "konquest_overview_groups_by_mission_general", "dataset": [{"group_name": "Liderança", "count_missions_by_group": 2, "count_users_by_group": 4}, {"group_name": "Book 1", "count_missions_by_group": 1, "count_users_by_group": 0}], "params": {}}, {"template": "konquest_overview_groups_by_mission_detailed", "dataset": [{"group_name": "Liderança", "count_missions_by_group": 2, "count_user_enrollments_finished": "0 (0%)", "count_user_enrollments_in_progress": "1 (12%)", "count_user_enrollments_not_started": "7 (88%)"}], "params": {}}, {"template": "thank_report", "dataset": {"data": "26/11/2020"}, "params": {"generate_date": "26/11/2020"}}], "konquest_course_overview": [{"template": "konquest_mission_cover", "dataset": {"cover": {"mission_name": "Fertilizantes"}}, "params": {"mission_name": "Fertilizantes"}}, {"template": "konquest_mission_general_information", "dataset": {"count_video": 2, "count_podcast": 0, "count_docs": 0, "count_spreadsheet": 0, "count_pdf": 0, "count_image": 0, "count_presentation": 0, "count_quiz": 0, "mission_created_user": "THIAGO DE BARROS SYLVESTRE (173343)", "user_creator_image": "https://s3.amazonaws.com/keeps.myaccount.media.prd/user-avatar/dd345dc8-5795-46d0-b504-9bca8e99ad13-1.jpg", "mission_created_date": "04/04/2020", "mission_duration_time": "00:41", "mission_points": "82", "mission_rating": 0}, "params": {}}, {"template": "konquest_mission_enrollments_analysis", "dataset": {"enrollments_conclusion_rate": "0%", "count_enrollments_finished": 0, "count_enrollments_in_progress": 3, "count_enrollments_finished_week": 0, "count_enrollments_finished_month": 0, "count_enrollments_started_week": 0, "count_enrollments_started_month": 1}, "params": {"mission_name": "Fertilizantes"}}, {"template": "konquest_mission_enrollments_performance_analysis", "dataset": {"count_enrollments_performance_0_10": 0, "count_enrollments_performance_10_30": 0, "count_enrollments_performance_30_50": 0, "count_enrollments_performance_50_75": 0, "count_enrollments_performance_75_100": 0}, "params": {"mission_name": "Fertilizantes"}}, {"template": "konquest_mission_quizzes_performance_analysis", "dataset": {"count_quizzes_performance_0_10": 0, "count_quizzes_performance_10_30": 0, "count_quizzes_performance_30_50": 0, "count_quizzes_performance_50_75": 1, "count_quizzes_performance_75_100": 1}, "params": {"mission_name": "Fertilizantes"}}, {"template": "konquest_mission_quizzes_analysis", "dataset": [{"quiz_name": "Módulo 14 - <PERSON>rt<PERSON><PERSON><PERSON> (Matérias-primas)", "count_total_questions": 5, "count_total_answers": 2, "count_total_hits": 2, "hits_percentage": "100%"}, {"quiz_name": "Módulo 15 - Fert<PERSON>zantes (Matérias-primas)", "count_total_questions": 5, "count_total_answers": 2, "count_total_hits": 2, "hits_percentage": "100%"}], "params": {"mission_name": "Fertilizantes"}}, {"template": "konquest_mission_ranking_user_performance", "dataset": [], "params": {"mission_name": "Fertilizantes"}}, {"template": "konquest_mission_ranking_user_quizzes_performance", "dataset": [{"user_name": "FERNANDO DUBOU HANSEL (201071)", "count_errors": 0, "count_hits": 10, "total_questions": 10, "performance_quiz": "100%"}, {"user_name": "FLAVIO BONINI", "count_errors": 5, "count_hits": 5, "total_questions": 10, "performance_quiz": "50%"}], "params": {"mission_name": "Fertilizantes"}}, {"template": "konquest_mission_groups_by_mission", "dataset": [{"group_name": "admin", "count_users_group": 2, "count_user_enrollments_finished": 0, "count_user_enrollments_in_progress": 1, "count_user_enrollments_not_started": 1}], "params": {"mission_name": "Fertilizantes"}}, {"template": "thank_report", "dataset": {"data": "26/11/2020"}, "params": {"generate_date": "26/11/2020"}}], "konquest_user_overview": [{"template": "konquest_user_mission", "sub_template": "subreport_konquest_user_mission", "dataset": {"conclusion_rate": "100%", "total_enrollments": 1, "enrollments_finished": 1, "mission_list": [{"mission_name": "<PERSON><PERSON><PERSON>", "mission_performance": "4%", "mission_quiz_points": 10}, {"mission_name": "<PERSON><PERSON><PERSON>", "mission_performance": "4%", "mission_quiz_points": 10}, {"mission_name": "<PERSON><PERSON><PERSON>", "mission_performance": "4%", "mission_quiz_points": 10}], "user_name": "<PERSON><PERSON><PERSON>", "user_created_date": "14/08/2019", "user_last_access_date": "13/08/2020", "user_avatar": "https://s3.amazonaws.com/keeps.reports/assets/mascote_icon.png", "profile_rate_red": "0%"}, "params": {}}, {"template": "konquest_user_pulse", "sub_template": "subreport_konquest_user_pulse", "dataset": {"total_pulse_consume": 10, "total_pulse_created": 8, "pulse_list": [{"pulse_name": "Conceitos e exemplos", "consume_time": 1, "pulse_time": 12}, {"pulse_name": "Vamos nos desafiar?", "consume_time": 1, "pulse_time": 1}, {"pulse_name": "Contribuição da liderança situacional nas organizações", "consume_time": 1, "pulse_time": 94}, {"pulse_name": "A origem da palavra", "consume_time": 180, "pulse_time": 8}, {"pulse_name": "20 coisas para não se fazer no feedback", "consume_time": 192, "pulse_time": 17}, {"pulse_name": "Explicando a metodologia", "consume_time": 1, "pulse_time": 9}, {"pulse_name": "O que é a liderança situacional?", "consume_time": 3, "pulse_time": 5}, {"pulse_name": "A cultura do feedback", "consume_time": 1, "pulse_time": 1}, {"pulse_name": "Você sabe o que é feedback?", "consume_time": 1, "pulse_time": 3}, {"pulse_name": "Exemplos lúdicos de liderança situacional", "consume_time": 1, "pulse_time": 7}], "user_name": "<PERSON><PERSON><PERSON>", "user_created_date": "14/08/2019", "user_last_access_date": "13/08/2020", "user_avatar": "https://s3.amazonaws.com/keeps.reports/assets/mascote_icon.png", "profile_rate_red": "0%"}, "params": {}}, {"template": "konquest_user_group", "sub_template": "subreport_konquest_user_group", "dataset": {"total_groups": 0, "total_missions_in_groups": 0, "total_enrollments_in_groups": "0%", "group_list": [{"group_name": "VINCULADO A NENHUM GRUPO", "total_mission": 0, "enrollments_completed": 0, "enrollments_in_progress": 0, "enrollments_not_started": 0}], "user_name": "<PERSON><PERSON><PERSON>", "user_created_date": "14/08/2019", "user_last_access_date": "13/08/2020", "user_avatar": "https://s3.amazonaws.com/keeps.reports/assets/mascote_icon.png", "profile_rate_red": "0%"}, "params": {}}], "smartzap_course_overview": [{"template": "smartzap_cover", "dataset": {"course_name": {"course_name": "Engajamento"}}, "params": {"course_name": "Engajamento"}}, {"template": "smartzap_enrollment", "dataset": {"total_enrollments": 71, "enrollments_active": 53, "enrollments_finished": 42, "enrollments_waiting": 15, "enrollments_refused": 2, "errors_enrollments": 1}, "params": {}}, {"template": "smartzap_performance", "dataset": {"num_performance_0": 10, "num_performance_10": 1, "num_performance_10-30": 5, "num_performance_30-50": 0, "num_performance_50-70": 5, "num_performance_70-100": 19, "total_enrollments": 42}, "params": {}}, {"template": "smartzap_consume_content", "dataset": [{"label": "Engajamento: O que é e por onde começar?", "totalUsers": 25, "totalHours": 1}, {"label": "Maneiras de engajar os colaboradores", "totalUsers": 22, "totalHours": 0}, {"label": "O que é engajamento?", "totalUsers": 34, "totalHours": 1}, {"label": "Qual o papel do Gestor frente ao engajamento da equipe?", "totalUsers": 21, "totalHours": 1}], "params": {}}, {"template": "smartzap_consume_quiz", "dataset": [{"quiz_name": "Vamos ver se você entendeu a essência do engajamento.", "num_users_accessed": 28, "num_users_finished": 26}], "params": {}}, {"template": "smartzap_ranking_users_performance", "dataset": [{"user_phone": "5547984517144", "user_name": "GABRIELA PEDROTTI", "user_tags": "Tutoria", "user_performance": "100.00%", "user_points": "16.64"}, {"user_phone": "5547996780922", "user_name": "RICARDO GRIMA FERNANDES", "user_tags": "Comercial", "user_performance": "100.00%", "user_points": "12.65"}, {"user_phone": "5547991088470", "user_name": "SIMONE REGINA FRUET", "user_tags": "CSC", "user_performance": "100.00%", "user_points": "21.46"}, {"user_phone": "5547991497020", "user_name": "<PERSON>", "user_tags": "Operações_Polo", "user_performance": "100.00%", "user_points": "21.54"}, {"user_phone": "5547988223042", "user_name": "CAROLINE HALMENSCHLAGER THOME", "user_tags": "CSC_Acadêmico", "user_performance": "100.00%", "user_points": "21.50"}, {"user_phone": "5511997157271", "user_name": "ANDERSON ORPHALI FLORES", "user_tags": "G&G", "user_performance": "100.00%", "user_points": "21.55"}, {"user_phone": "5547997154750", "user_name": "SUSAN HOEPERS SCHULTZ", "user_tags": "Articuladores", "user_performance": "100.00%", "user_points": "20.18"}, {"user_phone": "5547988548777", "user_name": "THALINE CAVILHA", "user_tags": "Comercial", "user_performance": "100.00%", "user_points": "21.25"}, {"user_phone": "5547988299326", "user_name": "CRISTIANE LISANDRA DANNA", "user_tags": "Operações_Polo", "user_performance": "100.00%", "user_points": "21.53"}, {"user_phone": "5547999110698", "user_name": "TATIANA MILANI ODORIZZI", "user_tags": "Produção_Material", "user_performance": "95.00%", "user_points": "18.54"}], "params": {}}, {"template": "smartzap_ranking_users_quiz_performance", "dataset": [{"user_name": "GABRIELA PEDROTTI", "tags": "Tutoria", "num_error": 0, "num_hit": 1, "total_question": 1, "performance": "100.00%"}, {"user_name": "RICARDO GRIMA FERNANDES", "tags": "Comercial", "num_error": 0, "num_hit": 1, "total_question": 1, "performance": "100.00%"}, {"user_name": "NORBERTO SIEGEL", "tags": "Novos_Negócios", "num_error": 0, "num_hit": 1, "total_question": 1, "performance": "100.00%"}, {"user_name": "MARIA CAROLINA DE FREITAS GONÇALVES", "tags": "", "num_error": 0, "num_hit": 1, "total_question": 1, "performance": "100.00%"}, {"user_name": "FELIPE CAMPOS DE BRITO", "tags": "CSC", "num_error": 0, "num_hit": 1, "total_question": 1, "performance": "100.00%"}, {"user_name": "SIMONE REGINA FRUET", "tags": "CSC", "num_error": 0, "num_hit": 1, "total_question": 1, "performance": "100.00%"}, {"user_name": "JESSICA MANES", "tags": "CSC", "num_error": 0, "num_hit": 1, "total_question": 1, "performance": "100.00%"}, {"user_name": "CLOVES MACHADO", "tags": "Relacionamento", "num_error": 0, "num_hit": 1, "total_question": 1, "performance": "100.00%"}, {"user_name": "BEATRIZ ESTELA DE SENA", "tags": "G&G", "num_error": 0, "num_hit": 1, "total_question": 1, "performance": "100.00%"}, {"user_name": "<PERSON>", "tags": "Operações_Polo", "num_error": 0, "num_hit": 1, "total_question": 1, "performance": "100.00%"}, {"user_name": "ALEXANDRE GARCEZ", "tags": "Ativos", "num_error": 0, "num_hit": 1, "total_question": 1, "performance": "100.00%"}, {"user_name": "TIAGO TOLAINE MARQUES POVOA", "tags": "CSC", "num_error": 0, "num_hit": 1, "total_question": 1, "performance": "100.00%"}, {"user_name": "Rangel", "tags": "", "num_error": 0, "num_hit": 1, "total_question": 1, "performance": "100.00%"}, {"user_name": "BRUNO SANTA MARIA", "tags": "", "num_error": 0, "num_hit": 1, "total_question": 1, "performance": "100.00%"}, {"user_name": "CRISTIANE LISANDRA DANNA", "tags": "Operações_Polo", "num_error": 0, "num_hit": 1, "total_question": 1, "performance": "100.00%"}], "params": {}}, {"template": "thank_report", "dataset": {"data": "27/11/2020"}, "params": {"generate_date": "27/11/2020"}}]}