import sys
import traceback
from typing import List, Optional

import psutil
from config.default import Config
from discord_webhook import DiscordEmbed, DiscordWebhook
from requests import Timeout

EMBED_FIELD_MAX_VALUE = 1024
HALF_EMBED_FIELD_MAX_VALUE = int(EMBED_FIELD_MAX_VALUE / 2)
TITLE_MAX_CHARACTERS = 256
DESCRIPTION_MAX_CHARACTERS = 4096
RED_COLOR = "ff0000"
WARNING_COLOR = "ffcc00"


class DiscordWebhookLogger:  # pragma: no cover
    _instance = None

    def __new__(cls):
        if not cls._instance:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(
        self,
        url: str = Config.DISCORD_WEBHOOK,
    ):
        self.url = url

    def emit_short_message(self, identifier: str, error: BaseException):
        if Config.DEBUG or "test" in sys.argv:
            return
        description = f"{error}\n{traceback.format_exc()}"
        if len(description) > EMBED_FIELD_MAX_VALUE:
            description = description[:HALF_EMBED_FIELD_MAX_VALUE] + ' | ' + description[-HALF_EMBED_FIELD_MAX_VALUE:]
        main_embed = DiscordEmbed(title=identifier[:TITLE_MAX_CHARACTERS], description=description)
        self.execute(main_embed)

    def execute(
        self,
        embed: DiscordEmbed,
        detail_embeds: Optional[List[DiscordEmbed]] = None,
        footer_text: Optional[str] = None
    ):
        embed.set_footer(text=footer_text)
        embed.set_timestamp()
        embed.add_embed_field(name="RAM memory used", value=f"{psutil.virtual_memory()[2]}%", inline=False)
        webhook = DiscordWebhook(url=self.url)
        webhook.add_embed(embed)
        if detail_embeds:
            detail_embed = detail_embeds[0]
            detail_embed.set_color(embed.color)
            webhook.add_embed(detail_embed)
        try:
            webhook.execute()
        except Timeout as error:
            raise RuntimeError(f"Connection to Discord timed out: {error}")
