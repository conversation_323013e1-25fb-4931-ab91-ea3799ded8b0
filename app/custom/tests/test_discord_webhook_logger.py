from mock import patch
from custom.discord_webhook_logger import DiscordWebhookLogger
import unittest
from config.default import Config

class DiscordWebhookLoggerTest(unittest.TestCase):

    @patch('custom.discord_webhook_logger.DiscordWebhookLogger.emit_short_message')
    def test_emit_short_message(self, mock_emit_short_message):
        logger = DiscordWebhookLogger()
        logger.emit_short_message('DiscordWebhookLogger Test Identifier: ', 'DiscordWebhookLogger Test Error')
        self.assertTrue(mock_emit_short_message.called)