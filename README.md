[![Quality Gate Status](https://sonar.keepsdev.com/api/project_badges/measure?project=Keeps-Learn_learn-analytics-server_609a5bfd-a0c7-45d9-8cc1-6c5bca121492&metric=alert_status&token=d03f4746aefa08a80511d17de64c4f5fa634e815)](https://sonar.keepsdev.com/dashboard?id=Keeps-Learn_learn-analytics-server_609a5bfd-a0c7-45d9-8cc1-6c5bca121492)
[![Coverage](https://sonar.keepsdev.com/api/project_badges/measure?project=Keeps-Learn_learn-analytics-server_609a5bfd-a0c7-45d9-8cc1-6c5bca121492&metric=coverage&token=d03f4746aefa08a80511d17de64c4f5fa634e815)](https://sonar.keepsdev.com/dashboard?id=Keeps-Learn_learn-analytics-server_609a5bfd-a0c7-45d9-8cc1-6c5bca121492)
[![Maintainability Rating](https://sonar.keepsdev.com/api/project_badges/measure?project=Keeps-Learn_learn-analytics-server_609a5bfd-a0c7-45d9-8cc1-6c5bca121492&metric=sqale_rating&token=d03f4746aefa08a80511d17de64c4f5fa634e815)](https://sonar.keepsdev.com/dashboard?id=Keeps-Learn_learn-analytics-server_609a5bfd-a0c7-45d9-8cc1-6c5bca121492)
[![Security Rating](https://sonar.keepsdev.com/api/project_badges/measure?project=Keeps-Learn_learn-analytics-server_609a5bfd-a0c7-45d9-8cc1-6c5bca121492&metric=security_rating&token=d03f4746aefa08a80511d17de64c4f5fa634e815)](https://sonar.keepsdev.com/dashboard?id=Keeps-Learn_learn-analytics-server_609a5bfd-a0c7-45d9-8cc1-6c5bca121492)
[![Reliability Rating](https://sonar.keepsdev.com/api/project_badges/measure?project=Keeps-Learn_learn-analytics-server_609a5bfd-a0c7-45d9-8cc1-6c5bca121492&metric=reliability_rating&token=d03f4746aefa08a80511d17de64c4f5fa634e815)](https://sonar.keepsdev.com/dashboard?id=Keeps-Learn_learn-analytics-server_609a5bfd-a0c7-45d9-8cc1-6c5bca121492)
[![Vulnerabilities](https://sonar.keepsdev.com/api/project_badges/measure?project=Keeps-Learn_learn-analytics-server_609a5bfd-a0c7-45d9-8cc1-6c5bca121492&metric=vulnerabilities&token=d03f4746aefa08a80511d17de64c4f5fa634e815)](https://sonar.keepsdev.com/dashboard?id=Keeps-Learn_learn-analytics-server_609a5bfd-a0c7-45d9-8cc1-6c5bca121492)
[![Bugs](https://sonar.keepsdev.com/api/project_badges/measure?project=Keeps-Learn_learn-analytics-server_609a5bfd-a0c7-45d9-8cc1-6c5bca121492&metric=bugs&token=d03f4746aefa08a80511d17de64c4f5fa634e815)](https://sonar.keepsdev.com/dashboard?id=Keeps-Learn_learn-analytics-server_609a5bfd-a0c7-45d9-8cc1-6c5bca121492)
[![Lines of Code](https://sonar.keepsdev.com/api/project_badges/measure?project=Keeps-Learn_learn-analytics-server_609a5bfd-a0c7-45d9-8cc1-6c5bca121492&metric=ncloc&token=d03f4746aefa08a80511d17de64c4f5fa634e815)](https://sonar.keepsdev.com/dashboard?id=Keeps-Learn_learn-analytics-server_609a5bfd-a0c7-45d9-8cc1-6c5bca121492)
![example branch parameter](https://github.com/Keeps-Learn/learn-analytics-server/actions/workflows/main.yml/badge.svg)

# Learn Analytics Server
Aplicação Flask responsável pela geração de relatórios e exportações. Três formatos de arquivos podem ser obtidos:

- **PDF**: O binário é gerado por outro serviço (Jasper Reports), a partir de um arquivo JSON gerado pelo _Learn Analytics_.
- **XLSX**: Arquivo gerado pelo próprio _Learn Analytics_, através da biblioteca Pandas.
- **CSV**: Arquivo gerado pelo próprio _Learn Analytics_, através da biblioteca Pandas.


## Ambiente de desenvolvimento

### Requisitos
- Python 3.11
- pyenv e pyenv-virtualenv
- PostgreSQL
- ElasticSearch

## Instalação local
Para instalar e executar a aplicação localmente, siga os passos abaixo:

1. Instale as bibliotecas pyenv e pyenv-virtualenv:
```bash
$ git clone https://github.com/pyenv/pyenv.git ~/.pyenv

$ echo 'export PYENV_ROOT="$HOME/.pyenv"' >> ~/.bashrc
$ echo 'export PATH="$PYENV_ROOT/bin:$PATH"' >> ~/.bashrc
$ echo -e 'if command -v pyenv 1>/dev/null 2>&1; then\n eval "$(pyenv init -)"\nfi' >> ~/.bashrc

$ exec "$SHELL"

$ git clone https://github.com/pyenv/pyenv-virtualenv.git $(pyenv root)/plugins/pyenv-virtualenv
$ echo 'eval "$(pyenv virtualenv-init -)"' >> ~/.bashrc
$ exec "$SHELL"
```
2. Baixe o projeto do repositório: `<NAME_EMAIL>:Keeps-Learn/learn-analytics-server.git`<br>
Para clonar o projeto localmente, você precisa adicionar uma chave SSH ao seu perfil no Github.
Caso ainda não tenha feito isso, siga os passos descritos em [Adding a new SSH key to your GitHub account](https://docs.github.com/en/authentication/connecting-to-github-with-ssh/adding-a-new-ssh-key-to-your-github-account)
3. Acesse a pasta `learn-analytics-server` (pasta raíz do projeto)
4. Na pasta raíz, execute `make create-venv` para criar o ambiente virtual e baixar as dependências.
5. Na pasta raíz, crie o arquivo _.env_ contendo as variáveis de ambiente necessárias para rodar a aplicação.</b>
Você pode precisar do plugin EnvFile para trabalhar com o Pycharm.

**Exemplo de arquivo .env**:</b>
``` 
AWS_BASE_CDN_URL=https://media-stage.keepsdev.com
AWS_BASE_S3_URL=https://s3.amazonaws.com
AWS_BUCKET_NAME=keeps-media-stage
AWS_BUCKET_NAME_REPORT=keeps-media-stage
AWS_BUCKET_PATH=analytics
AWS_LAMBDA_ACCESS_KEY_ID=
AWS_LAMBDA_REGION_NAME=us-east-1
AWS_LAMBDA_SECRET_ACCESS_KEY=
AWS_S3_ACCESS_KEY_ID=
AWS_S3_REGION_NAME=us-east-1
AWS_S3_SECRET_ACCESS_KEY=
CELERY_BROKER_URL=amqp://admin:admin@rabbitmq:5672
CELERY_RESULT_BACKEND=rpc://admin:admin@rabbitmq:5672
DATABASE_KONQUEST_URL=postgresql://username:<EMAIL>:5432/konquest_dev_db
DATABASE_KONTENT_URL=postgresql://username:passwordrds-postgres-stage.cpd3dmaosiyq.us-east-1.rds.amazonaws.com:5432/kontent_dev_db
DATABASE_MYACCOUNT_URL=postgresql://username:<EMAIL>:5432/myaccount_dev_db
DATABASE_SMARTZAP_URL=postgresql://username:<EMAIL>:5432/smartzap_dev_db
DATABASE_URL=postgresql://username:<EMAIL>:5432/analytics_dev_db
ELASTIC_APM_ENVIRONMENT=stage
ELASTIC_APM_SECRET_TOKEN=
ELASTIC_APM_SERVER_URL=https://keeps.apm.us-east-1.aws.cloud.es.io
ELASTICSEARCH_AUTH=
ELASTICSEARCH_INDEX_ACTIVITIES=kafka-analytics-activities-stage
ELASTICSEARCH_INDEX_ANSWERS=kafka-analytics-answers-stage
ELASTICSEARCH_INDEX_CHANNELS=kafka-analytics-channels-stage
ELASTICSEARCH_INDEX_COURSE_EVALUATIONS=kafka-analytics-course-evaluations-stage
ELASTICSEARCH_INDEX_COURSE_RATINGS=kafka-analytics-course-ratings-stage
ELASTICSEARCH_INDEX_COURSES=kafka-analytics-courses-stage
ELASTICSEARCH_INDEX_COURSES_V1=learning-analytics-courses-stage
ELASTICSEARCH_INDEX_COURSES_V2=kafka-analytics-courses-stage
ELASTICSEARCH_INDEX_ENROLLMENTS=kafka-analytics-enrollments-stage
ELASTICSEARCH_INDEX_PULSES=kafka-analytics-pulses-stage
ELASTICSEARCH_INDEX_USERS=kafka-analytics-users-stage
ELASTICSEARCH_INDEX_USERS_V1=learning-analytics-users-stage
ELASTICSEARCH_INDEX_USERS_V2=kafka-analytics-users-stage
ELASTICSEARCH_URL=https://keeps.es.us-east-1.aws.found.io
ENVIRONMENT=staging
FLASK_APP=wsgi.py
FLASK_ENV=development
FLASK_DEBUG=1
JASPER_REPORT_SERVER_URL=https://learning-platform-api-stage.keepsdev.com/report-generator
# SLACK_LOG_CHANNEL_WEBHOOK=*******************************************************************************
SLACK_LOG_CHANNEL_WEBHOOK=
WEB_CONCURRENCY=1
WORKER_CONCURRENCY=2
```
Observe que as variáveis abaixo estão sem valor, tratando-se de variáveis de valores sensíveis, como passwords, chaves de acesso, etc.
Para estas variáveis, solicite do seu lĩ́der técnico os valores correspondentes e atualize o seu arquivo _.env._
```
AWS_LAMBDA_ACCESS_KEY_ID=
AWS_LAMBDA_SECRET_ACCESS_KEY=
AWS_S3_ACCESS_KEY_ID=
AWS_S3_SECRET_ACCESS_KEY=

DATABASE_KONQUEST_URL=postgresql://username:<EMAIL>:5432/konquest_dev_db
DATABASE_KONTENT_URL=postgresql://username:passwordrds-postgres-stage.cpd3dmaosiyq.us-east-1.rds.amazonaws.com:5432/kontent_dev_db
DATABASE_MYACCOUNT_URL=postgresql://username:<EMAIL>:5432/myaccount_dev_db
DATABASE_SMARTZAP_URL=postgresql://username:<EMAIL>:5432/smartzap_dev_db
DATABASE_URL=postgresql://username:<EMAIL>:5432/analytics_dev_db

ELASTIC_APM_SECRET_TOKEN=
ELASTICSEARCH_AUTH=
```

## Database migration

#### It's necessary the environment variable: `DATABASE_URL`

New migration:

```
alembic revision -m 'create_tables' --rev-id='001' --autogenerate
```

Run migration to head revision:

```
alembic upgrade head
```

Run migration to previous revision:

```
alembic downgrade -1
```

## Docker Compose
1. Instale o [docker-compose](https://docs.docker.com/compose/install/)
2. Execute os passos de 2, 3 e 5 mencionados na seção **Instalação local** (caso ainda não tenha feito)
3. Execute o comando `docker-compose up --build`

## Visual Studio Code
Para utilizar a IDE Visual Studio Code (VSC) com este projeto:

1. Suba apenas os services `worker` e `rabbitmq` com o docker compose:<br>
`docker-compose up worker rabbitmq`
2. Na pasta raíz do projeto, abra o VSC: `code .`
3. Configure o projeto como uma aplicação Flask, criando o arquivo `launch.json` conforme abaixo:
```
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python Debugger: Flask",
            "type": "debugpy",
            "request": "launch",
            "module": "flask",
            "cwd": "${workspaceFolder}/app",
            "envFile": "${workspaceFolder}/.env",
            "args": [
                "run",
                "--port=8000"
            ],
            "jinja": true
        }
    ]
}
```
