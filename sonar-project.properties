sonar.projectKey=Keeps-Learn_learn-analytics-server_609a5bfd-a0c7-45d9-8cc1-6c5bca121492
sonar.sources=.
sonar.verbose=false
sonar.exclusions=app/Dockerfile, **/*.html, app/wsgi.py, app/gunicorn_config.py, app/domain/common/**, app/scripts/**, app/controller/db_module.py, app/email_locale/**, app/alembic/**,  app/reports/report_test.py, app/controller/api/report.py, app/modules/config_module.py, app/reports/export/__init__.py, app/domain/report/services.py, app/domain/course/**, app/domain/user/**, app/controller/**, docker-compose.yml

sonar.python.version=3
sonar.python.coverage.reportPaths=.reports/coverage/coverage.xml
sonar.python.xunit.reportPath=.reports/xunit/xunit.xml
sonar.python.flake8.reportPaths=.reports/output_flake.txt
sonar.python.pylint.reportPaths=.reports/output_pylint.txt
