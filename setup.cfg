[tool:pytest]
addopts = --tb=auto -color=yes
python_functions = test_* tests_*
python_files = test_*.py tests_*.py
postgresql_port = 8888

[coverage:run]
relative_files = True
source=.
branch = True
omit =
    wsgi.py,
    gunicorn_config.py,
    */domain/common/*,
    ./app/scripts/*,
    ./app/email_locale,
    ./app/reports/report_test.py

[coverage:report]
fail_under = 28

[coverage:html]
directory = .reports/coverage/

[coverage:xml]
output = .reports/coverage/coverage.xml

[flake8]
max-line-length=120
exclude = config, .venv, alembic, ./app/email_locale
max-complexity = 8
# ignore = E501, W503, E203, T001, B009

[pycodestyle]
exclude=config, .venv, venv, alembic
max-line-length=140
ignore = E501
