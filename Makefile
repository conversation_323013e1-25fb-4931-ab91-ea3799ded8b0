.DEFAULT_GOAL := default_target
.PHONY: default_target lint test clean

db := $(DATABASE_URL)
url := $(POSTMAN_URL)
url_security := $(SECURITY_URL)
stress_url := $(STRESS_TEST_URL)

PYTHON_VERSION := 3.11
VENV_NAME := learn-analytics-server-$(PYTHON_VERSION)

# Clean #
.clean-build: ## remove build artifacts
	rm -fr build/
	rm -fr dist/
	rm -fr .eggs/
	find . -name '*.egg-info' -exec rm -fr {} +
	find . -name '*.egg' -exec rm -f {} +

.clean-pyc: ## remove Python file artifacts
	find . -name '*.pyc' -exec rm -f {} +
	find . -name '*.pyo' -exec rm -f {} +
	find . -name '*~' -exec rm -f {} +
	find . -name '__pycache__' -exec rm -fr {} +

.clean-test: ## remove test and coverage artifacts
	rm -fr .tox/
	rm -f .coverage
	rm -fr .reports/
	rm -fr .pytest_cache/

clean: .clean-build .clean-pyc .clean-test ## remove all build, test, coverage and Python artifacts

# Environment #
.create-venv:
	pyenv install -s $(PYTHON_VERSION)
	pyenv uninstall -f $(VENV_NAME)
	pyenv virtualenv $(PYTHON_VERSION) $(VENV_NAME)
	pyenv local $(VENV_NAME)

create-venv: .create-venv setup-dev

.pip:
	pip install pip==25.1.1

setup:
	pip install -r app/requirements.txt

setup-dev: .pip
	pip install -r app/requirements-dev.txt

# Migrations #
.SILENT .PHONY: migration migration-head migration-downgrade migration-downgrade-1 run test code-convention docker-run-local

migration:
	export DATABASE_URL="sqlite:///"$(shell dirname $(realpath $(firstword $(MAKEFILE_LIST))))/storage.db;\
	cd app/;\
	alembic revision -m $(msg) --rev-id=$(rev) --autogenerate;\
	cd ..

migration-head:
	export DATABASE_URL="sqlite:///"$(shell dirname $(realpath $(firstword $(MAKEFILE_LIST))))/storage.db;\
	cd app/;\
	alembic upgrade head;\
	cd ..

migration-downgrade:
	export DATABASE_URL="sqlite:///"$(shell dirname $(realpath $(firstword $(MAKEFILE_LIST))))/storage.db;\
	cd app/;\
	alembic downgrade base;\
	cd ..

migration-downgrade-1:
	export DATABASE_URL="sqlite:///"$(shell dirname $(realpath $(firstword $(MAKEFILE_LIST))))/storage.db;\
	cd app/;\
	alembic downgrade -1;\
	cd ..

# Tests #
test: py.test -v

test-cov:
	export PYTHONPATH=""$(shell dirname $(realpath $(firstword $(MAKEFILE_LIST))))/app;\
	coverage run -m pytest -v --junitxml=.reports/xunit/xunit.xml
	coverage report -m
	coverage xml
	coverage html

code-convention:
	ruff check . -v --format=pylint --output-file=.reports/output_flake.txt || true
# Server #
run_server: python -m application

# Utils #
all: clean create-venv setup-dev test-cov

default_target: clean code-convention test-cov

ruff-code-convention:
	pip install ruff;\
	mkdir -p .reports ;\
	ruff check . --output-format=pylint | tee .reports/output_flake.txt ;\