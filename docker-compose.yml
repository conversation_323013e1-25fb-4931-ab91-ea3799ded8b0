version: "3.11"

services:
  api:
    image: keeps/analytics
    container_name: analytics-api
    build:
      context: ./app
      dockerfile: Dockerfile
    command: gunicorn --reload --access-logfile=- --config gunicorn_config.py wsgi:app --bind 0.0.0.0:8000
    ports:
      - ${API_PORT:-8000}:8000
    volumes:
      - ./app/:/app/
    env_file:
      - ./.env
    environment:
      FLASK_DEBUG: 1
      FLASK_ENV: development
      CELERY_BROKER_URL: ${CELERY_BROKER_URL:-amqp://admin:admin@rabbitmq:5672}
      CELERY_RESULT_BACKEND: ${CELERY_RESULT_BACKEND:-rpc://admin:admin@rabbitmq:5672}
      DATABASE_URL: ${DATABASE_URL:-**************************************/analytics_dev_db}

  worker:
    image: keeps/analytics
    container_name: analytics-worker
    build:
      context: ./app
      dockerfile: Dockerfile
    command: /usr/bin/supervisord -c /etc/supervisord.conf
    volumes:
      - ./app/:/app/
    env_file:
      - ./.env
    environment:
      FLASK_DEBUG: 1
      FLASK_ENV: development
      CELERYD_POOL_RESTARTS: "True" 
      CELERY_BROKER_URL: ${CELERY_BROKER_URL:-amqp://admin:admin@rabbitmq:5672}
      CELERY_RESULT_BACKEND: ${CELERY_RESULT_BACKEND:-rpc://admin:admin@rabbitmq:5672}
      DATABASE_URL: ${DATABASE_URL:-**************************************/analytics_dev_db}
    depends_on:
      - rabbitmq

  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: analytics-queue
    ports:
      - ${RABBITMQ_PORT:-5672}:5672
      - 15672:15672
    env_file:
      - ./.env
    environment:
      - RABBITMQ_DEFAULT_USER=${RABBITMQ_DEFAULT_USER:-admin}
      - RABBITMQ_DEFAULT_PASS=${RABBITMQ_DEFAULT_PASS:-admin}

  db:
    image: postgres:alpine
    container_name: analytics-db
    volumes:
      - ./postgres_data:/var/lib/postgresql/data
    env_file:
      - ./.env
    environment:
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-postgres}
      - POSTGRES_DB=${POSTGRES_DB:-analytics_dev_db}
    ports:
      - ${POSTGRES_PORT:-5432}:5432
