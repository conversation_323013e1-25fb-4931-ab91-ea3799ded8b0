#!/bin/bash

export ELASTICSEARCH_URL=https://keeps.es.us-east-1.aws.found.io:9243 # http://localhost:9200
export ELASTICSEARCH_AUTH=
export ELASTICSEARCH_INDEX_COURSES=kafka-analytics-courses-stage
export ELASTICSEARCH_INDEX_USERS=kafka-analytics-users-stage

### Dev ###
export DATABASE_URL=postgres://username:<EMAIL>/analytics_dev_db
export DATABASE_KONQUEST_URL=postgres://username:<EMAIL>/konquest_dev_db
export DATABASE_KONTENT_URL=postgres://username:<EMAIL>/kontent_dev_db
export DATABASE_MYACCOUNT_URL=postgres://username:<EMAIL>/myaccount_dev_db

export KEYCLOAK_PUBLIC_KEY=
export KEYCLOAK_ISS=https://iam.keepsdev.com/auth/realms/keeps-dev

# Declare empty Slack channel to avoid reporting errors in dev
export SLACK_LOG_CHANNEL_WEBHOOK=

export ELASTIC_APM_SECRET_TOKEN=token

export FLASK_ENV=development
export FLASK_APP=wsgi.py
export PYTHONPATH="${PYTHONPATH}:${PWD}/app/"

flask run --port=8000
